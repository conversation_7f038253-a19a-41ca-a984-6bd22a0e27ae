<template>
  <div class="product_width">
    <a-spin :spinning="loading">
      <!-- 筛选区域 start -->

      <!-- 筛选区域 start -->

      <div class="table-wrapper">
        <a-table
          ref="table"
          :style="`height:${tableHeight}px;`"
          :rowKey="record => record.issueId + record.productChildCate"
          :columns="columns"
          :dataSource="loadData"

        >
					<span slot="delayDays" slot-scope="text, record">
						<div v-if="record.productState == 7 || record.productState == 8" class="select-box">
							<div class="circle" style="background:#afc4d3"></div>
							停止
						</div>
						<div v-else-if="record.delayDays >= 1" class="select-box">
							<div class="circle" style="background:#fd8585"></div>
							逾期
						</div>
						<div v-else class="select-box">
							<div class="circle" style="background:#50a1f8"></div>
							正常
						</div>
					</span>
          <span slot="action" slot-scope="text, record">
						<a :style="{marginRight:'4px'}" v-show="!record.showPlan" @click="showPlan(record)">查看计划</a>
						<a :style="{marginRight:'4px'}" v-show="record.showPlan" @click="showPlan(record)">关闭查看</a>
						<template v-if="record.ganttFileId != 0">
							<span class="a-file" @click="previewPdf(suffx.indexOf(record.ganttFileType) != -1 ? baseUrl+record.ganttFileId : downUrl+record.ganttFileId,record.ganttFileType)">甘特图</span>
						</template>
						<span :style="{color:'transparent'}"  v-else>甘特图</span>
					</span>
        </a-table>
      </div>
    </a-spin>
    <a-drawer :bodyStyle="{ height: '100%' }" width="70%" :closable="false" placement="right"  :visible="drawerVisible" @close="drawerVisible = false">
      <iframe v-if="fileType.indexOf('pdf') !== -1" :src="pdfUrl" width="100%" height="100%"></iframe>
      <img v-else width="100%" height="100%"  :src="pdfUrl" alt="">
    </a-drawer>
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import { getTreeProductsOfNotSplit,exportStagePlan } from "@/api/modular/system/jmChartManage"
import Vue from "vue"
export default {
  components: {
    Treeselect
  },
  data() {
    return {
      pdfUrl:'',
      fileType:'',
      drawerVisible:false,
      drawerImgVisible:false,
      suffx: ["jpg", "jpeg", "png", "gif", "bmp", "svg", "pdf"],
      baseUrl: '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',
      downUrl: '/api/sysFileInfo/download?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=',

      queryparam: {
        cates: [],
        states: [],
        depts:[],
        keyword: null,
      },
      loading: true,
      stageDateField: {
        1 : 'k0PlannedDate',
        2 : 'm1PlannedDate',
        3 : 'traPlannedDate',
        4 : 'dfaPlannedDate',
        5 : 'm2PlannedDate',
        6 : 'trbPlannedDate',
        7 : 'dfbPlannedDate',
        8 : 'm3PlannedDate',
        9 : 'm4PlannedDate',
        10 : 'ppapPlannedDate',
        11 : 'sopPlannedDate',
      },
      columns: [
        {
          title: "序号",
          width: 60,
          dataIndex: "no",
          align: "center",
          customRender: (text, record, index) => {
            if (record.productOrProject == 1 && !record.stage) {
              return `${index + 1}`
            }
            return ''
          },
        },
        {
          title: "产品名称",
          width: 120,
          dataIndex: "productProjectName",
          align: "center",
          ellipsis: true,
        },
        {
          title: "项目名称",
          width: 140,
          dataIndex: "projectName",
          align: "center",
          ellipsis: true,
        },
        {
          title: "项目阶段",
          width: 100,
          dataIndex: "productStageName",
          align: "center",
        },
        {
          title: "项目进度",
          width: 100,
          dataIndex: "delayDays",
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "delayDays" }
        },
        {
          title: '项目获取',
          align: "center",
          children: [
            {
              title: 'K0',
              width: 100,
              dataIndex: 'productPlannedK0',
              align: "center",
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 0 + 1)
              }
            }
          ]
        },
        {
          title: '项目规划',
          align: "center",
          children: [
            {
              title: 'M1阶段评审',
              width: 100,
              align: "center",
              dataIndex: 'productPlannedM1',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 1 + 1)
              }
            }
          ]
        },
        {
          title: '产品开发',
          align: "center",
          children: [
            {
              title: 'TR-A',
              width: 100,
              dataIndex: 'productPlannedM2A',
              align: "center",
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 2 + 1)
              }
            },
            {
              title: 'DF-A',
              width: 100,
              align: "center",
              dataIndex: 'dfaPlannedDate',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 3 + 1)
              }
            },
            {
              title: 'M2阶段评审',
              width: 100,
              dataIndex: 'productPlannedM2z',
              align: "center",
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 4 + 1)
              }
            },
          ]
        },
        {
          title: '过程开发',
          children: [
            {
              title: 'TR-B',
              width: 100,
              align: "center",
              dataIndex: 'productPlannedM3B',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 5 + 1)
              }
            },
            {
              title: 'DF-B',
              width: 100,
              align: "center",
              dataIndex: 'dfbPlannedDate',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 6 + 1)
              }
            },
            {
              title: 'M3阶段评审',
              width: 100,
              dataIndex: 'productPlannedM3z',
              align: "center",
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 7 + 1)
              }
            },
          ]
        },
        {
          title: '制造过程实现',
          align: "center",
          children: [
            {
              title: 'M4阶段评审',
              width: 100,
              align: "center",
              dataIndex: 'productPlannedM4',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 8 + 1)
              }
            }
          ]
        },
        {
          title: '产品和过程放行',
          align: "center",
          children: [
            {
              title: 'PPAP',
              width: 100,
              align: "center",
              dataIndex: 'productPlannedM5',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 9 + 1)
              }
            }
          ]
        },
        {
          title: '量产爬坡',
          align: "center",
          children: [
            {
              title: 'SOP',
              width: 100,
              align: "center",
              dataIndex: 'productPlannedM6',
              customRender: (text, record, index) => {
                return this.customRenderSpan(record, 10 + 1)
              }
            }
          ]
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          dataIndex: 'action',
          scopedSlots: {
            customRender: "action"
          },
        },
      ],
      loadData: [],
      totalData: [],
    }
  },
  props: {
    // 图表高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },
  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "62px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    flattenTree(tree) {
      return tree.reduce((acc, node) => {
        acc.push(node.id);
        if (node.children && node.children.length > 0) {
          acc.push(...this.flattenTree(node.children));
        }
        return acc;
      }, []);
    },
    downloadStagePlan(){
      let _params = {
        projectIds: this.flattenTree(this.loadData) //this.loadData.map(item=>item.issueId)
      }
      exportStagePlan(_params).then(res => {
        const fileName = `产品里程碑.xlsx`;
        const _res = res.data;
        let blob = new Blob([_res]);
        let downloadElement = document.createElement("a");
        //创建下载的链接
        let href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        //下载后文件名
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        //点击下载
        downloadElement.click();
        //下载完成移除元素
        document.body.removeChild(downloadElement);
        //释放掉blob对象
        window.URL.revokeObjectURL(href);
      })
    },
    previewPdf(fileUrl,fileType) {
      this.pdfUrl = fileUrl
      this.fileType = fileType

      if(['xbm','tif','pjp','svgz','jpg','jpeg','ico','tiff','gif','svg','jfif','webp','png','bmp','pjpeg','avif','pdf'].some(someItem => { return typeof fileType === 'string' && someItem.includes(fileType)})){
        this.drawerVisible = true
      }else{
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = fileUrl
        a.download ='甘特图'
        a.click()
      }
    },
    showPlan(record) {
      record.showPlan = !record.showPlan
    },
    customRenderSpan(record, num) {
      const overDays = record.delayDays
      // 当前阶段 可能尚未创建转阶段任务
      if (num == record.mstatus) {

        const reviewDate = record[this.stageDateField[num]] || '--'

        if (record.state == 3) {
          return (
            <span class={"line green2gray"} >
								<a-tooltip placement="bottom" overlayClassName='tooltipGrayColor'>
									<template slot="title">
										<span>
											项目于
                      {record.stopTime}
                      停止
										</span>
									</template>
									<span class="line radius_first radius_last deep-gray">
										{record.stopTime}
									</span>
								</a-tooltip>
							</span>
          )
        }

        if (num == 1) {
          if (overDays >= 1) {
            return (
              <span
                class={"line radius_first reds"}
              >
								<a-tooltip  placement="bottom" overlayClassName={ 'tooltipRedColor' } >
									<template slot="title">
										<span>{ '已逾期' + overDays  + '天'}</span>
									</template>
									<span
                    class={"line radius_first radius_last reds"}
                  >
										{reviewDate}
									</span>
								</a-tooltip>
							</span>
            )
          }else{

            return (
              <span class={"line radius_first"}>
								<span class={ "line radius_first radius_last"}>
									{reviewDate}
								</span>
							</span>
            )

          }
        }

        if (overDays >= 1) {
          return (
            <span class={"line green2gray"}>
								<a-tooltip placement="bottom" overlayClassName={ 'tooltipRedColor' }>
										<template slot="title">
											<span>{ '已逾期' + overDays + '天' }</span>
										</template>
										<span
                      class={"line radius_first radius_last reds"}
                    >
											{reviewDate}
										</span>
								</a-tooltip>
							</span>
          )
        }else{

          return (
            <span class={"line green2gray"}>
							<span
                class={
                  reviewDate == "--"
                    ? "line radius_first radius_last"
                    : "line radius_first radius_last"
                }
              >
								{reviewDate}
							</span>
						</span>
          )
        }

      }

      // 已结束阶段 必然存在转阶段子任务
      if (num < record.mstatus) {

        const stage = record.stages.find(item=>item.stage == num)
        const reviewDate = stage?.reviewDate || (record[this.stageDateField[num]] || '--')

        if (num == 1) {
          return (
            <span class="line green radius_first">{record.showPlan ? reviewDate : ""}</span>
          )
        }
        if (num == 11) {

          return (
            <span class="line green radius_last">{record.showPlan ? reviewDate : ""}</span>
          )
        }

        return (
          <span class="line green">{record.showPlan ? reviewDate : ""}</span>
        )
      }

      // 未开始阶段 不存在转阶段子任务
      if (num > record.mstatus) {

        const reviewDate = record[this.stageDateField[num]] || '--'

        if (num == 1) {
          return (
            <span class="line gray radius_first">{record.showPlan ? reviewDate : ""}</span>
          )
        }
        if (num == 11) {
          return (
            <span class="line gray radius_last">{record.showPlan ? reviewDate : ""}</span>
          )
        }
        return (
          <span class="line gray">{record.showPlan ? reviewDate : ""}</span>
        )
      }
    },
    change() {
      this.callFilter()
    },

    //数据筛选
    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

      // 产品分类
      if (this.queryparam["cates"].length > 0) {
        filterData = filterData.filter(
          item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
        )
        if (this.queryparam["cates"].indexOf(2) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["depts"].length > 0) {
        console.log(this.queryparam["depts"])
        filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
      }

      // 产品名称
      if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
        const temList = []
        this.searchParam.inputSearch.forEach(v => {
          if (v.keyword === "") return
          filterData.forEach(e => {
            if (e.productProjectName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
              temList.push(e)
            }
          })
        })
        filterData = _.uniqWith(temList, _.isEqual)
      }

      if (this.queryparam.startDate != null) {
        filterData = filterData.filter(
          item =>
            Date.parse(item.initiationDate) >= this.queryparam.startDate &&
            Date.parse(item.initiationDate) < this.queryparam.endDate
        )
      }

      // 表格数据
      this.loadData = filterData
    },
    dateChange(date, dateString) {
      if (dateString[0] != null && dateString[0] != "") {
        this.queryparam.startDate = Date.parse(dateString[0])
      } else {
        this.queryparam.startDate = null
      }
      if (dateString[1] != null && dateString[1] != "") {
        this.queryparam.endDate = Date.parse(dateString[1])
      } else {
        this.queryparam.endDate = null
      }
      this.callFilter()
    },
    getTreeProducts() {
      let that = this
      that.loading = true
      getTreeProductsOfNotSplit({})
        .then(res => {

          if (res.success) {

            for(let item of res.data){
              item.showPlan = false
              if(item.children && item.children.length > 0){
                item.children.forEach(e=>{
                  e.showPlan = false
                })
              }
            }

            this.totalData = JSON.parse(JSON.stringify(res.data))

            this.callFilter()
          }
          res?.success
          || that.$message.error(res?.message, 1)
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
  created() {
    this.getTreeProducts()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  }
}
</script>

<style lang="less" scoped="">
@import "./productoption.less";

.select-box {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}
.select-box .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

:root {
  --height: 600px;
}
.a-file{
  color: #1890ff;
  cursor: pointer;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}

/deep/.ant-table-thead > tr > th {
  border-top: 1px solid #dfdbdb;
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
}
.line {
  display: flex;
  width: 100%;
  height: 13px;
  background: #fff;
  align-items: center;
  justify-content: center;
}
.gray {
  background: #dfdfdf;
}

.deep-gray {
  background: #a5a5a5;
}
.green {
  background: #c4ebad;
}
.yellows {
  background: #f7e771;
}
.reds {
  background: #ff7f7f;
}
.radius_first {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}
.radius_last {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
.white2gray {
  background: linear-gradient(to right, #fff 0%, #fff 50%, #dfdfdf 50.01%, #dfdfdf 100%);
}
.green2gray {
  background: linear-gradient(to right, #c4ebad 0%, #c4ebad 50%, #dfdfdf 50.01%, #dfdfdf 100%);
}
.cur_before::before{
  content: "\25B2";
  margin-right: 5px;
  /*color: #009999;*/
  color: transparent;
  font-size: 14px;
}
// 表头居中
/deep/.ant-table-thead tr th {
  border-left: 1px solid #dfdbdb;
  padding: 5px;
}
/deep/.ant-table-thead tr th[key='delayDays']{
  border-right: 1px solid #dfdbdb;
}
/deep/.ant-table-thead tr th:first-child {
  border-left: none;
}
/deep/.ant-table-thead tr th:last-child {
  border-right: none;
}

/deep/.ant-table-tbody tr td {
  padding: 12px 0 !important;
}
.icon-style {
  margin-right: 8px;
}

/* 固定列 */
/deep/ .ant-table tr td {
  background: #fff;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 60px;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(3),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: calc(60px + 120px);
  z-index: 10;
}

/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(4),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(4){
  position: sticky;
  left: calc(60px + 120px + 140px);
  z-index: 10;
}

/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(5),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(5){
  position: sticky;
  left: calc(60px + 120px + 140px + 100px);
  z-index: 10;
}

</style>
<style lang='css'>
.tooltipGrayColor .ant-tooltip-inner,.tooltipGrayColor .ant-tooltip-arrow::before {
  background-color: #a5a5a5 !important;
  color: #000;
}

.tooltipRedColor .ant-tooltip-inner,.tooltipRedColor .ant-tooltip-arrow::before {
  background-color: #ff7f7f !important;
  color: #000;
}

.tooltipYellowColor .ant-tooltip-inner,.tooltipYellowColor .ant-tooltip-arrow::before {
  background-color: #f7e771 !important;
  color: #000;
}

.tooltipGreenColor .ant-tooltip-inner,.tooltipGreenColor .ant-tooltip-arrow::before {
  background-color: #009999 !important;
  color: #000;
}
/* 如果上面不起作用可使用这个试试 */
/* .ant-tooltip-arrow-content {
  background-color: #fff !important;
} */
</style>
