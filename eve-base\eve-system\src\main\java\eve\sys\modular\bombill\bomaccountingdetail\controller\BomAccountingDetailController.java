package eve.sys.modular.bombill.bomaccountingdetail.controller;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomaccountingdetail.service.IBomAccountingDetailService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * BOM核算明细Controller
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@RestController
@RequestMapping("/bomAccountingDetail")
public class BomAccountingDetailController {

    @Resource
    private IBomAccountingDetailService bomAccountingDetailService;

    /**
     * 分页列表查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @PostMapping("/pageList")
    @BusinessLog(title = "BOM核算明细-分页列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData pageList(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.pageList(param));
    }

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return 列表结果
     */
    @PostMapping("/list")
    @BusinessLog(title = "BOM核算明细-列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.list(param));
    }

    /**
     * 根据id查询
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @PostMapping("/get")
    @BusinessLog(title = "BOM核算明细-根据id查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData get(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.get(param));
    }

    /**
     * 新增
     *
     * @param param 新增参数
     * @return 操作结果
     */
    @PostMapping("/add")
    @BusinessLog(title = "BOM核算明细-新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.add(param));
    }

    /**
     * 删除
     *
     * @param param 删除参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    @BusinessLog(title = "BOM核算明细-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.delete(param));
    }

    /**
     * 更新
     *
     * @param param 更新参数
     * @return 操作结果
     */
    @PostMapping("/update")
    @BusinessLog(title = "BOM核算明细-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.update(param));
    }

    @PostMapping("/updateBaseUse")
    @BusinessLog(title = "BOM核算明细-修改使用量", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData updateBaseUse(@RequestBody BomAccountingDetail param) {
        bomAccountingDetailService.updateBaseUse(param);
        return new SuccessResponseData();
    }

    /**
     * 导出Excel
     *
     * @param param 查询参数
     * @param response HTTP响应
     */
    @PostMapping("/export")
    @BusinessLog(title = "BOM核算明细-导出Excel", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void exportExcel(@RequestBody BomAccountingDetail param, HttpServletResponse response) {
        bomAccountingDetailService.exportExcel(param, response);
    }

    /**
     * 获取核算结果
     *
     * @param param 查询参数
     * @return 核算结果
     */
    @PostMapping("/getAccountingResult")
    @BusinessLog(title = "BOM核算明细-获取核算结果", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getAccountingResult(@RequestBody BomAccountingDetail param) {
        return new SuccessResponseData(bomAccountingDetailService.getAccountingResult(param));
    }

    /**
     * 下载BOM文件模板
     *
     * @param param 查询参数
     * @param response HTTP响应
     */
    @PostMapping("/downloadTemplate")
    @BusinessLog(title = "BOM核算明细-下载模板", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void downloadTemplate(@RequestBody BomAccountingDetail param, HttpServletResponse response) {
        bomAccountingDetailService.exportTemplate(response, param);
    }


    @PostMapping("/syncBomAccountingDetail")
    @BusinessLog(title = "BOM核算明细-同步BOM核算明细", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData syncBomAccountingDetail(@RequestBody BomAccountingDetail param) {
        bomAccountingDetailService.syncBomAccountingDetail(param);
        return new SuccessResponseData();
    }
}
