package eve.sys.modular.bombill.bomaccountingdetail.service.impl;

import cn.hutool.core.util.ObjectUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bombill.bomaccountingdetail.dto.BomAccountingDetailExcelDto;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomaccountingdetail.mapper.BomAccountingDetailMapper;
import eve.sys.modular.bombill.bomaccountingdetail.params.BomAccountingDetailDataListener;
import eve.sys.modular.bombill.bomaccountingdetail.params.BomAccountingDetailParam;
import eve.sys.modular.bombill.bomaccountingdetail.dto.BomAccountingDetailTemplateDto;
import eve.sys.modular.bombill.bomaccountingdetail.service.IBomAccountingDetailService;
import eve.sys.modular.bombill.bomaccountingdetail.excel.ColumnWidthHandler;
import eve.sys.modular.bombill.bomaccountingdetail.util.BomAccountingDetailExcelUtil;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;
import eve.sys.modular.bombill.bomcostoverview.params.PositiveType2PartNo;
import eve.sys.modular.bombill.bomcostoverview.service.IBomCostOverviewService;
import eve.sys.modular.bombill.chemicalelementprice.entity.ChemicalElementPrice;
import eve.sys.modular.bombill.chemicalelementprice.service.IChemicalElementPriceService;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.materialprice.service.IMaterialPriceService;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import eve.sys.modular.bombill.positivematerialprice.entity.PositiveMaterialPrice;
import eve.sys.modular.bombill.positivematerialprice.service.IPositiveMaterialPriceService;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import eve.sys.modular.bombill.scenario.service.IScenarioService;
import eve.sys.modular.file.result.SysFileInfoResult;
import eve.sys.modular.file.service.SysFileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * BOM核算明细Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
public class BomAccountingDetailServiceImpl extends ServiceImpl<BomAccountingDetailMapper, BomAccountingDetail> implements IBomAccountingDetailService {

    @Override
    public PageResult<BomAccountingDetail> pageList(BomAccountingDetail param) {
        LambdaQueryWrapper<BomAccountingDetail> queryWrapper = new LambdaQueryWrapper<>();
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(BomAccountingDetail::getCreateTime);
        
        Page<BomAccountingDetail> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<BomAccountingDetail> pageResult = this.page(page, queryWrapper);
        
        return new PageResult<>(pageResult);
    }

    @Resource
    private IScenarioService scenarioService;

    @Override
    public List<JSONObject> list(BomAccountingDetail param) {

        LambdaQueryWrapper<BomAccountingDetail> queryWrapper = new LambdaQueryWrapper<>();
        
        if (ObjectUtil.isNotNull(param.getBomCostOverviewId())) {
            queryWrapper.eq(BomAccountingDetail::getBomCostOverviewId, param.getBomCostOverviewId());
        }
    
        List<BomAccountingDetail> list = this.list(queryWrapper);

        // 填充化学体系信息
        fillChemicalSystemInfo(list);

        List<Scenario> scenarios = scenarioService.list(Scenario.builder().bomCostOverviewId(param.getBomCostOverviewId()).build());

        List<JSONObject> jsonObjects = new ArrayList<>();

        list.forEach(e->{

            List<Scenario> scenarioList = scenarios.stream().filter(s->s.getBomAccountingDetailId().equals(e.getId())).collect(Collectors.toList());
            
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", e.getId());
            jsonObject.put("partNumber", e.getPartNumber());
            jsonObject.put("partName", e.getPartName());
            jsonObject.put("partDescription", e.getPartDescription());
            jsonObject.put("partUnit", e.getPartUnit());
            jsonObject.put("baseUse", e.getBaseUse());
            jsonObject.put("materialType", e.getMaterialType());
            jsonObject.put("structureFlag", e.getStructureFlag());
            jsonObject.put("chemicalSystem", e.getChemicalSystem());
            jsonObject.put("processingFee", e.getProcessingFee());
            jsonObject.put("partDesc",e.getPartDesc());
            jsonObject.put("remark", e.getRemark());
            scenarioList.forEach(s->{
                jsonObject.put("id"+s.getScenarioId(), s.getId());
                jsonObject.put("scenarioId", s.getScenarioId());
                jsonObject.put("untaxedUnitPrice"+s.getScenarioId(), s.getUntaxedUnitPrice());
                jsonObject.put("amountCnyEa"+s.getScenarioId(), s.getAmountCnyEa());
                jsonObject.put("amountCnyWh"+s.getScenarioId(), s.getAmountCnyWh());
                jsonObject.put("proportion"+s.getScenarioId(), s.getProportion());
                jsonObject.put("bomAccountingDetailId"+s.getScenarioId(), s.getBomAccountingDetailId());
                jsonObject.put("baseUse"+s.getScenarioId(), s.getBaseUse());
                jsonObject.put("hide"+s.getScenarioId(), false);
            });
            
            jsonObjects.add(jsonObject);
        });

        return jsonObjects;
    }

    @Override
    public Boolean add(BomAccountingDetail param) {
        return this.save(param);
    }

    @Resource
    private IPositiveMaterialPriceService positiveMaterialPriceService;

    @Transactional
    @Override
    public Boolean delete(BomAccountingDetail param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        this.removeById(param.getId());
        scenarioService.remove(Wrappers.lambdaQuery(Scenario.class).eq(Scenario::getBomAccountingDetailId, param.getId()));
        positiveMaterialPriceService.remove(Wrappers.lambdaQuery(PositiveMaterialPrice.class).eq(PositiveMaterialPrice::getBomAccountingDetailId, param.getId()));
        return true;
    }

    @Override
    public Boolean update(BomAccountingDetail param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        BomAccountingDetail updateEntity = BomAccountingDetail.builder()
            .id(param.getId())
            //.bomCostOverviewId(param.getBomCostOverviewId())
            .partNumber(param.getPartNumber())
            //.partName(param.getPartName())
            //.partDescription(param.getPartDescription())
            .partUnit(param.getPartUnit())
            //.baseUse(param.getBaseUse())
            .structureFlag(param.getStructureFlag())
            //.materialType(param.getMaterialType())
            .remark(param.getRemark())
            .build();
        return this.updateById(updateEntity);
    }

    @Override
    public BomAccountingDetail get(BomAccountingDetail param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }

    
    /* 
     * 修改物料用量触发修改场景使用量
     */
    public void updateBaseUse(BomAccountingDetail param){
        LambdaUpdateWrapper<BomAccountingDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BomAccountingDetail::getId, param.getId());
        updateWrapper.set(BomAccountingDetail::getBaseUse, param.getBaseUse());
        this.update(updateWrapper);

        Scenario scenario = Scenario.builder().bomAccountingDetailId(param.getId()).baseUse(param.getBaseUse()).build();
        scenarioService.batchUpdateBaseUse(scenario);
    }
    
    /* 
     * 批量插入
     */
    @Resource
    private IMaterialPriceService materialPriceService;
    
    @Resource
    private IPositiveMaterialAccountingService positiveMaterialAccountingService;

    @Override
    public void insertBatch(List<BomAccountingDetailParam> params,BomCostOverview bomCostOverview) {
        if (ObjectUtil.isEmpty(params)) {
            return;
        }
        List<MaterialPrice> materialPrices = materialPriceService.list(
            MaterialPrice
            .builder()
            .partNumbers(params.stream().map(BomAccountingDetailParam::getPartNumber).collect(Collectors.toList()))
            .build()
        );
        List<PositiveMaterialAccounting> positiveMaterialAccountings = positiveMaterialAccountingService.list();
        
        List<BomAccountingDetail> bomAccountingDetails = new ArrayList<>();
        params.forEach(e->{

            Optional<MaterialPrice> m = materialPrices.stream().filter(mp->mp.getPartNumber().equals(e.getPartNumber())).findFirst();

            Optional<PositiveType2PartNo> keyVal = bomCostOverview.getANodeItems().stream().filter(
                a->a.getValue().equals(e.getPartNumber())
            ).findFirst();

            Optional<PositiveMaterialAccounting> p = positiveMaterialAccountings.stream().filter(
                pma->pma.getId().equals(keyVal.map(PositiveType2PartNo::getPositiveMaterialAccountingId).orElse(null))
            ).findFirst();
           
            e.setMaterialType(m.map(MaterialPrice::getMaterialType).orElse(0));
            e.setStructureFlag(m.map(MaterialPrice::getStructuralPart).orElse(0));
            e.setPartDescription(m.map(MaterialPrice::getDescription).orElse(e.getPartDescription()));
            
            //正极材料核算
            //e.setChemicalSystemCode(p.map(PositiveMaterialAccounting::getChemicalSystemCode).orElse(""));
            e.setPositiveMaterialAccountingId(p.map(PositiveMaterialAccounting::getId).orElse(null));
            e.setProcessingFee(p.map(PositiveMaterialAccounting::getProcessingFee).orElse(BigDecimal.ZERO));
            
            // 如果是正极材料，物料类型为‘-’，结构件标志为 ‘否’
            if (p.isPresent()) {
                e.setMaterialType(0);
                e.setStructureFlag(0);
            }

            bomAccountingDetails.add(
                BomAccountingDetail
                .builder()
                .bomCostOverviewId(bomCostOverview.getId())
                .partNumber(e.getPartNumber())
                .partName(e.getPartName())
                .partDesc(e.getPartDesc())//excel的物料描述或系统bom的物料规格
                .partDescription(e.getPartDescription())//匹配到的材料价格的物料规格描述
                .partUnit(e.getPartUnit())//excel的物料单位或系统bom的物料单位
                .baseUse(e.getBaseUse())//excel的理论用量或系统bom的理论用量
                .structureFlag(e.getStructureFlag())
                .materialType(e.getMaterialType())
                .positiveMaterialAccountingId(e.getPositiveMaterialAccountingId())
                .processingFee(e.getProcessingFee())

                //正极材料不匹配未税单价或者单位不匹配，未税单价置为0
                .disableEditUnitPrice(null != e.getPositiveMaterialAccountingId() || null == e.getPartUnit()  || !e.getPartUnit().equals(m.map(MaterialPrice::getUnit).orElse("")))
                .build()
            );
        });
        this.saveBatch(bomAccountingDetails);
        scenarioService.insertBatch(bomAccountingDetails, materialPrices, bomCostOverview);
    }

    @Resource
    private IBomCostOverviewService bomCostOverviewService;

    @Transactional
    @Override
    public void syncBomAccountingDetail(BomAccountingDetail param) {
        
        List<BomAccountingDetail> list = this.list(Wrappers.lambdaQuery(BomAccountingDetail.class).eq(BomAccountingDetail::getBomCostOverviewId, param.getBomCostOverviewId()));

        BomCostOverview bomCostOverview = bomCostOverviewService.getById(param.getBomCostOverviewId());

        List<MaterialPrice> materialPrices = materialPriceService.list(
            MaterialPrice
            .builder()
            .partNumbers(list.stream().map(BomAccountingDetail::getPartNumber).collect(Collectors.toList()))
            .build()
        );
        List<PositiveMaterialAccounting> positiveMaterialAccountings = positiveMaterialAccountingService.list();

        list.forEach(e->{

            Optional<MaterialPrice> m = materialPrices.stream().filter(mp->mp.getPartNumber().equals(e.getPartNumber())).findFirst();

            Optional<PositiveMaterialAccounting> p = positiveMaterialAccountings.stream().filter(
                pma->pma.getId().equals(e.getPositiveMaterialAccountingId())
            ).findFirst();
           
            e.setMaterialType(m.map(MaterialPrice::getMaterialType).orElse(0));
            e.setStructureFlag(m.map(MaterialPrice::getStructuralPart).orElse(0));
            e.setPartDescription(m.map(MaterialPrice::getDescription).orElse(e.getPartDescription()));
            
            //正极材料核算
            e.setPositiveMaterialAccountingId(p.map(PositiveMaterialAccounting::getId).orElse(null));
            e.setProcessingFee(p.map(PositiveMaterialAccounting::getProcessingFee).orElse(BigDecimal.ZERO));
            
            // 如果是正极材料，物料类型为‘-’，结构件标志为 ‘否’
            if (p.isPresent()) {
                e.setMaterialType(0);
                e.setStructureFlag(0);
            }
            e.setDisableEditUnitPrice(null != e.getPositiveMaterialAccountingId() || null == e.getPartUnit()  || !e.getPartUnit().equals(m.map(MaterialPrice::getUnit).orElse("")));

        });

        this.updateBatchById(list);

        scenarioService.syncScenarios(list, materialPrices, bomCostOverview);

    }

    /* 
     * excel导入
     */
    @Resource
    private SysFileInfoService fileInfoService;
    @Override
    public void importfromExcel(BomCostOverview bomCostOverview) throws Exception {
        SysFileInfoResult result = fileInfoService.getFileInfoResult(bomCostOverview.getBomFileId());
        ExcelReader reader = EasyExcel.read(result.getFileObj(), BomAccountingDetailParam.class, new BomAccountingDetailDataListener(this,bomCostOverview)).build();
        List<ReadSheet>  sheets = reader.excelExecutor().sheetList();
        for (ReadSheet sheet : sheets) {
            reader.read(sheet);
        }
    }

    /* 
     * bom导入
     * 
     */
    @Resource
    private ISysBomService bomService;
    @Override
    public void importfromBom(BomCostOverview bomCostOverview){

        SysBom bom = bomService.get(bomCostOverview.getBomId());

        List<TreeBom> treeBoms = JSONObject.parseArray(bom.getBomData(), TreeBom.class);

        List<BomAccountingDetailParam> params = new ArrayList<>();

        List<TreeBom> queqeBoms = new ArrayList<>();
        
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom e = queqeBoms.remove(0);

                if (null == e.getBase() || !e.getBase().equals(1)) {
                    
                    params.add(
                        BomAccountingDetailParam
                        .builder()
                        .partNumber(e.getSapNumber())
                        .partName(e.getPartName())
                        .partDesc(e.getPartDescription())
                        .partUnit(e.getPartUnit())
                        .baseUse(BigDecimal.valueOf(e.getPartUse()))
                        .build()
                    );
                }
                
                if (!e.noSub()) {
                    queqeBoms.addAll(e.getSubstitute());
                }
                if (!e.isLeaf()) {
                    queqeBoms.addAll(e.getLists());
                }
            }
        }
        this.insertBatch(params,bomCostOverview);
    }

    /* 
     * 模板导出
     */

    @Override
    public void exportTemplate(HttpServletResponse response, BomAccountingDetail param) {
        try {
            log.info("开始导出BOM核算明细模板");

            String fileName = "BOM文件模板.xlsx";
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename="+ URLEncoder.encode(fileName,"UTF-8").replaceAll("\\+", "%20"));

            // 创建示例数据，帮助用户理解填写格式
            List<BomAccountingDetailTemplateDto> templateData = new ArrayList<>();
            BomAccountingDetailTemplateDto example = BomAccountingDetailTemplateDto.builder()
                .partNumber("示例物料代码")
                .partName("示例物料名称")
                .partDescription("示例物料规格")
                .partUnit("个")
                .baseUse(new BigDecimal("1.00"))
                .structureFlag(1) // 1表示"是"，0表示"否"
                .build();
            templateData.add(example);

            // 使用专门的模板DTO类导出，只包含用户需要填写的字段
            EasyExcel
                .write(response.getOutputStream(), BomAccountingDetailTemplateDto.class)
                .sheet("BOM核算明细模板")
                .doWrite(templateData);

            log.info("BOM核算明细模板导出完成");

        } catch (Exception e) {
            log.error("导出BOM核算明细模板失败", e);
            throw new ServiceException(500, "导出模板失败: " + e.getMessage());
        }
    }

    @Override
    public void exportExcel(BomAccountingDetail param, HttpServletResponse response) {
        try {
            log.info("开始导出BOM核算明细Excel，查询参数: {}", param);

            // 1. 查询数据
            List<JSONObject> jsonObjects = this.list(param);

            if (jsonObjects.isEmpty()) {
                log.warn("没有找到符合条件的数据，导出空Excel");
            }

            // 2. 获取场景ID列表
            Scenario scenarioParam = new Scenario();
            scenarioParam.setBomCostOverviewId(param.getBomCostOverviewId());
            List<Long> scenarioIds = scenarioService.getScenarioIdsByBomCostOverviewId(scenarioParam);

            log.info("获取到 {} 个场景ID: {}", scenarioIds.size(), scenarioIds);

            // 3. 转换为Excel DTO
            List<BomAccountingDetailExcelDto> excelDataList = BomAccountingDetailExcelUtil.toExcelDtoList(jsonObjects, scenarioIds);

            // 4. 生成文件名
            String fileName = "BOM核算明细_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            // 5. 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 6. 生成数据行
            List<List<Object>> dataRows = BomAccountingDetailExcelUtil.generateDataRows(excelDataList, scenarioIds);

            // 7. 生成完整表头
            List<List<String>> completeHeads = BomAccountingDetailExcelUtil.generateCompleteHeads(scenarioIds);

            // 8. 创建列宽处理器
            int baseColumnCount = 8; // 基础列数量（物料代码到备注）
            ColumnWidthHandler columnWidthHandler = new ColumnWidthHandler(baseColumnCount, scenarioIds.size());

            // 9. 写入Excel（使用EasyExcel自动处理分组表头，自定义处理器设置列宽）
            EasyExcel.write(response.getOutputStream())
                    .head(completeHeads)
                    .registerWriteHandler(columnWidthHandler)
                    .sheet("BOM核算明细")
                    .doWrite(dataRows);

            log.info("BOM核算明细Excel导出成功，共导出 {} 条数据", excelDataList.size());

        } catch (Exception e) {
            log.error("BOM核算明细Excel导出失败", e);
            e.printStackTrace();

            // 导出失败时返回错误信息
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    @Override
    public JSONObject getAccountingResult(BomAccountingDetail param) {
        JSONObject result = new JSONObject();

        if (ObjectUtil.isNull(param.getBomCostOverviewId())) {
            throw new ServiceException(500, "BOM成本总览ID不能为空");
        }

        try {
            // 1. 获取所有场景ID
            List<Scenario> allScenarios = scenarioService.list(
                Scenario.builder().bomCostOverviewId(param.getBomCostOverviewId()).build()
            );

            // 获取所有场景ID并去重排序
            List<Long> scenarioIds = allScenarios.stream()
                .map(Scenario::getScenarioId)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

            // 2. 获取BOM核算明细数据
            LambdaQueryWrapper<BomAccountingDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BomAccountingDetail::getBomCostOverviewId, param.getBomCostOverviewId());
            List<BomAccountingDetail> detailList = this.list(queryWrapper);

            // 3. 计算每个场景的核算结果
            List<JSONObject> scenarioResults = new ArrayList<>();

            for (Long scenarioId : scenarioIds) {
                JSONObject scenarioResult = new JSONObject();
                scenarioResult.put("scenarioId", scenarioId);
                scenarioResult.put("scenarioName", "场景" + scenarioId);

                // 获取该场景的所有数据
                List<Scenario> currentScenarios = allScenarios.stream()
                    .filter(s -> s.getScenarioId().equals(scenarioId))
                    .collect(Collectors.toList());

                // 计算结构件成本(元/Wh) - 结构件标志为1的材料价格合计
                BigDecimal structureCost = BigDecimal.ZERO;
                // 计算BOM成本(元/Wh) - 所有材料价格合计
                BigDecimal bomCostWh = BigDecimal.ZERO;
                // 计算BOM成本(元/EA) - 所有材料价格合计
                BigDecimal bomCostEa = BigDecimal.ZERO;

                for (Scenario scenario : currentScenarios) {
                    // 查找对应的明细记录
                    Optional<BomAccountingDetail> detailOpt = detailList.stream()
                        .filter(d -> d.getId().equals(scenario.getBomAccountingDetailId()))
                        .findFirst();

                    if (detailOpt.isPresent()) {
                        BomAccountingDetail detail = detailOpt.get();

                        // 累加BOM成本
                        if (scenario.getAmountCnyWh() != null) {
                            bomCostWh = bomCostWh.add(scenario.getAmountCnyWh());
                        }
                        if (scenario.getAmountCnyEa() != null) {
                            bomCostEa = bomCostEa.add(scenario.getAmountCnyEa());
                        }

                        // 如果是结构件，累加到结构件成本
                        if (detail.getStructureFlag() != null && detail.getStructureFlag().equals(1)) {
                            if (scenario.getAmountCnyWh() != null) {
                                structureCost = structureCost.add(scenario.getAmountCnyWh());
                            }
                        }
                    }
                }

                // 计算化学材料成本(元/Wh) = BOM成本(元/Wh) - 结构件(元/Wh)
                BigDecimal chemicalCost = bomCostWh.subtract(structureCost);

                scenarioResult.put("chemicalCost", chemicalCost.setScale(3, BigDecimal.ROUND_HALF_UP));
                scenarioResult.put("structureCost", structureCost.setScale(3, BigDecimal.ROUND_HALF_UP));
                scenarioResult.put("bomCostWh", bomCostWh.setScale(3, BigDecimal.ROUND_HALF_UP));
                scenarioResult.put("bomCostEa", bomCostEa.setScale(3, BigDecimal.ROUND_HALF_UP));

                scenarioResults.add(scenarioResult);
            }

            result.put("scenarioResults", scenarioResults);
            result.put("scenarioIds", scenarioIds);

            log.info("核算结果计算完成，场景数量: {}", scenarioIds.size());

        } catch (Exception e) {
            log.error("获取核算结果失败", e);
            throw new ServiceException(500, "获取核算结果失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 填充化学体系信息
     * 根据positiveMaterialAccountingId查询对应的化学体系编号和名称
     */
    private void fillChemicalSystemInfo(List<BomAccountingDetail> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 收集所有的positiveMaterialAccountingId
        Set<Long> accountingIds = list.stream()
            .map(BomAccountingDetail::getPositiveMaterialAccountingId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (accountingIds.isEmpty()) {
            return;
        }

        // 批量查询PositiveMaterialAccounting信息
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getId, accountingIds)
        );

        // 创建ID到对象的映射
        Map<Long, PositiveMaterialAccounting> accountingMap = accountingList.stream()
            .collect(Collectors.toMap(PositiveMaterialAccounting::getId, Function.identity()));

        // 填充化学体系信息
        list.forEach(detail -> {
            if (detail.getPositiveMaterialAccountingId() != null) {
                PositiveMaterialAccounting accounting = accountingMap.get(detail.getPositiveMaterialAccountingId());
                if (accounting != null) {
                    detail.setChemicalSystemCode(accounting.getChemicalSystemCode());
                    detail.setChemicalSystem(accounting.getChemicalSystem());
                }
            }
        });
    }

}
