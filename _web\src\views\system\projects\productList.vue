<template>
    <div>
        <div class="product_wrapper">
            <div class="product_tabs">
                <div class="head active">
                    产品列表
                </div>

                <div class="circle"></div>
                <div class="line"></div>
            </div>

            <div class="searchForm">
                <div class="left">
                    <div class="searchItem">
                        <span>立项日期:</span>
                        <a-range-picker :placeholder="['立项开始日期', '立项结束日期']" size="small" @change="dateChange" />
                    </div>
                    <div class="searchItem">
                        <span>细分市场:</span>
                        <treeselect
							:limit="1"
							@input="change"
							:max-height="200"
							placeholder="请选择细分市场"
							value-consists-of="BRANCH_PRIORITY"
							v-model="queryParam.cates"
							:multiple="true"
							:options="cates"
						/>
                    </div>

                    <div class="searchItem">
                        <span>产品状态:</span>
                        <treeselect
							:limit="1"
							@input="change"
							:max-height="200"
							placeholder="请选择产品状态"
							value-consists-of="BRANCH_PRIORITY"
							v-model="queryParam.states"
							:multiple="true"
							:options="states"
						/>
                    </div>
                    <div class="searchItem">
                      <span>产品部门:</span>
                      <treeselect
                        @input="change"
                        :max-height="200"
                        placeholder="请选择所属部门"
                        value-consists-of="BRANCH_PRIORITY"
                        v-model="queryParam.depts"
                        :multiple="true"
                        :options="deptsOptions"
                      />
                    </div>
                    <div class="searchItem">
                        <span>产品名称:</span>
                        <a-input
							size="small"
							@keyup.enter.native="change"
							v-model="queryParam.keyword"
							placeholder="请输入产品名称"
						>
							<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
						</a-input>
                    </div>
                </div>
                <div class="right">
                    <a-button size='small' class="product_btn" @click="reload" :style="{marginRight:'8px'}">
                        重置
                    </a-button>
                    <a-button class="product_btn product_btn_primary" size='small' @click="$refs.addForm.add()">
                        新建
                    </a-button>
                </div>
            </div>

            <!-- 表格 start -->
            <div>
                <a-spin :spinning="loading">
                    <a-table
                        size="middle"
                        :customRow="customRow"
                        ref="table"
                        :scroll="{x:true,y:windowHeight}"
                        :rowKey="record => record.issueId+record.productChildCate"
                        :columns="columns"
                        :dataSource="loadData">

                        <template slot="productSplitName" slot-scope="text, record">
                            <div v-for="(item,i) in record.productSplitName.split(';')" :key="i">
                                {{ item }}
                            </div>
                        </template>

                        <template slot="lines" slot-scope="text, record">
                            <span v-if="record.lines && record.lines.length > 0" >
                                {{ record.lines.map(item => dataLines[item]).filter((value, index, self) => {
                                    return self.indexOf(value) === index;
                                }).join() }}
                            </span>
                            <span v-else>-</span>
                        </template>

                        <template slot="projectFixedDate" slot-scope="text, record">
                            <span v-if="record.actualFixedDate"><span class="fixedDateCls greenBg" ></span>{{record.actualFixedDate}}</span>
                            <span v-else>
                                <template v-if="text">
                                    <span class="fixedDateCls" :class="Date.parse(text) < (new Date()) ? 'yellowBg' : ''" ></span>
                                    {{ text }}
                                </template>
                                <template v-else><span>-</span></template>
                            </span>
                        </template>



                        <span slot="action" slot-scope="text,record">
                            <a>
                                <a-icon :style="{fontSize:'15px'}" v-if="record.productOrProject === '1'" class="icon" type="line-chart" @click.stop="handleIcon(record)" />
                            </a>
                        </span>

                    </a-table>
                </a-spin>
            </div>
            <!-- 表格 end -->
            <add-form ref="addForm" @ok="getAlls" />
        </div>
    </div>
</template>

<script>
import { getwerklines } from "@/api/modular/system/bomManage"
import { getAlls } from "@/api/modular/system/productManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import addForm from "./create/addForm"
import Vue from 'vue'
export default {
    props: {

    },
    components: {
        Treeselect,
        addForm
    },
    data() {
        return {
            dataLines:{},
            cates: [],
            states:[],
            deptsOptions: [],
            queryParam: {
                cates:[],
                states:[],
                depts:[]
            },
            loading: false,
            // windowHeight: document.documentElement.clientHeight - 220,
            windowHeight: document.documentElement.clientHeight - 151 - 60 - 24 - 10 - 2,
            loadData: [],
            originLoadData: [],
            columns: [
                {
                    title: "序号",
                    width: 50,
                    dataIndex: "no",
                    align: "center",
                    customRender: (text, record, index) => {
						if (record.productOrProject == 1 && !record.stage) {
							return `${index + 1}`
						}
						return ''
					},
                },
                {
                    title: "细分市场",
                    width: 120,
                    dataIndex: "productSplitName",
                    align: "center",
                    scopedSlots: {
                        customRender: 'productSplitName'
                    }
                },
                {
                    title: "产品名称",
                    width: 80,
                    dataIndex: "productProjectName",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "项目名称",
                    width: 80,
                    dataIndex: "projectName",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "等级",
                    width: 50,
                    dataIndex: "projectLevelName",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "PD",
                    width: 80,
                    align: "center",
                    dataIndex: "productManagerName",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "RPM",
                    width: 80,
                    dataIndex: "productRPMName",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "PM",
                    width: 80,
                    dataIndex: "largeProjectManagerName",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "客户代码",
                    width: 80,
                    dataIndex: "customer",
                    align: "center",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "客户项目",
                    width: 80,
                    align: "center",
                    dataIndex: "customerProject",
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "定点日期",
                    width: 80,
                    align: "center",
                    dataIndex: 'projectFixedDate',
                    scopedSlots: {
                        customRender: 'projectFixedDate'
                    }
                },
                {
                    title: "立项日期",
                    width: 80,
                    align: "center",
                    dataIndex: 'initiationDate',
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "PPAP日期",
                    width: 80,
                    align: "center",
                    dataIndex: 'productPlannedM5',
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "产品状态",
                    width: 80,
                    align: "center",
                    dataIndex: 'productStateName',
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},
                },
                {
                    title: "适用工厂",
                    width: 80,
                    align: "center",
                    scopedSlots: {
                        customRender: 'lines'
                    }
                },
                {
                    title: "研究所",
                    width: 120,
                    align: "center",
                    dataIndex: 'parentDeptName',
                    customRender: (text, record, index) => {
						return text ? text : '-'
					},

                },
                {
                    title: "操作",
                    width: 60,
                    align: "center",
                    dataIndex: 'action',
                    scopedSlots: {
                        customRender: 'action'
                    }
                },
            ],

        }
    },
    methods: {
        handleIcon(value) {
            console.log(value)
            this.$router.push({
				path: "/product_dashboard",
				query:{cateId:value.productCateMulti,keyword:value.productProjectName}
			})
		},
        reload() {
			this.queryParam = {
                states:[],
                cates: []
            }
			let filterData = JSON.parse(JSON.stringify(this.originLoadData))
			this.loadData = filterData
		},
        getwerklines() {
                getwerklines().then((res) => {
                    if (res.success) {
                        let mapline = {}
                        for (var key in res.data) {
                            for (const _item of res.data[key]) {
                                mapline[_item.id] = _item.lineName && _item.lineName.length > 0 && _item.lineName.indexOf('-') > -1 ? _item.lineName.split('-')[0] : _item.lineName //_item.werkNo + ( _item.lineName && _item.lineName.length > 0 ? '->' + _item.lineName : '')
                            }
                        }
                        this.dataLines = mapline
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                })
            },
        change() {
            console.log(this.queryParam)
			this.callFilter()
		},
        getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
        getProductState(){
            let items = this.getDict('product_state_status')
            items.sort((a,b)=>a.sort-b.sort)
            let states = []
            for (const item of items) {
                let _item = {
                    id: parseInt(item.code),
                    label: item.name
                }
                states.push(_item)
            }
            this.states = states
        },
        getJiraOptionList(){
            getJiraOptionList({fieldName:'allProductCate'}).then(res => {
                if (res.success) {
                    let cates = []
                    let parentNodes = res.data.filter(o => o.parentoptionid == null)
                    for (const item of parentNodes) {
                        let _item = {
                            id: parseInt(item.id),
                            label: item.customvalue
                        }
                        let childNodes = res.data.filter(o => o.parentoptionid == item.id)
                        if (childNodes && childNodes.length > 0) {
                            _item.children = []
                            for (const $item of childNodes) {
                                _item.children.push({
                                    id: parseInt($item.id),
                                    label: $item.customvalue
                                })
                            }
                        }
                        cates.push(_item)
                    }
                    this.cates = cates
                }
            })
        },
        async getJiraOptionListAll(){
            await this.getJiraOptionList()
            getJiraOptionList({fieldName:'fileTranProductCate'}).then(res => {
                if (res.success) {
                    let cates = this.cates 
                    let cateIds = cates.map(o => o.id)
                    let parentNodes = res.data.filter(o => o.parentoptionid == null)
                    
                    for (const item of parentNodes) {
                        if (cateIds.indexOf(parseInt(item.id)) > -1) {
                            continue
                        }
                        let _item = {
                            id: parseInt(item.id),
                            label: item.customvalue
                        }
                        let childNodes = res.data.filter(o => o.parentoptionid == item.id)
                        if (childNodes && childNodes.length > 0) {
                            _item.children = []
                            for (const $item of childNodes) {
                                _item.children.push({
                                    id: parseInt($item.id),
                                    label: $item.customvalue
                                })
                            }
                        }
                        cates.push(_item)
                    }
                    this.cates = cates
                }
            })
        },
        dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryParam.startDate = Date.parse(dateString[0])
			} else {
				this.queryParam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryParam.endDate = Date.parse(dateString[1])
			} else {
				this.queryParam.endDate = null
			}
            this.callFilter()
		},
        customRow(row, index) {
			return {
				on: {
					click: () => {
						this.$router.push({
							path: "/project_overview",
							query: {
								issueId: row.issueId
							}
						})
					}
				}
			}
		},
        getAlls() {
            let that = this
            that.loading = true
            getAlls({})
            .then(res => {

                res?.success
                && (that.loadData = res.data)


                res?.success  && (this.originLoadData = JSON.parse(JSON.stringify(res.data)))

                res?.success
                || that.$message.error(res?.message, 1)
            })
            .finally(() => {
				that.loading = false
			})
        },
        callFilter() {

			let filterData = JSON.parse(JSON.stringify(this.originLoadData))

            console.log(this.originLoadData)

			if (this.queryParam["cates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryParam['cates'].some(el => item.cateIds.includes(el+''))
				)
			}

      //部门模糊匹配
      if (this.queryParam["depts"] && this.queryParam["depts"].length > 0) {
        filterData = filterData.filter(item => this.queryParam["depts"].indexOf((item.parentDept)) > -1)
      }

			if (this.queryParam["states"].length > 0) {

				filterData = filterData.filter(
                    item => this.queryParam["states"].indexOf(parseInt(item.productState)) != -1
                )

			}
			if (this.queryParam.keyword != null && this.queryParam.keyword != "") {

                filterData = filterData.filter(
					item => item.productProjectName.toLowerCase().indexOf(this.queryParam.keyword.toLowerCase()) != -1
				)
			}

			if (this.queryParam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryParam.startDate &&
						Date.parse(item.initiationDate) < this.queryParam.endDate
				)
			}

			this.loadData = filterData
		},
      getDeptJiraOptionList() {
        // getJiraOptionList({ fieldName: 'department' }).then(res => {
        //   if (res.success) {
        //     let depts = []
        //     let _depts = ["18863", "18846", "22492", "22487", "18711", "22101", "22269", "22105"]
        //     let parentNodes = res.data.filter(o => o.parentoptionid == null)
        //     for (const item of parentNodes) {
        //       if (_depts.indexOf(item.id) == -1) {
        //         continue
        //       }
        //       let _item = {
        //         id: (item.id),
        //         label: item.customvalue
        //       }
        //       depts.push(_item)
        //     }
        //     this.deptsOptions = depts
        //   }
        // })
        this.deptsOptions = [{
            id: '18863',
            label: 'V圆柱所',
        },{
            id: '18846',
            label: 'G圆柱所',
        }, {
            id: '22492',
            label: 'C圆柱所',
        },  {
            id: '22487',
            label: '锰铁锂电所',
          }, {
            id: '18711',
            label: '铁锂一所',
          },{
            id: '22101',
            label: '铁锂二所',
        }, {
            id: '22269',
            label: '储能所',
          }, {
            id: '22105',
            label: '新型所',
        }];
      },
    },
    created() {
        this.getAlls()
        this.getwerklines()
        this.getJiraOptionListAll()
        this.getProductState()
        this.getDeptJiraOptionList()
    }
}
</script>

<style lang='less' scoped=''>

@import './product.less';
.searForm{
    margin: 10px 0;
}
/deep/.ant-table-tbody>tr:hover:not(.ant-table-expanded-row)>td{
    cursor: pointer;
}
/deep/.ant-table-body{
    overflow: initial !important;
}
/deep/.ant-table-scroll{
    overflow-x: scroll;
}
/deep/.ant-table-fixed-header .ant-table-scroll .ant-table-header{
    position: sticky;
    top: 0;
    z-index: 2;
}
/deep/ .ant-pagination {
	margin: 10px 0;
}
.fixedDateCls{
    /* padding: 2px 6px; */
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
    margin-right: 3px;
}
.greenBg{
    background: #58a55c;
}
.yellowBg{
    background: #fac858;
}
</style>