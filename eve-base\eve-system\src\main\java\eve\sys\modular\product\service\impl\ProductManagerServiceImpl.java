package eve.sys.modular.product.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import eve.core.context.login.LoginContextHolder;
import eve.core.pojo.page.PageResult;
import eve.sys.jiraModular.customTool.service.ICustomfieldoptionService;
import eve.sys.modular.product.entity.ProductManager;
import eve.sys.modular.product.mapper.ProductManagerMapper;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.param.ProductCateOptionBean;
import eve.sys.modular.product.param.ProductProjectItem;
import eve.sys.modular.product.param.ProductStageItem;
import eve.sys.modular.product.param.ProjectDetail;
import eve.sys.modular.product.param.StageRange;
import eve.sys.modular.product.service.IProductManagerChildIssueService;
import eve.sys.modular.product.service.IProductManagerService;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.topic.param.Cate;

import java.text.SimpleDateFormat;

@Service
@DS("b2")
public class ProductManagerServiceImpl extends ServiceImpl<ProductManagerMapper, ProductManager> implements IProductManagerService{

    @Resource
    private IProductManagerChildIssueService childIssueService;

    @Resource
    private ICustomfieldoptionService customfieldoptionService;

    @Override/* 成本分析 产品下拉选择框 */
    public List<ProductProjectItem> getAllProducts(String token){

        List<ProductManager> productList = this.list();

        List<ProductProjectItem> items = new ArrayList<>();
        
        List<ProductStageItem> stages = childIssueService.getStages(productList.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));
        
        List<Cate> cateBeans = customfieldoptionService.cateList("productCate");

        productList.forEach(e->{
            ProductProjectItem _e = ProductProjectItem.builder()
            .issueId(e.getIssueId())
            .issueKey(e.getIssueKey())
            .customer(e.getCustomer())
            .productOrProject(e.getProductOrProject())
            .fixedState(e.getFixedState().intValue())
            .state(e.getProductState().intValue())
            .productState(e.getState())
            .produceFeedback(e.getProduceFeedback())
            .supplyFeedback(e.getSupplyFeedback())
            .sellFeedback(e.getSellFeedback())
            .projectName(e.getProjectName())
            .productProjectName(e.getProductProjectName())
            .mStatus(e.getProductStage().intValue())
            .cateId(null == e.getProductChildCate() ? null :Long.valueOf(e.getProductChildCate()))
            .catepid(null == e.getProductParentCate() ? null : Long.valueOf(e.getProductParentCate()))
            .productClassification(e.getProductClassification())
            .productDistribute(e.getProductDistribute())
            .productDistributeName(e.getProductDistributeName())
            .researchProjectManager(e.getProductRPMName())
            .productManager(e.getProductManagerName())
            .plannedFixedDate(e.getProjectFixedDate())
            .productTechMajordomoName(e.getProductTechMajordomoName())
            .initiationDate(null == e.getInitiationDate() ? "" : e.getInitiationDate())
            .stopTime(e.getStopTime())
            .structureType(e.getStructureType()) //-- 电池结构
            //.scenario(e.getScenario())//-- 应用场景
            .productLevel(e.getProjectLevelName())
            .sellRepresent(e.getSellRepresent())
            .sellRepresentName(e.getSellRepresentName())
            .vCylinderProductDepartment(e.getVCylinderProductDepartment())
            .vCylinderProductDepartmentName(e.getVCylinderProductDepartmentName())
            .build();

            List<ProductCateOptionBean> depts = new ArrayList<>();
            List<ProductCateOptionBean> cates = new ArrayList<>();

            if (null != e.getParentDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(0L)
                    .id(e.getParentDept())
                    .value(e.getParentDeptName())
                    .build()
                );
            }

            if (null != e.getChildDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(e.getParentDept())
                    .id(e.getChildDept())
                    .value(e.getChildDeptName())
                    .build()
                );
            }

            if (!StrUtil.isEmpty(e.getProductCateMulti())) {
                Arrays.stream(e.getProductCateMulti().split(",")).forEach(str->{
                    Optional<Cate> cateOptional = cateBeans.stream().filter(cate->cate.getId().equals(Long.valueOf(str))).findFirst();
                    cateOptional.ifPresent(childCate->{
                        Optional<Cate> parentCateOptional = cateBeans.stream().filter(parentCate->parentCate.getId().equals(childCate.getPid())).findFirst();   
                        cates.add(
                            ProductCateOptionBean.builder()
                            .pid(parentCateOptional.isPresent() ? childCate.getPid() : 0L)
                            .id(childCate.getId())
                            .value(parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "->" + childCate.getValue() : childCate.getValue())
                            .build()
                        );
                    });
                });
            }

            _e.setDepartmentOptionList(depts);
            _e.setProductCateOptionBeans(cates);
            _e.setProductStageItems(stages.stream().filter($e->$e.getParentId().equals(e.getIssueId())).collect(Collectors.toList()));
            
            Optional<ProductStageItem> stageOptional = stages.stream().filter($e->$e.getParentId().equals(e.getIssueId()) && $e.getStage().equals(e.getProductStage())).findFirst();

            _e.setProductPlannedDate(!stageOptional.isPresent() ? "-": stageOptional.get().getPlanReviewDate());
            _e.setProductActualDate(!stageOptional.isPresent() ? "-": stageOptional.get().getActualCompletionDate());

            items.add(_e);
        });

        List<ProductProjectItem> productProjectItems = items.stream().filter(e->e.getProductOrProject().equals(1L)).collect(Collectors.toList());

        for (ProductProjectItem item : productProjectItems) {
            List<ProductProjectItem> childrens = items.stream().filter(e->!e.getProductOrProject().equals(1L) && e.getProductProjectName().equals(item.getProductProjectName())).collect(Collectors.toList());
            
            item.setChildren(childrens.size() > 0 ? childrens : null);
        }
        return productProjectItems;
    }
    
    /* 成本分析 项目研发汇总 */
    public ProductProjectItem getProject(String token,ProductManager param){
        
        LambdaQueryWrapper<ProductManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductManager::getIssueId,param.getIssueId());
        ProductManager e = this.getOne(queryWrapper);

        List<ProductStageItem> stages = childIssueService.getStages(new ArrayList<Long>(){{
            add(param.getIssueId());
        }});

        ProductProjectItem _e = null;

        if (ObjectUtil.isNotNull(e)) {

            _e = ProductProjectItem.builder()
            .issueId(e.getIssueId())
            .productOrProject(e.getProductOrProject())
            .children(new ArrayList<>())
            .mStatus(e.getProductStage().intValue())
            .build();
            _e.setProductStageItems(stages);
            setStageRange(_e);

            if (e.getProductOrProject().equals(1L)) {

                ProductProjectItem vo = JSONObject.parseObject(JSONObject.toJSONString(_e), ProductProjectItem.class);
                vo.setProductOrProject(2L);
                _e.getChildren().add(vo);

                LambdaQueryWrapper<ProductManager> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ProductManager::getProductProjectName, e.getProductProjectName());
                lambdaQueryWrapper.ne(ProductManager::getProductOrProject, 1);
                List<ProductManager> projects = this.list(lambdaQueryWrapper);

                List<ProductStageItem> _stages = childIssueService.getStages(
                    projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())
                );

                for (ProductManager project : projects) {
                    ProductProjectItem item = ProductProjectItem.builder()
                        .issueId(project.getIssueId())
                        .productOrProject(project.getProductOrProject())
                        .children(new ArrayList<>())
                        .mStatus(project.getProductStage().intValue())
                        .build();
                    item.setProductStageItems(_stages.stream().filter($e->$e.getParentId().equals(item.getIssueId())).collect(Collectors.toList()));
                    setStageRange(item);
                    _e.getChildren().add(item);
                }
            }
        }
        return _e;
    }

    @Override
    public List<ProductProjectItem> getProducts(ProductManager param){

        LambdaQueryWrapper<ProductManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductManager::getProductOrProject, 1);

        LambdaQueryWrapper<ProductManager> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(ProductManager::getProductOrProject, 1);
    

        if (ObjectUtil.isNotNull(param)) {
            if (ObjectUtil.isNotEmpty(param.getParentDepts())) {
                queryWrapper.in(ProductManager::getParentDept, param.getParentDepts());
                lambdaQueryWrapper.in(ProductManager::getParentDept, param.getParentDepts());
            }
            if (ObjectUtil.isNotEmpty(param.getProjectLevels())) {
                queryWrapper.in(ProductManager::getProjectLevel, param.getProjectLevels());
                lambdaQueryWrapper.in(ProductManager::getProjectLevel, param.getProjectLevels());
            }
            if (ObjectUtil.isNotEmpty(param.getProductStates())) {
                queryWrapper.in(ProductManager::getProductState, param.getProductStates());
                lambdaQueryWrapper.in(ProductManager::getProductState, param.getProductStates());
            }
            if (ObjectUtil.isNotEmpty(param.getProductStages())) {
                queryWrapper.in(ProductManager::getProductStage, param.getProductStages());
                lambdaQueryWrapper.in(ProductManager::getProductStage, param.getProductStages());
            }

            if (ObjectUtil.isNotEmpty(param.getKeyword())) {
                queryWrapper.like(ProductManager::getProductProjectName, param.getKeyword());
                lambdaQueryWrapper.like(ProductManager::getProjectName, param.getKeyword());
            }
        }

        List<ProductManager> rows = this.list(queryWrapper);

        if (rows.size() == 0) {
            return new ArrayList<>();
        }

        lambdaQueryWrapper.in(ProductManager::getProductProjectName, rows.stream().map(ProductManager::getProductProjectName).collect(Collectors.toList()));
        List<ProductManager> projects = this.list(lambdaQueryWrapper);
        rows.addAll(projects);

        List<ProductProjectItem> items = new ArrayList<>();

        List<ProductStageItem> stages = childIssueService.getStages(rows.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        rows.forEach(e->{
            ProductProjectItem _e = ProductProjectItem.builder()
            .issueId(e.getIssueId())
            .deptId(e.getParentDept())
            .issueKey(e.getIssueKey())
            .productOrProject(e.getProductOrProject())
            .state(e.getProductState().intValue())
            .productState(e.getState())
            .projectName(e.getProjectName())
            .productProjectName(e.getProductProjectName())
            .productManager(e.getProductManagerName())
            .mStatus(e.getProductStage().intValue())
            .productLevel(e.getProjectLevelName())
            .stateTxt(e.getProductStateName())
            .parentDeptName(e.getParentDeptName())
            .childDeptName(e.getChildDeptName())
            .children(new ArrayList<>())
            .build();

            _e.setProductStageItems(stages.stream().filter($e->$e.getParentId().equals(e.getIssueId())).collect(Collectors.toList()));
            
            setStageRange(_e);

            items.add(_e);
        });

        List<ProductProjectItem> productProjectItems = items.stream().filter(e->e.getProductOrProject().equals(1L)).collect(Collectors.toList());

        for (ProductProjectItem item : productProjectItems) {
            
            if (ObjectUtil.isEmpty(item.getChildren())) {
                item.setChildren(new ArrayList<>());
            } 
            
            ProductProjectItem vo = JSONObject.parseObject(JSONObject.toJSONString(item), ProductProjectItem.class);

            vo.setProductOrProject(2L);
            item.getChildren().add(vo);

            List<ProductProjectItem> childrens = items.stream().filter(e->!e.getProductOrProject().equals(1L) && e.getProductProjectName().equals(item.getProductProjectName())).collect(Collectors.toList());
            
            if (childrens.size() > 0 ) {
                item.getChildren().addAll(childrens);
            }
        }

        return productProjectItems;
    }

    @Override/* 成本分析 */
    public PageResult<ProductProjectItem> pageProjects(String token,ProductManager param) {

        LambdaQueryWrapper<ProductManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductManager::getProductOrProject, 1);

        LambdaQueryWrapper<ProductManager> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(ProductManager::getProductOrProject, 1);
    

        if (ObjectUtil.isNotNull(param)) {
            if (ObjectUtil.isNotEmpty(param.getParentDepts())) {
                queryWrapper.in(ProductManager::getParentDept, param.getParentDepts());
                lambdaQueryWrapper.in(ProductManager::getParentDept, param.getParentDepts());
            }
            if (ObjectUtil.isNotEmpty(param.getProjectLevels())) {
                queryWrapper.in(ProductManager::getProjectLevel, param.getProjectLevels());
                lambdaQueryWrapper.in(ProductManager::getProjectLevel, param.getProjectLevels());
            }
            if (ObjectUtil.isNotEmpty(param.getProductStates())) {
                queryWrapper.in(ProductManager::getProductState, param.getProductStates());
                lambdaQueryWrapper.in(ProductManager::getProductState, param.getProductStates());
            }
            if (ObjectUtil.isNotEmpty(param.getProductStages())) {
                queryWrapper.in(ProductManager::getProductStage, param.getProductStages());
                lambdaQueryWrapper.in(ProductManager::getProductStage, param.getProductStages());
            }

            if (ObjectUtil.isNotEmpty(param.getKeyword())) {
                queryWrapper.like(ProductManager::getProductProjectName, param.getKeyword());
                lambdaQueryWrapper.like(ProductManager::getProjectName, param.getKeyword());
            }
        }

        PageResult<ProductManager> pageResult = new PageResult<>(this.page(new Page<ProductManager>(param.getPageNo(),param.getPageSize()), queryWrapper));

        List<ProductProjectItem> items = new ArrayList<>();

        if (pageResult.getRows().size() == 0) {
            PageResult<ProductProjectItem> productProjectPageRes = new PageResult<>();
            productProjectPageRes.setPageNo(pageResult.getPageNo());
            productProjectPageRes.setPageSize(pageResult.getPageSize());
            productProjectPageRes.setRainbow(pageResult.getRainbow());
            productProjectPageRes.setTotalPage(pageResult.getTotalPage());
            productProjectPageRes.setTotalRows(pageResult.getTotalRows());
            productProjectPageRes.setRows(new ArrayList<>());
            return productProjectPageRes;
        }
        lambdaQueryWrapper.in(ProductManager::getProductProjectName, pageResult.getRows().stream().map(ProductManager::getProductProjectName).collect(Collectors.toList()));
        List<ProductManager> projects = this.list(lambdaQueryWrapper);

        pageResult.getRows().addAll(projects);

        List<ProductStageItem> stages = childIssueService.getStages(pageResult.getRows().stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        pageResult.getRows().forEach(e->{
            ProductProjectItem _e = ProductProjectItem.builder()
            .issueId(e.getIssueId())
            .issueKey(e.getIssueKey())
            .productOrProject(e.getProductOrProject())
            .state(e.getProductState().intValue())
            .productState(e.getState())
            .projectName(e.getProjectName())
            .productProjectName(e.getProductProjectName())
            .mStatus(e.getProductStage().intValue())
            .productLevel(e.getProjectLevelName())
            .children(new ArrayList<>())
            .build();

            List<ProductCateOptionBean> depts = new ArrayList<>();

            if (null != e.getParentDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(0L)
                    .id(e.getParentDept())
                    .value(e.getParentDeptName())
                    .build()
                );
            }

            if (null != e.getChildDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(e.getParentDept())
                    .id(e.getChildDept())
                    .value(e.getChildDeptName())
                    .build()
                );
            }


            _e.setDepartmentOptionList(depts);
            _e.setProductStageItems(stages.stream().filter($e->$e.getParentId().equals(e.getIssueId())).collect(Collectors.toList()));
            
            setStageRange(_e);

            items.add(_e);
        });

        List<ProductProjectItem> productProjectItems = items.stream().filter(e->e.getProductOrProject().equals(1L)).collect(Collectors.toList());

        for (ProductProjectItem item : productProjectItems) {
            
            if (ObjectUtil.isEmpty(item.getChildren())) {
                item.setChildren(new ArrayList<>());
            } 
            
            ProductProjectItem vo = JSONObject.parseObject(JSONObject.toJSONString(item), ProductProjectItem.class);

            vo.setProductOrProject(2L);
            item.getChildren().add(vo);

            List<ProductProjectItem> childrens = items.stream().filter(e->!e.getProductOrProject().equals(1L) && e.getProductProjectName().equals(item.getProductProjectName())).collect(Collectors.toList());
            
            if (childrens.size() > 0 ) {
                item.getChildren().addAll(childrens);
            }
        }

        PageResult<ProductProjectItem> productProjectPageRes = new PageResult<>();
        productProjectPageRes.setPageNo(pageResult.getPageNo());
        productProjectPageRes.setPageSize(pageResult.getPageSize());
        productProjectPageRes.setRainbow(pageResult.getRainbow());
        productProjectPageRes.setTotalPage(pageResult.getTotalPage());
        productProjectPageRes.setTotalRows(pageResult.getTotalRows());
        productProjectPageRes.setRows(productProjectItems);

        return productProjectPageRes;
    }

    private void setStageRange(ProductProjectItem e) {

        List<Long> stages = new ArrayList<Long>(){{
            add(1L); // k0
            add(2L); // m1
            add(4L); // m2 a样
            add(6L); // m3 b样
            add(7L); // m4 c样
        }};

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        List<StageRange> stageRanges = new ArrayList<>();

        Date endDate = null;
        Date startDate = null;

        Calendar calendar = Calendar.getInstance();

        int count = 0;

        for (int i = 0, j= stages.size(); i < j; i++) {

            final int n = i;

            String reviewDate = null;

            if(e.getMStatus() >= 7 && i == j-1){
                calendar.setTime(new Date());
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                reviewDate = dateFormat.format(calendar.getTime());
            }else{
                reviewDate = e.getProductStageItems().stream()
                .filter(_e->_e.getStage().equals(stages.get(n)))
                .filter(_e-> ObjectUtil.isNotEmpty(_e.getActualCompletionDate()))
                .map(ProductStageItem::getActualCompletionDate)
                .findFirst()
                .orElseGet(()->{
                    if ( e.getMStatus() >= 4 && e.getMStatus() <= stages.get(n)) {
                        calendar.setTime(new Date());
                        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                        return dateFormat.format(calendar.getTime());
                    }
                    return null;
                });
            }
            
            if(ObjectUtil.isNotEmpty(reviewDate)){
                try {
                    endDate = dateFormat.parse(reviewDate);
                } catch (Exception ex) {
                    endDate = null;
                }

                if(ObjectUtil.isNotEmpty(endDate)){

                    calendar.setTime(endDate);
                    int day = calendar.get(Calendar.DAY_OF_MONTH);
                    if (day >= 15) {
                        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                        endDate = calendar.getTime();//当月最后一天
                    }else{
                        calendar.set(Calendar.DAY_OF_MONTH, 1);//当月第一天
                        calendar.add(Calendar.DAY_OF_MONTH, -1);//当月第一天减去一天等于上个月的最后一天
                        endDate = calendar.getTime();//上个月最后一天
                    }
                }
                
            }else{
                endDate = null;
            }
            stageRanges.add(StageRange.builder()
            .startDate(null != endDate ? startDate : null)
            .endDate(endDate)
            .showFlag(1)
            .stageName( i == 0 ? "k0阶段" :"m"+(i)+"阶段")
            .stage(count)
            .build());

            if (ObjectUtil.isNotEmpty(endDate)) {
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                startDate = calendar.getTime();
            }

            count++;
        }

        endDate = null;
        startDate = null;

        char _char = 'A';
        for (int i = 2,j= stages.size(); i < j; i++) {
            
            if(ObjectUtil.isNotEmpty(stageRanges.get(i).getEndDate())){
                endDate = stageRanges.get(i).getEndDate();
            }else{
                
                endDate = null;

                // m2 计划时间
                boolean a = e.getProductStageItems().stream().filter(_e->_e.getStage().equals(4L)).map(ProductStageItem::getPlanReviewDate).findFirst().isPresent();
                
                // m3 计划时间
                boolean b = e.getProductStageItems().stream().filter(_e->_e.getStage().equals(6L)).map(ProductStageItem::getPlanReviewDate).findFirst().isPresent();

                calendar.setTime(new Date());
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

                if (i == 2 && e.getMStatus() < 4 ) {
  
                    if (!a && !b) {
                        endDate = calendar.getTime(); 
                    }
                    if (a) {
                        endDate = calendar.getTime(); 
                    }
                }

                if (i == 3 && e.getMStatus() < 4) {
                    if (!a && b) {
                        endDate = calendar.getTime(); 
                    }
                }
                
            }
            stageRanges.add(StageRange.builder()
            .startDate(null != endDate ? startDate : null)
            .endDate(endDate)
            .showFlag(2)
            .stageName(_char+"样")
            .stage(count)
            .build());

            if (null != endDate) {
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                startDate = calendar.getTime();
                //startDate = endDate;
            }
            _char++;
            count++;
        }

        e.setStageRanges(stageRanges);
    }

    @Override/* 产品管理 */
    public List<ProductProjectItem> getProducts(Boolean isVcyBoolean, Boolean isJMAreaBoolean)
    {
        /* JSONObject jiraResp = Utils.doGet(JiraApiParams.VProjectsListByPermitApi, token, null);
        if (!jiraResp.getBoolean("result")) {
            return new ArrayList<>();
        }

        List<Long> ids = JSONObject.parseObject(jiraResp.getString("value"),new TypeReference<List<Long>>(){});

        if (ObjectUtils.isEmpty(ids)) {
            return new ArrayList<>();
        } */

        String account = LoginContextHolder.me().getSysLoginUserAccount();

        LambdaQueryWrapper<ProductManager> productManagQueryWrapper = new LambdaQueryWrapper<>();
        //productManagQueryWrapper.in(ProductManager::getIssueId,ids);

        if (isVcyBoolean) {
            productManagQueryWrapper.in(ProductManager::getParentDept, Arrays.asList(18863L));
        }  else if (isJMAreaBoolean) { //"部门（研究院）"-荆门产品部门id
            productManagQueryWrapper.in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L));
        }else {
            productManagQueryWrapper.in(ProductManager::getParentDept, Arrays.asList(18863L, 18846L, 22492L, 22101L, 22105L));
        }

        
        

        List<ProductManager> productList = this.list(productManagQueryWrapper);


        List<ProductProjectItem> items = new ArrayList<>();
        
        List<ProductStageItem> stages = childIssueService.getStages(productList.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        List<Cate> cateBeans;
        if (isJMAreaBoolean) {
            cateBeans = customfieldoptionService.cateList("jmProductCate");
        } else {
            cateBeans = customfieldoptionService.cateList("productCate");
        }
        boolean isSuperAdmin = LoginContextHolder.me().isSuperAdmin();

        boolean isProductManger = LoginContextHolder.me().getLoginUserRoleIds().contains(isVcyBoolean ? "1797956238238863361" : "1797956039894421505");

        productList.forEach(e->{
            
            if (!isSuperAdmin && e.getAccounts().indexOf(account) == -1 && !isProductManger) {
                return;
            }
            ProductProjectItem _e = ProductProjectItem.builder()
            .issueId(e.getIssueId())
            .issueKey(e.getIssueKey())
            .customer(e.getCustomer())
            .productOrProject(e.getProductOrProject())
            .fixedState(e.getFixedState().intValue())
            .state(e.getProductState().intValue())
            .productState(e.getState())
            .produceFeedback(e.getProduceFeedback())
            .supplyFeedback(e.getSupplyFeedback())
            .sellFeedback(e.getSellFeedback())
            .projectName(e.getProjectName())
            .productProjectName(e.getProductProjectName())
            .mStatus(e.getProductStage().intValue())
            .cateId(null == e.getProductChildCate() ? null :Long.valueOf(e.getProductChildCate()))
            .catepid(null == e.getProductParentCate() ? null : Long.valueOf(e.getProductParentCate()))
            .productClassification(e.getProductClassification())
            .productDistribute(e.getProductDistribute())
            .productDistributeName(e.getProductDistributeName())
            .researchProjectManager(e.getProductRPMName())
            .productManager(e.getProductManagerName())
            .plannedFixedDate(e.getProjectFixedDate())
            .productTechMajordomoName(e.getProductTechMajordomoName())
            .initiationDate(null == e.getInitiationDate() ? "" : e.getInitiationDate())
            .stopTime(e.getStopTime())
            .structureType(e.getStructureType()) //-- 电池结构
            //.scenario(e.getScenario())//-- 应用场景
            .productLevel(e.getProjectLevelName())
            .sellRepresent(e.getSellRepresent())
            .sellRepresentName(e.getSellRepresentName())
            .vCylinderProductDepartment(e.getVCylinderProductDepartment())
            .vCylinderProductDepartmentName(e.getVCylinderProductDepartmentName())
            .build();

            List<ProductCateOptionBean> depts = new ArrayList<>();
            List<ProductCateOptionBean> cates = new ArrayList<>();

            if (null != e.getParentDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(0L)
                    .id(e.getParentDept())
                    .value(e.getParentDeptName())
                    .build()
                );
            }

            if (null != e.getChildDept()) {
                depts.add(
                    ProductCateOptionBean.builder()
                    .pid(e.getParentDept())
                    .id(e.getChildDept())
                    .value(e.getChildDeptName())
                    .build()
                );
            }

            if (!StrUtil.isEmpty(e.getProductCateMulti())) {
                Arrays.stream(e.getProductCateMulti().split(",")).forEach(str->{
                    Optional<Cate> cateOptional = cateBeans.stream().filter(cate->cate.getId().equals(Long.valueOf(str))).findFirst();
                    cateOptional.ifPresent(childCate->{
                        Optional<Cate> parentCateOptional = cateBeans.stream().filter(parentCate->parentCate.getId().equals(childCate.getPid())).findFirst();   
                        cates.add(
                            ProductCateOptionBean.builder()
                            .pid(parentCateOptional.isPresent() ? childCate.getPid() : 0L)
                            .id(childCate.getId())
                            .value(parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "->" + childCate.getValue() : childCate.getValue())
                            .build()
                        );
                    });
                });
            }

            _e.setDepartmentOptionList(depts);
            _e.setProductCateOptionBeans(cates);
            _e.setProductStageItems(stages.stream().filter($e->$e.getParentId().equals(e.getIssueId())).collect(Collectors.toList()));
            
            Optional<ProductStageItem> stageOptional = stages.stream().filter($e->$e.getParentId().equals(e.getIssueId()) && $e.getStage().equals(e.getProductStage())).findFirst();

            _e.setProductPlannedDate(!stageOptional.isPresent() ? "-": stageOptional.get().getPlanReviewDate());
            _e.setProductActualDate(!stageOptional.isPresent() ? "-": stageOptional.get().getActualCompletionDate());

            items.add(_e);
        });

        return items.stream()
        /* .filter(
            e->e.getProductCateOptionBeans()
            .stream()
            .filter(_e-> isVcyBoolean ? _e.getPid()
            .equals(20047L) : !_e.getPid().equals(20047L)
            )
            .findFirst()
            .isPresent()
        ) */
        .collect(Collectors.toList());
    }

    /* 产品管理 */
    public ProjectDetail getProduct(ProductProjectItem param) {

        LambdaQueryWrapper<ProductManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductManager::getIssueId,param.getIssueId());
        ProductManager productManager = this.getOne(queryWrapper);

        List<Long> issueIds = new ArrayList<Long>(){{
            add(param.getIssueId());
        }};

        List<ProductStageItem> stages = childIssueService.getStages(issueIds);

        if (null != productManager) {
            ProjectDetail _e = ProjectDetail.builder()
            .issueId(productManager.getIssueId())
            .cateId(productManager.getCateId())
            .department(productManager.getDepartment())
            .productProjectName(productManager.getProductProjectName())
            .projectName(productManager.getProjectName())
            .productType(productManager.getProductType())
            .customer(productManager.getCustomer())
            .fixedState(productManager.getFixedState().intValue())
            .plannedFixedDate(productManager.getProjectFixedDate())
            .mStatus(productManager.getMStatus().intValue())
            .state(productManager.getProductState().intValue())
            .level(productManager.getLevel().intValue())
            .projectLevelName(productManager.getProjectLevelName())
            .productOrProject(productManager.getProductOrProject())
            .productStageItems(stages)
            .productPlannedK0(productManager.getProductPlannedK0())
            .productPlannedM1(productManager.getProductPlannedM1())
            .productPlannedM2A(productManager.getProductPlannedM2A())
            .productPlannedM2z(productManager.getProductPlannedM2z())
            .productPlannedM3B(productManager.getProductPlannedM3B())
            .productPlannedM3z(productManager.getProductPlannedM3z())
            .productPlannedM4(productManager.getProductPlannedM4())
            .productPlannedM5(productManager.getProductPlannedM5())
            .productPlannedM6(productManager.getProductPlannedM6())
            .president(productManager.getPresident())
            .cto(productManager.getCto())
            .headOfTheInstitute(productManager.getHeadOfTheInstitute())
            .productManagerNo(productManager.getProductManagerNo())
            .infoCommissioner(productManager.getInfoCommissioner())
            .productCenterManager(productManager.getProductCenterManager())
            .productTechMajordomo(productManager.getProductTechMajordomo())
            .productManager(productManager.getProductManager())
            .projectMajordomo(productManager.getProjectMajordomo())
            .productRPM(productManager.getProductRPM())
            .engineCenterManager(productManager.getEngineCenterManager())
            .structureCenterManager(productManager.getStructureCenterManager())
            .structureTechnologyRepresent(productManager.getStructureTechnologyRepresent())
            .commonCenterManager(productManager.getCommonCenterManager())
            .commonCenterRepresent(productManager.getCommonCenterRepresent())
            .officeManager(productManager.getOfficeManager())
            .productDQE(productManager.getProductDQE())
            .testManager(productManager.getTestManager())
            .testTechnologyRepresent(productManager.getTestTechnologyRepresent())
            .presidentName(productManager.getPresidentName())
            .ctoName(productManager.getCtoName())
            .headOfTheInstituteName(productManager.getHeadOfTheInstituteName())
            .infoCommissionerName(productManager.getInfoCommissionerName())
            .productCenterManagerName(productManager.getProductCenterManagerName())
            .productTechMajordomoName(productManager.getProductTechMajordomoName())
            .productManagerName(productManager.getProductManagerName())
            .projectMajordomoName(productManager.getProjectMajordomoName())
            .productRPMName(productManager.getProductRPMName())
            .engineCenterManagerName(productManager.getEngineCenterManagerName())
            .structureCenterManagerName(productManager.getStructureCenterManagerName())
            .structureTechnologyRepresentName(productManager.getStructureTechnologyRepresentName())
            .commonCenterManagerName(productManager.getCommonCenterManagerName())
            .commonCenterRepresentName(productManager.getCommonCenterRepresentName())
            .officeManagerName(productManager.getOfficeManagerName())
            .productDQEName(productManager.getProductDQEName())
            .testManagerName(productManager.getTestManagerName())
            .testTechnologyRepresentName(productManager.getTestTechnologyRepresentName())
            .productClassification(productManager.getProductClassification()+"")
            .sellRepresent(productManager.getSellRepresent())
            .structureType(productManager.getStructureType())
            //.scenario(productManager.getScenario())
            .sellRepresentName(productManager.getSellRepresentName())
            //.projectManager(productManager.getProjectManager())
            //.projectManagerName(productManager.getProjectManagerName())
            .productDistribute(productManager.getProductDistribute())
            .productDistributeName(productManager.getProductDistributeName())
            .productMajordomo(productManager.getProductMajordomo())
            .productMajordomoName(productManager.getProductMajordomoName())
            .productProjectManager(productManager.getProductProjectManager())
            .productProjectManagerName(productManager.getProductProjectManagerName())
            .instituteProjectManager(productManager.getInstituteProjectManager())
            .instituteProjectManagerName(productManager.getInstituteProjectManagerName())
            .vCylinderProductDepartment(productManager.getVCylinderProductDepartment())
            .vCylinderProductDepartmentName(productManager.getVCylinderProductDepartmentName())
            .build();

            _e.setProductEnginMulti(Arrays.asList(Optional.ofNullable(productManager.getProductEnginMulti()).orElse("").split(",")));
            _e.setProductEnginMultiName( Arrays.asList(Optional.ofNullable(productManager.getProductEnginMultiName()).orElse("").split(",")));


            _e.setEngineCenterRepresentMulti(Arrays.asList(Optional.ofNullable(productManager.getEngineCenterRepresentMulti()).orElse("").split(",")));
            _e.setEngineCenterRepresentMultiName( Arrays.asList(Optional.ofNullable(productManager.getEngineCenterRepresentMultiName()).orElse("").split(",")));
            

            _e.setResearchTechnologyRepresentMulti( Arrays.asList(Optional.ofNullable(productManager.getResearchTechnologyRepresentMulti()).orElse("").split(",")));
            _e.setResearchTechnologyRepresentMultiName(Arrays.asList(Optional.ofNullable(productManager.getResearchTechnologyRepresentMultiName()).orElse("").split(",")));


            _e.setTechnicalPlatformSupportMulti( Arrays.asList(Optional.ofNullable(productManager.getTechnicalPlatformSupportMulti()).orElse("").split(",")));
            _e.setTechnicalPlatformSupportMultiName( Arrays.asList(Optional.ofNullable(productManager.getTechnicalPlatformSupportMultiName()).orElse("").split(",")));


            _e.setDevicePlatformSupportMulti( Arrays.asList(Optional.ofNullable(productManager.getDevicePlatformSupportMulti()).orElse("").split(",")));
            _e.setDevicePlatformSupportMultiName( Arrays.asList(Optional.ofNullable(productManager.getDevicePlatformSupportMultiName()).orElse("").split(",")));


            _e.setProjectManagerMulti( Arrays.asList(Optional.ofNullable(productManager.getProjectManagerMulti()).orElse("").split(",")));
            _e.setProjectManagerMultiName( Arrays.asList(Optional.ofNullable(productManager.getProjectManagerMultiName()).orElse("").split(",")));
            
            _e.setResearchStructureRepresentMulti( Arrays.asList(Optional.ofNullable(productManager.getResearchStructureRepresentMulti()).orElse("").split(",")));
            _e.setResearchStructureRepresentMultiName( Arrays.asList(Optional.ofNullable(productManager.getResearchStructureRepresentMultiName()).orElse("").split(",")));
            
            _e.setResearchTestRepresentMulti( Arrays.asList(Optional.ofNullable(productManager.getResearchTestRepresentMulti()).orElse("").split(",")));
            _e.setResearchTestRepresentMultiName( Arrays.asList(Optional.ofNullable(productManager.getResearchTestRepresentMultiName()).orElse("").split(",")));

            List<ProductCateOptionBean> cates = new ArrayList<>();
            List<Cate> cateBeans;
            if(null != param.getIsJMArea() && param.getIsJMArea().equals(1L)){
                cateBeans = customfieldoptionService.cateList("jmProductCate");
            } else {
                cateBeans = customfieldoptionService.cateList("productCate");
            }
            if (!StrUtil.isEmpty(productManager.getProductCateMulti())) {
                Arrays.stream(productManager.getProductCateMulti().split(",")).forEach(str->{
                    Optional<Cate> cateOptional = cateBeans.stream().filter(cate->cate.getId().equals(Long.valueOf(str))).findFirst();
                    cateOptional.ifPresent(childCate->{
                        Optional<Cate> parentCateOptional = cateBeans.stream().filter(parentCate->parentCate.getId().equals(childCate.getPid())).findFirst();   
                        cates.add(
                            ProductCateOptionBean.builder()
                            .pid(parentCateOptional.isPresent() ? childCate.getPid() : 0L)
                            .id(childCate.getId())
                            .value(parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "->" + childCate.getValue() : childCate.getValue())
                            .build()
                        );
                    });
                });
            }
            _e.setProductCateOptionBeans(cates);
            return _e;
        }
        return null;
    }
    
}
