<template>
  <div class="product_width">
    <a-spin :spinning="loading">
      <!-- 筛选区域 start -->
      <!-- 筛选区域 end -->

      <!-- 表格 start -->
      <div class="table-wrapper">
        <a-table
          ref="table"
          :rowKey="record => record.issueId + record.productChildCate"
          :columns="columns"
          :dataSource="loadData"
        >
                <span slot="problemStatus" slot-scope="text">
							<div class="problem-status-show">
								<div class="circle"
                     :style="`background:${text == '绿灯' ? '#91cc75' : text == '红灯' ? '#ff3333' : text == '黄灯'||text == '黄灯TOP3' ? '#efeb73' : '#FFFFFF'}`">
								</div>
								{{ text== '黄灯TOP3'?'TOP3':''}}
							</div>
						</span>

          <span slot="simpleText" slot-scope="text">
							{{text?text:'-'}}
						</span>

          <span slot="clampText" slot-scope="text">
							<clamp :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :key="new Date()" />
						</span>
        </a-table>
      </div>
      <infoForm ref="infoForm" />
      <!-- 表格 end -->
    </a-spin>
  </div>
</template>

<script>

import infoForm from "./modal/infoForm"
import {moniterDetails} from "@/api/modular/system/jmChartManage"

import _ from "lodash"

import { clamp } from '@/components'

export default {
  components: {
    clamp,
    infoForm
  },
  data() {
    return {
      dataLines:{},
      queryparam: {
        cates: [],
        states: [],
        depts: [],
        keyword: null,
      },
      loading: true,
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          align: 'center',
          width: 50,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: '细分市场',
          dataIndex: 'productSplitName',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title: '产品名称',
          dataIndex: 'productName',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title: '产品状态',
          dataIndex: 'productStateName',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title: '问题维度',
          dataIndex: 'problemDimensionName',
          align: 'center',
          width: 75,
          scopedSlots: { customRender: 'problemDimensionName' }
        },
        {
          title: '升级灯',
          dataIndex: 'problemStatusName',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'problemStatus' }
        },
        {
          title: '问题描述',
          dataIndex: 'problemDescription',
          align: 'center',
          width: 250,
          scopedSlots: { customRender: 'clampText' }
        },
        {
          title: '原因分析',
          dataIndex: 'causeAnalysis',
          align: 'center',
          width: 250,
          scopedSlots: { customRender: 'clampText' }
        },
        {
          title: '解决措施',
          dataIndex: 'problemSolving',
          align: 'center',
          width: 250,
          scopedSlots: { customRender: 'clampText' }
        },
        {
          title: '问题进展',
          dataIndex: 'productProcess',
          align: 'center',
          width: 250,
          scopedSlots: { customRender: 'clampText' }
        },
        {
          title: '责任人',
          dataIndex: 'responsiblePersonName',
          align: 'center',
          width: 95,
        },
        {
          title: '提出日期',
          dataIndex: 'findDate',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title: '计划关闭日期',
          dataIndex: 'plannedCompletionDate',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        },
        {
          title:'实际关闭日期',
          dataIndex:'actualCompletionDate',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'simpleText' }
        }

      ],
      loadData: [],
      totalData: [],
      tablesScroll: { x: "100%", y: 500 },
      tableScroll: 100
    }
  },
  props: {
    // 表格高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },

  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "31px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.moniterDetails()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  },
  methods: {

    // 数据筛选
    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

      // 产品分类
      if (this.queryparam["cates"].length > 0) {
        filterData = filterData.filter(
          item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
        )
        if (this.queryparam["cates"].indexOf(2) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["depts"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
      }

      // 产品名称
      if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
        const temList = []
        this.searchParam.inputSearch.forEach(v => {
          if (v.keyword === "") return
          filterData.forEach(e => {
            if (e.productName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
              temList.push(e)
            }
          })
        })
        filterData = _.uniqWith(temList, _.isEqual)
      }

      if (this.queryparam.startDate != null) {
        filterData = filterData.filter(
          item =>
            Date.parse(item.initiationDate) >= this.queryparam.startDate &&
            Date.parse(item.initiationDate) < this.queryparam.endDate
        )
      }

      // 表格数据
      this.loadData = filterData
    },
    dateChange(date, dateString) {
      if (dateString[0] != null && dateString[0] != "") {
        this.queryparam.startDate = Date.parse(dateString[0])
      } else {
        this.queryparam.startDate = null
      }
      if (dateString[1] != null && dateString[1] != "") {
        this.queryparam.endDate = Date.parse(dateString[1])
      } else {
        this.queryparam.endDate = null
      }
      this.callFilter()
    },

    moniterDetails() {
      this.loading = true
      moniterDetails({})
        .then(res => {
          if (!res.success) return this.$message.error("错误提示：" + res.message, 1)

          this.totalData = JSON.parse(JSON.stringify(res.data))
          this.callFilter()
        })
        .finally(() => {
          this.loading = false
        })
    },
  }
}
</script>

<style lang="less" scoped="">
@import "./productoption.less";
:root {
  --height: 200px;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}


.select-box {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.select-box .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

.ellipsis-tip {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 这里是超出几行省略 */
  overflow: hidden;
}

.problem-status-show {
  justify-content: center;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.problem-status-show .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 固定列 */
/deep/ .ant-table tr td {
  background: #fff;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(1),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(2),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 50px;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(3),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: calc(50px + 100px);
  z-index: 10;
}
</style>
