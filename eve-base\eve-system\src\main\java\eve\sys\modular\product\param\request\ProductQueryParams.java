package eve.sys.modular.product.param.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductQueryParams implements Serializable {
    private Integer weekTime;
    private List<Long> cates;
    private List<Long> productCates;
    private List<Long> productClassification;
    private List<Integer> states;
    private List<Long> productDistribute;
    private List<Integer> ears;
    private List<Long> depts;
    private String startDate;
    private String endDate;
    private String keyword;
    private Long projectId;
    private Long isVcylinder;
    private Long isJMArea;
    private Long productOrProject;
    private List<Long> issueIds;
    private List<String> weekTimeLists;
}
