package eve.sys.modular.product.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import eve.core.context.login.LoginContextHolder;
import eve.core.exception.ServiceException;
import eve.sys.jiraModular.customTool.service.ICustomfieldoptionService;
import eve.sys.modular.feedback.entity.Feedback;
import eve.sys.modular.feedback.service.IFeedbackService;
import eve.sys.modular.product.param.*;
import eve.sys.modular.product.param.request.DocParams;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.topic.param.Cate;
import eve.sys.modular.topic.param.TopicListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductJiraService {

    @Resource
    private IFeedbackService feedbackService;

    public List<ProjectCateBean> getProjectHead() {
        List<ProjectCateBean> nodes = new ArrayList<>();

        /* nodes.add(new ProjectCateBean(12L, 0L, "productCateOptionBeans", "productCateOptionBeans", "产品分类", null, 200L,
                false,
                null)); */
        nodes.add(new ProjectCateBean(1L, 0L, "no", "no", "序号", null, 40L, true, null));

        nodes.add(new ProjectCateBean(12L, 0L, "productCateParent", "productCateParent", "产品", null, 90L,
        false,
        null));

        nodes.add(new ProjectCateBean(13L, 0L, "productCate", "productCate", "类别", null, 80L,
        false,
        null));

        /* nodes.add(new ProjectCateBean(14L, 0L, "productOrProject", "productOrProject", "产品/项目", null, 80L,
        false,
        null)); */


        nodes.add(new ProjectCateBean(2L, 0L, "productProjectName", "productProjectName", "产品名称", null, 50L, true,
                null));
        nodes.add(new ProjectCateBean(3L, 0L, "productManager", "productManager", "产品经理", null, 50L, true, null));
        nodes.add(new ProjectCateBean(4L, 0L, "customer", "customer", "客户", null, 50L, true, null));
        nodes.add(new ProjectCateBean(5L, 0L, "fixedState", "fixedState", "定点状态", null, 50L, true, null));
        //nodes.add(new ProjectCateBean(6L, 0L, "ah", "ah", "电芯容量", null, 50L, true, null));
        //nodes.add(new ProjectCateBean(7L, 0L, "size", "size", "尺寸", null, 80L, true, null));
        nodes.add(new ProjectCateBean(8L, 0L, "mstatus", "mstatus", "产品阶段", null, 50L, true, null));
        nodes.add(new ProjectCateBean(8L, 0L, "state", "state", "产品状态", null, 50L, true, null));
        nodes.add(new ProjectCateBean(9L, 0L, "produceFeedback", "produceFeedback", "制造反馈", null, null, true, null));
        nodes.add(new ProjectCateBean(10L, 0L, "sellFeedback", "sellFeedback", "销售反馈", null, null, true, null));
        nodes.add(new ProjectCateBean(11L, 0L, "supplyFeedback", "supplyFeedback", "供应链反馈", null, null, true, null));
        // nodes.add(new ProjectCateBean(9L, 0L, "performance", "performance", "性能表达",
        // null, 200L, true,null));
        return nodes;
    }

    @Resource
    private IProductManagerService productManagerService;

    public List<ProductProjectItem> getProducts(Long isVcylinder, Long isJMArea) {

        List<ProductProjectItem> projectItems = new ArrayList<>();

        List<ProductProjectItem> productProjectItems = new ArrayList<>();

        if (null != isVcylinder && isVcylinder.equals(1L)) {
            productProjectItems = productManagerService.getProducts(true, false);
        } else if (null != isJMArea && isJMArea.equals(1L)) {
            productProjectItems = productManagerService.getProducts(false, true);
        } else {
            productProjectItems = productManagerService.getProducts(false, false);
        }

        for (ProductProjectItem e : productProjectItems) {
            for (ProductCateOptionBean _e : e.getProductCateOptionBeans()) {
                String[] split = _e.getValue().split("->");
                ProductProjectItem _item = ProductProjectItem.builder()
                        .issueId(e.getIssueId())
                        .catepid(_e.getPid())
                        .departmentOptionList(e.getDepartmentOptionList())
                        .cateId(_e.getId())
                        .productClassification(e.getProductClassification())
                        .initiationDate(e.getInitiationDate())
                        .issueKey(e.getIssueKey())
                        .productProjectName(e.getProductProjectName())
                        .projectName(e.getProjectName())
                        .productOrProject(e.getProductOrProject())
                        .productManager(e.getProductManager())
                        .customer(e.getCustomer())
                        .fixedState(e.getFixedState())
                        .plannedFixedDate(e.getPlannedFixedDate())
                        .mStatus(e.getMStatus())
                        .state(e.getState())
                        .productLevel(e.getProductLevel())
                        .researchProjectManager(e.getResearchProjectManager())
                        .showPlan(false)
                        .productCateOptionBeans(e.getProductCateOptionBeans())
                        .productStageItems(e.getProductStageItems())
                        .cateIdKey(_e.getId() + "")
                        .catePidKey(_e.getPid().equals(0L) ? _e.getId() + "" : _e.getPid() + "")
                        .build();

                _item = split.length > 1 ? _item.toBuilder().productCateParent(split[0]).productCate(split[1]).build()
                : _item.toBuilder().productCateParent(split[0]).productCate("").build();

                projectItems.add(_item);
            }
        }

        return projectItems;
    }

    public JSONObject getProjects(ProductProjectItem item) {

        JSONObject resp = new JSONObject();

        List<ProductProjectItem> projectItems = getProducts(item.getIsVcylinder(),item.getIsJMArea());

        ProductProjectItem _item = projectItems.stream().filter(e->e.getIssueId().equals(76609L)).findFirst().orElse(null);

        if (projectItems.size() == 0) {
            return resp;
        }

        if (null!= item && null != item.getCateId()) {
            projectItems = projectItems
            .stream()
            .filter(
                e->e.getProductCateOptionBeans()
                .stream()
                .filter(
                    _e->_e.getPid().equals(item.getCateId())
                    || _e.getId().equals(item.getCateId())
                )
                .findFirst()
                .isPresent()
            )
            .collect(Collectors.toList());
        }

        List<Long> issueIds = projectItems.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());

        List<Feedback> feedbacks = feedbackService.getByIssueId(issueIds);

        projectItems = projectItems.stream().sorted(Comparator.comparing(ProductProjectItem::getProductCateParent).thenComparing(ProductProjectItem::getProductCate).thenComparing(ProductProjectItem::getProductProjectName)).collect(Collectors.toList());

        List<Integer> index = new ArrayList<Integer>(1) {
            {
                add(1);
            }
        };

        projectItems.forEach(e -> {
            int _i = index.get(0);
            e.setNo(_i + "");
            _i++;
            index.set(0, _i);

            Optional<Feedback> saleFeedback = feedbacks.stream().filter(_e -> _e.getIssueId().equals(e.getIssueId())
                            && _e.getFeedbackType() == 2).findFirst();

            Optional<Feedback> madeFeedback = feedbacks.stream().filter(_e -> _e.getIssueId().equals(e.getIssueId())
                            && _e.getFeedbackType() == 1).findFirst();

            Optional<Feedback> supplyFeedback = feedbacks.stream().filter(_e -> _e.getIssueId().equals(e.getIssueId())
                            && _e.getFeedbackType() == 3).findFirst();

             e.setSellFeedback(saleFeedback.isPresent() ? saleFeedback.get().getFeedbackContent() : "");
             e.setProduceFeedback(madeFeedback.isPresent() ? madeFeedback.get().getFeedbackContent() : "");
             e.setSupplyFeedback(supplyFeedback.isPresent() ? supplyFeedback.get().getFeedbackContent() : "");
         });

        resp.put("columns", getProjectHead());
        resp.put("rows", projectItems);

        return resp;
    }


    public List<ProjectCateBean> getFacsTree() {
        List<ProjectCateBean> nodes = new ArrayList<>();
        nodes.add(new ProjectCateBean(1L, 0L, "id", "id", "序号", null, null, true, null));
        nodes.add(new ProjectCateBean(2L, 0L, "customer", "customer", "客户", null, null, true, null));
        nodes.add(new ProjectCateBean(3L, 0L, "dept", "dept", "事业部", null, null, true, null));
        nodes.add(new ProjectCateBean(4L, 0L, "factory", "factory", "工厂", null, null, true, null));
        nodes.add(new ProjectCateBean(5L, 0L, "ppm", "ppm", "PPM", null, null, true, null));
        nodes.add(new ProjectCateBean(6L, 0L, "productLine", "productLine", "产线", null, null, true, null));
        nodes.add(new ProjectCateBean(7L, 0L, "productCap", "productCap", "设计产能(GWh)", null, null, true, null));
        nodes.add(new ProjectCateBean(8L, 0L, "sop", "sop", "sop", null, null, true, null));
        return nodes;
    }

    /* public JSONObject getFacsData(String token, DocParams docParams) {
        JSONObject resp = new JSONObject();
        resp.put("columndata", getFacsTree());

        JSONObject jiraResp = Utils.doGet(
                JiraApiParams.FacsApi + docParams.getIssueId() + "?auth={auth}&token={token}",
                token, null);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }
        List<SupplierBean> supplierBeans = JSONObject.parseArray(jiraResp.getString("value"),
                SupplierBean.class);

        for (int i = 0, j = supplierBeans.size(); i < j; i++) {
            supplierBeans.get(i).setId(Long.valueOf(i + 1));
        }
        resp.put("rowdata", supplierBeans);
        return resp;
    } */


    public ProjectDetail getProjectDetail(DocParams docParams) {

        

        ProjectDetail projectDetail = null ;

        if (docParams.getIssueId() == 0) {
            projectDetail = ProjectDetail.builder().projectLevelName("/").productProjectName("CG01").issueId(0L).state(6).mStatus(11).build();
            return projectDetail;
        }

        /* if (null != docParams.getIsVcylinder() && docParams.getIsVcylinder().equals(1L)) { */
            projectDetail =  productManagerService.getProduct(ProductProjectItem.builder().issueId(docParams.getIssueId()).build());
        /* }else{

            JSONObject jiraResp = Utils.doGet(
                JiraApiParams.ProjectDetailApi + docParams.getIssueId() + "?auth={auth}&token={token}",
                token, null);

            if (!jiraResp.getBoolean("result")) {
                return null;
            }

            projectDetail = JSONObject.parseObject(jiraResp.getString("value"),
                ProjectDetail.class);
        } */

        //ProjectDetail projectDetail = productManagerService.getProduct(ProductProjectItem.builder().issueId(docParams.getIssueId()).build());

        return projectDetail;
    }

    /* public List<ProjectCateBean> getBomTree(String token, DocParams docParams) {

        List<ProjectCateBean> nodes = new ArrayList<>();
        LocalDate today = LocalDate.now();

        JSONObject jiraResp = Utils.doGet(
                JiraApiParams.FacsApi + docParams.getIssueId() + "?auth={auth}&token={token}",
                token, null);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }
        List<SupplierBean> supplierBeans = JSONObject.parseArray(jiraResp.getString("value"),
                SupplierBean.class);

        nodes.add(new ProjectCateBean(1L, 0L, "material", "material", "物料", "left", null, true, null));
        nodes.add(new ProjectCateBean(2L, 0L, "a", "a", "供应链", "left", null, true, null));
        nodes.add(new ProjectCateBean(3L, 0L, "b", "b", "研发", null, null, true, null));
        nodes.add(new ProjectCateBean(4L, 0L, "c", "c", "质量", null, null, true, null));
        nodes.add(new ProjectCateBean(5L, 0L, "d", "d", "工厂", null, null, true, null));


        nodes.add(new ProjectCateBean(6L, 2L, "supplierType", "supplierType", "供应商类别", null, null, true, null));
        nodes.add(new ProjectCateBean(7L, 2L, "materialNo", "materialNo", "物料料号", null, null, true, null));
        nodes.add(new ProjectCateBean(8L, 2L, "supplierCode", "supplierCode", "供应商代码", null, null, true, null));

        nodes.add(new ProjectCateBean(9L, 3L, "techTreaty", "techTreaty", "技术协议", null, null, true, null));
        nodes.add(new ProjectCateBean(10L, 3L, "materialProc", "materialProc", "材料测试协议", null, null, true, null));

        nodes.add(new ProjectCateBean(11L, 4L, "ppap", "ppap", "PPAP", null, null, true, null));
        nodes.add(new ProjectCateBean(12L, 4L, "e", "e", "试产状态", null, null, true, null));

        nodes.add(new ProjectCateBean(13L, 5L, "f", "f", "物料需求量", null, null, true, null));
        nodes.add(new ProjectCateBean(14L, 5L, "g", "g", "物料供应量", null, null, true, null));
        nodes.add(new ProjectCateBean(15L, 5L, "h", "h", "供需匹配状态", null, null, true, null));



        nodes.add(new ProjectCateBean(19L, 13L, "materialDemand" + today.getYear(), "materialDemand" + today.getYear(),
                today.getYear() + "", null, null, true, null));
        nodes.add(new ProjectCateBean(20L, 13L, "materialDemand" + (today.getYear() + 1),
                "materialDemand" + (today.getYear() + 1), (today.getYear() + 1) + "", null, null, true, null));

        nodes.add(new ProjectCateBean(21L, 14L, "materialSupply" + today.getYear(), "materialSupply" + today.getYear(),
                today.getYear() + "", null, null, true, null));
        nodes.add(new ProjectCateBean(22L, 14L, "materialSupply" + (today.getYear() + 1),
                "materialSupply" + (today.getYear() + 1), (today.getYear() + 1) + "", null, null, true, null));

        nodes.add(new ProjectCateBean(23L, 15L, "i", "i", today.getYear() + "", null, null, true, null));
        nodes.add(new ProjectCateBean(24L, 15L, "j", "j", (today.getYear() + 1) + "", null, null, true, null));
        int i = 1;
        for (SupplierBean supplierBean : supplierBeans) {

            String fac = supplierBean.getFactory();
            nodes.add(
                    new ProjectCateBean(Long.valueOf(15 + i), 12L, fac, fac, fac, null, null, true, null));

            nodes.add(new ProjectCateBean(Long.valueOf(24 + i), 23L, "demandstatus" + fac + today.getYear(),
                    "demandstatus" + fac + today.getYear(), fac, null, null, true, null));
            nodes.add(new ProjectCateBean(Long.valueOf(27 + i), 24L, "demandstatus" + fac + (today.getYear() + 1),
                    "demandstatus" + fac + (today.getYear() + 1), fac, null, null, true, null));

            i++;
        }

        return nodes;
    } */

    public void productMsgUpdate(String token, Map<Object,Object> updateMap) {

        if (updateMap.containsKey("isVcylinder")){

            if (updateMap.get("productDistribute").equals("1")) {
                updateMap.put("productMultiCate", "20048");
            }
            if (updateMap.get("productDistribute").equals("2")) {
                updateMap.put("productMultiCate", "20049");
            }
            if (updateMap.get("productDistribute").equals("3")) {
                updateMap.put("productMultiCate", "22357");
            }

            updateMap.put("department", "18864");
        }

        updateMap.remove("isVcylinder");

        JSONObject resp = new JSONObject();
        Map<String, Object> map = new HashMap<String, Object>(1){{
            put("issueId", updateMap.get("issueId"));
            put("userName", LoginContextHolder.me().getSysLoginUserAccount());
            put("map",updateMap);
        }};
        resp = Utils.doPostAndToken(JiraApiParams.updateProductMsgApi, token, map);
        if(resp.getBoolean("result")){

        }else{
            throw new ServiceException(500,resp.getString("message"));
        }

    }

    public Boolean createProduct(Map<Object,Object> param, String token) {

        if (param.containsKey("isVcylinder")){

            if (param.get("productDistribute").equals(1)) {
                param.put("productMultiCate", "20048");
            }
            if (param.get("productDistribute").equals(2)) {
                param.put("productMultiCate", "20049");
            }
            if (param.get("productDistribute").equals(3)) {
                param.put("productMultiCate", "22357");
            }

            param.put("department", "18864");
        }

        param.remove("isVcylinder");

        Map<String, Object> params = new HashMap<String, Object>(4) {
            {
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map",param);
            }
        };

        log.info("创建产品参数"+JSON.toJSONString(params));

        JSONObject resp = Utils.doPostAndToken(JiraApiParams.createProduct, token, params);

        log.info("创建产品返回结果"+JSON.toJSONString(resp));

        if(resp.getBoolean("result")){
            return true;

        }else{
            throw new ServiceException(500,resp.getString("message"));
        }
    }

    public List<Cate> getCylinderDepts(String token){

        Map<String, String> map = new HashMap<String, String>(1){{
            put("fieldName", "vCylinderProductDepartment");
        }};

        JSONObject jsonob = Utils.doGet(JiraApiParams.CylinderDeptApi, token, map);

        return JSONObject.parseArray(jsonob.getString("value"),Cate.class);

    }

    @Resource
    private ICustomfieldoptionService customfieldoptionService;
    public List<TreeSelectParam> getCatesTree(TopicListParam param) {

        List<Cate> cateBeans = customfieldoptionService.cateList(param.getFieldName());//  JSONObject.parseArray(jsonob.getString("value"),Cate.class);

        if (param.getFieldName().equals("department")) {
            cateBeans = cateBeans
            .stream().filter(e -> !e.getId().equals(1L) && !e.getId().equals(2L))
            .sorted(Comparator.comparing(Cate::getSequence))
            .collect(Collectors.toList());

            List<Long> deptIds = Arrays.asList(18863L,18846L,22492L,22487L,18711L,22101L,22269L,22105L);
            cateBeans = cateBeans.stream().filter(e->deptIds.indexOf(e.getId()) != -1).collect(Collectors.toList());

            if (null != param.getFlag()) {
                cateBeans = cateBeans
                .stream().filter(e -> e.getPid().equals(1L))
                .sorted(Comparator.comparing(Cate::getSequence))
                .collect(Collectors.toList());
            }
        }else{
            cateBeans = cateBeans
            .stream().filter(e->!e.getId().equals(1L) && !e.getId().equals(2L))
            .sorted(Comparator.comparing(Cate::getSequence))
            .collect(Collectors.toList());
        }


        List<TreeSelectParam> treeSelectParams = new ArrayList<>();
        for (Cate e : cateBeans) {
            treeSelectParams.add(null == param.getFlag() ? TreeSelectParam.builder()
                    .key(e.getId() + "")
                    .value(e.getId() + "")
                    .pid(e.getPid().equals(null) || e.getPid().equals(0L) ? "" : e.getPid() + "")
                    .selectable(e.getPid().equals(null) || !e.getPid().equals(1L) )
                    .title(e.getValue())
                    .children(new ArrayList<>())
                    .build() :
                    TreeSelectParam.builder()
                    .key(e.getId() + "")
                    .value(e.getId() + "")
                    .pid(e.getPid().equals(null) || e.getPid().equals(0L) ? "" : e.getPid() + "")
                    .title(e.getValue())
                    .children(new ArrayList<>())
                    .build());
        }
        treeSelectParams =  Utils.buildCateTree(treeSelectParams);

        if (param.getFieldName().equals("productCate") && param.getIsVcylinder().equals(1L)) {
            treeSelectParams = treeSelectParams.stream().filter(e->e.getKey().equals("20047")).collect(Collectors.toList());
        }else if(param.getFieldName().equals("productCate") && param.getIsVcylinder().equals(0L)){
            treeSelectParams = treeSelectParams.stream().filter(e->!e.getKey().equals("20047")).collect(Collectors.toList());
        }

        return treeSelectParams;
    }

}
