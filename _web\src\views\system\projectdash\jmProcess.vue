<template>
  <div class="container">
    <!-- 面包屑 start -->
    <div class="breadcrumb">
      <a-breadcrumb separator=">">
        <a-breadcrumb-item class="hand">
          <router-link :to="homepath"><a-icon class="rollback-icon" type="rollback" />首页看板</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>{{ $route.query.dept }}项目进展</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <!-- 面包屑 end -->

    <!-- 内容 start -->
    <div class="content-wrapper">
      <div class="table-page-search-wrapper mb0">
        <a-form layout="inline">
          <a-row :gutter="32">
            <a-col :md="7" :sm="24">
              <a-form-item label="项目状态">
                <a-select  v-model="projectStatu" mode="multiple" placeholder="请选择项目状态" @change="handleQuery">
                  <a-select-option v-for="(item, index) in statusOption" :key="index" :value="item.value">
                    <div class="select-box">
                      <div class="circle" :style="`background:${item.color}`"></div>
                      {{ item.label }}
                    </div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" has-feedback>
                <a-select
                  v-model="queryparam.depts"
                  mode="multiple"
                  style="width: 100%"
                  placeholder="请选择所属部门"
                  @change="change"
                  :maxTagCount="2"
                  allowClear
                >
                  <a-select-option v-for="i in deptsOptions" :value="i.id" :key="i.label">
                    {{ i.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="产品名称">
                <a-input
                  size="small"
                  class="filter-input"
                  @keyup.enter.native="handleQuery"
                  v-model="queryparam.productProjectName"
                  placeholder="请输入产品名称"
                >
                  <a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <a-form-item label="项目名称">
                <a-input
                  size="small"
                  class="filter-input"
                  @keyup.enter.native="handleQuery"
                  v-model="queryparam.projectName"
                  placeholder="请输入项目名称"
                >
                  <a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :md="1" :sm="24" :style="{ float: 'right' }">
              <div class="table-page-search-submitButtons" :style="{ float: 'right' ,marginTop:'4px'}">
                <a-button size="small" style="margin-left: 120px;" type="primary" @click="handleQuery">查询</a-button>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 表格 start -->
      <div class="table-wrapper">
        <a-table
          :rowKey="record => record.issueId"
          :pagination="pagination"
          :data-source="data"
          :columns="columns"
          :loading="loading"
          :style="`height:${tableHeight}px;`"
          size="middle"
        >
          <template slot="delayDays" slot-scope="text, record">
            <span v-if="record.productState == 7 || record.productState == 8">-</span>
            <span v-else>{{ text }}</span>
          </template>

          <!-- 项目进度 start -->
          <span slot="projectStatu" slot-scope="text, record">
						<!-- state 7 : 暂停 8 : 停产  -->
						<div v-if="record.productState == 7 || record.productState == 8" class="select-box">
							<div class="circle" style="background:#afc4d3"></div>
							停止
						</div>
						<div v-else-if="record.delayDays >= 1" class="select-box">
							<div class="circle" style="background:#fd8585"></div>
							逾期
						</div>
            <!-- <div v-else-if="14 > record.delayDays && record.delayDays >= 7" class="select-box">
              <div class="circle" style="background:#fac714"></div>
              逾期7-14天
            </div> -->
						<div v-else class="select-box">
							<div class="circle" style="background:#50a1f8"></div>
							正常
						</div>
					</span>
          <!-- 项目进度 end -->
        </a-table>
      </div>
      <!-- 表格 end -->
    </div>
    <!-- 内容--所 end -->
  </div>
</template>

<script>
import { getProcessForProject } from "@/api/modular/system/jmChartManage"
import {clamp} from '@/components'
import _ from "lodash"
import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";
import Treeselect from "@riophae/vue-treeselect";
export default {
  components:{
    clamp,
    Treeselect
  },
  data() {
    return {
      homepath:'',
      loading: false,
      //  40 顶部栏 20 页面padding 20 面包屑  48 标题 16 内页面padding
      tableHeight: document.documentElement.clientHeight - 40 - 20 - 20 - 80 - 5,
      chartHeight: document.documentElement.clientHeight - 40 - 20 - 20 - 26,
      data: [],
      deptFilters: [],
      cateFilters: [],
      nameFilters: [],
      pdFilters: [],
      rpmFilters: [],
      custFilters: [],
      dateFilters: [],
      dayFilters: [],
      columns: [
        {
          title: "序号",
          dataIndex: "seq",
          align: "center",
          width: 50,
          customRender: (text, record, index) => <span>{index + 1}</span>
        },
        {
          title: "产品名称",
          dataIndex: "productProjectName",
          align: "center"
        },
        {
          title: "项目名称",
          dataIndex: "projectName",
          align: "center"
        },
        {
          title: "项目等级",
          dataIndex: "projectLevelName",
          align: "center"
        },
        {
          title: "状态",
          dataIndex: "projectStatu",
          align: "center",
          scopedSlots: { customRender: "projectStatu" }
        },
        {
          title: "项目阶段",
          dataIndex: "productStageName",
          align: "center",
        },
        {
          title: "计划时间",
          dataIndex: "planDate",
          align: "center"
        },
        {
          title: "逾期天数",
          dataIndex: "delayDays",
          align: "center",
          scopedSlots: { customRender: "delayDays" }
        },
        {
          title: "停止日期",
          dataIndex: "stopTime",
          align: "center",
        },
        {
          title: "PD",
          dataIndex: "productManagerName",
          align: "center"
        },
        {
          title: "研究所",
          dataIndex: "parentDeptName",
          align: "center"
        }
      ],
      depts: [],
      // 分页
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: (current, size) => {
          this.pagination.current = current
          this.pagination.pageSize = size
        },
        onShowSizeChange: (current, pageSize) => {
          this.pagination.current = 1
          this.pagination.pageSize = pageSize
        }
      },
      labelCol: {
        xs: {
          span: 8
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 14
        }
      },
      // xiaodong
      queryparam: {
        depts: []
      },
      projectStatu: [],
      statusOption: [
        { value: 1, label: "正常", color: "#50a1f8" },
        //{ value: 3, label: "逾期7-14天", color: "#fac714" },
        { value: 2, label: "逾期", color: "#fd8585" },
        { value: 4, label: "停止", color: "#afc4d3" }
      ],
      departmentOption: [],
      deptsOptions: [],

    }
  },
  mounted() {
    this.homepath = '/jm_product_chart'
    if (this.$route.query.projectStatu) {
      switch (this.$route.query.projectStatu) {
        case "正常":
          this.projectStatu = [1]
          break
        case "停止":
          this.projectStatu = [4]
          break
        case "逾期":
          this.projectStatu = [2]
          break
      }
    }

    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 47}px`)
    document.documentElement.style.setProperty(`--chartHeight`, `${this.chartHeight}px`)

    // 获取数据
    if (this.$route.query.deptId) {
      this.getProcessForProject(this.$route.query.deptId)
    } else {
      this.getProcessForProject(null)
    }
    this.getDeptJiraOptionList();
  },
  computed: {
    defaultSelect() {
      // 假设你想默认选中所有部门
      let filter = this.deptsOptions.map(dept => dept.id).filter(e => e == this.$route.query.deptId);
      return filter;
    }
  },
  watch: {
    data(newVal, oldVal) {
      if (this.data.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.tableHeight - 47}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "80px")
      }
    },
    defaultSelect(newVal) {
      // 当defaultSelect更新时，更新queryparam.depts
      this.$nextTick(() => {
        this.queryparam.depts = newVal;
      });
    }
  },
  methods: {
    getDeptJiraOptionList() {
      /*      getJiraOptionList({ fieldName: 'department' }).then(res => {
              if (res.success) {
                let depts = []
                let _depts = ["18846", "22492", "22487", "18711", "22101", "22269", "22105"]
                let parentNodes = res.data.filter(o => o.parentoptionid == null)
                for (const item of parentNodes) {
                  if (_depts.indexOf(item.id) == -1) {
                    continue
                  }
                  let _item = {
                    id: (item.id),
                    label: item.customvalue
                  }
                  depts.push(_item)
                }
                console.log("depts",depts)
                this.deptsOptions = depts
                // 确保请求完成后设置默认值
                this.queryparam.depts = this.defaultSelect;
              }
            })*/
      this.deptsOptions = [
        {
          id: '22269',
          label: '储能所',
        }, {
          id: '18711',
          label: '铁锂所',
        }, {
          id: '22487',
          label: '锰铁锂所',
        }
      ];
      this.queryparam.depts = this.defaultSelect;
    },
    change(val) {
      this.handleQuery();
    },
    // 获取数据
    getProcessForProject(deptId) {
      this.loading = true
      let param = {underDev:this.$route.query.underDev,...this.queryparam}
      if(deptId){
        param.deptId = deptId
      }
      getProcessForProject(param)
        .then(res => {
          if (res.success) {
            this.data = res.data

            if (this.projectStatu.length !== 0) {
              const temData = []
              this.projectStatu.forEach(value => {
                // 1:正常
                if (value == 1) {
                  this.data.forEach(v => {
                    if (v.productState < 7 && parseInt(v.delayDays) < 1) {
                      temData.push(v)
                    }
                  })
                }
                // 逾期大于等于14
                if (value == 2) {
                  this.data.forEach(v => {
                    if (v.productState < 7 && parseInt(v.delayDays) >= 1) {
                      temData.push(v)
                    }
                  })
                }
                // 逾期7-14
                /* if (value == 3) {
                  this.data.forEach(v => {
                    if (v.productState < 7 && parseInt(v.delayDays) >= 7 && parseInt(v.delayDays) < 14) {
                      temData.push(v)
                    }
                  })
                } */
                // 停止
                if (value == 4) {
                  this.data.forEach(v => {
                    if (v.productState == 7 || v.productState == 8) {
                      temData.push(v)
                    }
                  })
                }
              })
              this.data = temData
              this.pagination.current = 1
            }
          } else {
            this.$message.error("错误提示：" + res.message, 1)
          }
        })
        .catch(err => {
          this.$message.error("错误提示：" + err.message, 1)
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 搜索
    handleQuery() {
      this.getProcessForProject(null)
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  margin-left: -40px;
}

// 图表
.board {
  display: flex;
  margin-bottom: 10px;
}

.col1 {
  width: 50%;
}

.col2 {
  width: 50%;
}

.head {
  position: absolute;
  top: 12px;
  left: 12px;
}

// select 选项
.select-box {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}
.select-box .circle {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 8px;
}

// 内容
.content-wrapper {
  background: #fff;
  border-radius: 10px;
  padding: 10px;
}

// 筛选框高度
.filter-input {
  height: 32px !important;
}

.item {
  padding: 8px;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

// 面包屑
/deep/.ant-breadcrumb {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65) !important;
  margin: 0 12px 12px;
}

/deep/.ant-table-placeholder {
  padding: 0;
}

// 表头
/deep/.ant-table-thead > tr > th {
  font-size: 13px;
  background: #f5f5f5 !important;
  color: #666;
}

// 表格内容
/deep/.ant-table-tbody {
  background: #fff;
  color: #333;
}

// 表头icon
/deep/.ant-table-thead > tr > th .anticon-filter,
/deep/.ant-table-thead > tr > th .ant-table-filter-icon {
  color: #999;
}

/deep/.ant-checkbox-group-item {
  display: block;
  margin: 0 8px;
}

:root {
  --height: 600px;
  --chartHeight: 600px;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}

// 表头固定
/deep/.ant-table-thead {
  position: sticky;
  top: 0;
}

/* 主标题 */

.head-title {
  color: #333;
  padding: 10px 0;
  font-size: 18px;
  font-weight: 600;
}

.head-title::before {
  width: 8px;
  background: #1890ff;
  margin-right: 8px;
  content: "\00a0"; /* 填充空格 */

  color: #5aaef4;
}

// 查看所有--图表
.chart-wrapper {
  padding: 8px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
}

/deep/.filter-form{
  height: 26px !important;
}

.chart-wrapper .chart_table {
  width: 100%;
  height: var(--chartHeight) !important;
}

// 筛选框
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
  margin-bottom: 10px;
}

// 去除表格空状态边框
/deep/.ant-table-placeholder {
  border: none;
}

/deep/.ant-select-selection--multiple {
  max-height: 32px !important;
  overflow: auto;
}

/deep/.ant-input-affix-wrapper .ant-input {
  height: 32px !important;
}

.searchItem{
  height: 32px !important;
  display: flex;
  align-items: center;
  flex: 1;
}
.searchItem span{
  margin-right: 10px;
  font-size: 12px;
  color: #000;
}
/deep/.searchItem .ant-calendar-picker{
  flex: 1;

}

/deep/.searchItem .ant-input-affix-wrapper{
  flex: 1;
}

/deep/.ant-table-pagination.ant-pagination{
  margin: 16px 0 0;
}
</style>
