<template>
  <div class="dashboard-container">
    <a-spin :spinning="loading">
      <!-- 提示框 -->
      <div
        ref="floatTip"
        class="float-tip"
        :show="floatTipData.show"
        :style="floatTipData.style"
      >
        <p class="title">{{ floatTipData.text }}</p>
        <p v-for="(item, i) in tipDatas" :key="i">{{ depts[i] }}:{{ item[i] }}%</p>
      </div>
      <div class="head_title">
        <img src="~@/assets/dashboard/right.svg" alt="" />
        <span>产品项目管理平台</span>
        <img src="~@/assets/dashboard/left.svg" alt="" />
      </div>
      <!-- 顶部卡片 -->
      <div class="top-cards">
        <div class="card">
          <div class="card-content">
            <div class="card-icon blue-icon tag-1">

            </div>
            <div class="card-info">
              <div class="card-tips">
                <div class="card-title">产品总数(个)</div>
                <div class="card-value">{{this.chartDatas.productCount||0}}</div>
              </div>

							<div class="card-details">
								<div class="detail-item" v-for="(item,i) in this.chartDatas.proudctLevel" :key="i">
									<div>{{item.name}}</div>
									<div>{{item.value||0}}</div>
								</div>

							</div>
						</div>
					</div>
				</div>
				<div class="card">
					<div class="card-content">
						<div class="card-icon blue-icon tag-2">
							
						</div>
						<div class="card-info">
							<div class="card-tips">
								<div class="card-title">项目总数(个)</div>
								<div class="card-value">{{this.chartDatas.projectCount||0}}</div>
							</div>

							<div class="card-details">

								<div class="detail-item" v-for="(item,i) in this.chartDatas.projectLevel" :key="i">
									<div>{{item.name}}</div>
									<div>{{item.value||0}}</div>
								</div>

							</div>
						</div>
					</div>
				</div>
				<div class="card">
					<div class="card-content progress">
						<div class="card-icon blue-icon tag-2">
							
						</div>
						<div class="card-info progress">
							<div class="card-tips">
								<div class="card-title">项目进度(个)</div>
							</div>

              <div class="card-details">
                <div class="detail-item" v-for="(item,i) in this.chartDatas.projectProcess" :key="i"
                     :style="{cursor: 'pointer'}"  @click="$router.push({
										path:'/project_process',
										query: {
											projectStatu: processStatus[i]
										}
									})">
                  <div>
                    <div class="circle-dot big" :class="i == 0 ? '': (i == 1 ? 'yellow': 'grey')">
                      <div class="small"></div>
                    </div>
                    <span>{{ i == 0 ? '正常' : (i == 1 ? '逾期' : '停止')}}</span>
                  </div>
                  <div>{{item.value||0}}</div>
                </div>

              </div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-content last">
            <div class="card-icon blue-icon tag-3">

            </div>
            <div class="card-info">
              <div class="card-tips" :style="{cursor: 'pointer'}" @click="$router.push('/project_alter')">
                <div class="card-title">产品变更数(个)</div>
                <div class="card-value">{{this.chartDatas.productAltersCount||0}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表网格 -->
      <div class="charts-grid">
        <!-- 第一行图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 产品类别
            </div>
            <div class="chart-unit">单位: 个</div>
            <div class="chart-view" @click.prevent="toReport" >产品矩阵图 ></div>
          </div>
          <div ref="pieChart1" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 产品状态
            </div>
            <div class="chart-unit">单位: 个</div>
          </div>
          <div ref="barChart1" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 项目进度
            </div>
            <div class="chart-unit">单位: 个</div>
          </div>
          <div ref="stackedBar1" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 动态监控
            </div>
            <div class="chart-unit">单位: 个</div>
          </div>
          <div class="chart-empty">
            <a-empty :image="simpleImage">
              <span slot="description">暂无内容</span>
            </a-empty>
          </div>
        </div>

        <!-- 第二行图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 产品等级
            </div>
            <div class="chart-unit">单位: 个</div>
          </div>
          <div ref="pieChart2" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 产品变更
            </div>
            <div class="chart-unit">单位: 个</div>
            <div class="tabs">
              <span class="active">C样后</span>
              <span>A/B样</span>
            </div>
          </div>
          <div ref="stackedBar2" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 项目费用<!-- <span class="chart-tip">(功能开发中)</span> -->
            </div>
            <div class="chart-unit">单位: 个</div>
          </div>
          <div ref="stackedBar3" class="chart-item"></div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">
              <img src="~@/assets/dashboard/left-arrow.svg" /> 研发质量
            </div>
          </div>
          <div class="gauge-charts">
            <div class="gauge-item"
                 @mouseover="showFloatTip('问题关闭率',chartDatas.countQualityProblemByDept)"
                 @mouseout="hideTip"
            >
              <div ref="gaugeChart1" class="gauge-chart"></div>
              <div class="gauge-info" @click.prevent="toProblemDoc">
                <div class="gauge-title">问题关闭率</div>
                <div class="gauge-value">{{problemCloseCount}}%</div>
                <div class="gauge-link">查看详情 ></div>
              </div>
            </div>
            <div class="gauge-item"
                 @mouseover="showFloatTip('转阶段文件达成率',chartDatas.countStageByDept)"
                 @mouseout="hideTip">
              <div ref="gaugeChart2" class="gauge-chart"></div>

              <div class="gauge-info" @click.prevent="toStageDoc">
                <div class="gauge-title">转阶段文件达成率</div>
                <div class="gauge-value">{{stageDocsCount}}%</div>
                <div class="gauge-link">查看详情 ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { Empty } from 'ant-design-vue';
import {
	getProductStatics,
} from "@/api/modular/system/jmChartManage"


export default {
	name: 'jmProductDashboard',

  props: {
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },

  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  },
  data() {
    return {
      charts: [],
      processStatus:['正常','逾期','停止'],
      loading: true,
      chartDatas: {
        productAltersByDept: [],
        productCate: [],
        productAltersCount: 0,
        projectCount: 0,
        productCateByDept: [],
        projectLevel: [],
        projectProcessByDept: [],
        proudctLevel: [],
        productCount: 0,
        projectProcess: []
      },
      convertNum: 1,
      stageDocsCount: 0,
      problemCloseCount: 0,
      tipDatas: [],
      depts: ["储能所","铁锂所","锰铁锂所"],

      floatTipData: {
        show: false,
        text: "",
        style: {
          left: "",
          top: ""
        }
      }
    }
  },
  mounted() {
    this.getProductStatics()
  },
  created() {
    // 响应式处理
    window.addEventListener('resize', this.resetDimensions)

    this.resetDimensions()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resetDimensions)
    this.charts.forEach(chart => {
      chart.dispose()
    })
  },
  methods: {
    toProblemDoc(){
      this.$router.push({
        path: "/jm_problemBoard",
        query: {
          departmentCheck: '10700', //跳转质量，"部门（荆门院）"的id
          departmentCheckName: '储能电池研究所',
          isProduct:1,
        }
      })
    },
    toReport(){
      this.$router.push({
        path: "/jm_report_sum"
      })
    },
    toStageDoc(){
      this.$router.push({
        path: "/jm_project_stagedoc",
      })
    },

    /* getStageCount() {
      getStageCount({})
        .then(res => {
          if (!res.success) {
            return
          }

          if (res.data && res.data.length > 0) {
            let _pop = res.data.pop()
            this.stageDocsCount = _pop["count"]
            this.stageDocs = res.data

            this.$nextTick(()=>{
              this.mouseFollowFloatTip()
            })
          }
        })
        .finally(() => {
        })
    }, */
    getProductStatics(){
      this.loading = true
      getProductStatics().then(res => {
        if(!res.success){
          return
        }
        this.chartDatas = res.data

        /* this.$nextTick(()=>{
          this.initCharts()
        }) */

        let _pop = this.chartDatas.countStageByDept.pop()
        this.stageDocsCount = _pop["count"]

        _pop = this.chartDatas.countQualityProblemByDept.pop()
        this.problemCloseCount = _pop["count"]

        this.$nextTick(()=>{
          this.initCharts()
          this.mouseFollowFloatTip()
        })
      }).finally(() => {
        this.loading = false
      })
    },
    initCharts() {
      this.initPieChart1()
      this.initPieChart2()
      this.initBarChart1()
      this.initStackedBar1()
      this.initStackedBar2()
      this.initStackedBar3()
      this.initGaugeChart1()
      this.initGaugeChart2()
    },
    resizeCharts() {
      this.charts.forEach(chart => {
        chart.resize()
      })
    },
    getPieOption(legendData,colorsArr,datas){
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `${params.name} (${params.percent.toFixed(0)}%,${params.value})`
          }
        },
        legend: {
          y: '90%',
          itemWidth: this.convertFlexiblePx(9,2*9,12),
          itemHeight: this.convertFlexiblePx(9,2*9,12),
          icon: 'circle',
          textStyle: { fontSize: this.convertFlexiblePx(9,2*9,12) },
          data: legendData
        },
        color: colorsArr,
        series: [
          {
            type: 'pie',
            z: 10,
            minAngle: 15,
            radius: ['45%', '65%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 0
            },
            label: {
              show: true,
              color: "#333",
              formatter: function (params) {
                return `${params.name}\n\n${params.value}/${params.percent.toFixed(0)}%`
              },
              fontSize: this.convertFlexiblePx(9,2*9,12)
            },
            labelLine: {
              smooth: 0.6,
              length: 10,
              length2: 20
            },
            data: datas
          },
          {
            name: '外边框',
            type: 'pie',
            clockWise: false,
            radius: ['68%', '68%'],//边框大小
            center: ['50%', '45%'],//边框位置
            data: [{
              value: 10,
              itemStyle: {
                normal: {
                  borderRadius: 0,
                  borderWidth: 2,//设置边框粗细
                  borderColor: '#EBEFF3'//边框颜色
                }
              }
            }],
            labelLine:{
              normal:{
                length:45,
                length2:45,
                show: false    // 隐藏所有指示线
              }
            },
            z: 1,
            emphasis:{
              disabled:true,
            },
            tooltip:{
              show:false,
            },
            silent:true,
          },
        ]
      }
      return option
    },
    getStackedBarOption(legendDatas,xAxisDatas,colorsArr,datas){
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          x: 'left',
          y: 0,
          itemWidth: this.convertFlexiblePx(8,2*8,12),
          itemHeight: this.convertFlexiblePx(6,2*6,11),
          textStyle: { fontSize: this.convertFlexiblePx(9,2*9,12) },
          data: legendDatas
        },
        grid: {
          left: '0',
          right: '1',
          bottom: '0',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          showMinLabel: true,
          interval: 0,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',
            },
          },
          axisLabel: {
            color: "#333",
            textStyle: { fontSize: this.convertFlexiblePx(9,2*8,12) },
            overflow: "break",
          },
          axisTick: {
            show: false,
          },
          data: xAxisDatas//
        },
        yAxis: [{
          type: 'value',
          splitLine: { show: true },
          splitNumber: 5,
          axisLabel: {
            color: "#333",
            textStyle: { fontSize: this.convertFlexiblePx(8,2*8,12) },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',//坐标轴的颜色
            },
          },

        },{
          type: 'value',
          splitLine: { show: true },
          splitNumber: 5,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',//坐标轴的颜色
            },
          },

        }],
        color: colorsArr,
        series: [
        ]
      }

      this.initStackDataType(datas,option)
      return option

    },
    getBarChartOption(legendDatas,xAxisDatas,colorsArr,dataset){
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          x: 'left',
          y: 0,
          itemWidth: this.convertFlexiblePx(8,2*8,12),
          itemHeight: this.convertFlexiblePx(6,2*6,11),
          textStyle: { fontSize: this.convertFlexiblePx(9,2*9,12) },
          data: legendDatas
        },
        grid: {
          left: '0',
          right: '1',
          bottom: '0',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          showMinLabel: true,
          interval: 0,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',
            },
          },
          axisLabel: {
            color: "#333",
            textStyle: { fontSize: this.convertFlexiblePx(9,2*8,12) },
            overflow: "break",
          },
          axisTick: {
            show: false,
          },
          data: xAxisDatas
        },
        yAxis: [{
          type: 'value',
          splitLine: { show: true },
          axisLabel: {
            color: "#333",
            textStyle: { fontSize: this.convertFlexiblePx(8,2*8,12) },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',  //坐标轴的颜色
            },
          },
        },{
          type: 'value',
          splitLine: { show: true },
          axisLabel: {
            show:false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e2e7f2',  //坐标轴的颜色
            },
          },
        }],
        dataset: dataset,
        color: colorsArr,
        series: [
          {
            type: 'bar',
            barWidth: this.convertFlexiblePx(9,2*9,12),
            itemStyle: {
              normal:{
                barBorderRadius: [20, 20, 0, 0],
                label: {
                  show: true,
                  position: "top",
                  textStyle: {
                    color: "#333",
                    fontSize: this.convertFlexiblePx(8,2*6,11)
                  },
                },
              }
            }
          },
          {
            type: 'bar',
            barWidth: this.convertFlexiblePx(9,2*9,12),
            itemStyle: {
              normal:{
                barBorderRadius: [20, 20, 0, 0],
                label: {
                  show: true,
                  position: "top",
                  textStyle: {
                    color: "#333",
                    fontSize: this.convertFlexiblePx(8,2*6,11)
                  },
                },
              }
            }
          },
        ]
      }
      return option
    },
    gettGaugeChartOption(image,progressColor,axisColor,datas){
      const option = {
        graphic: {
          elements: [{
            type: "image",
            z: 3,
            style: {
              image: image,
              width: this.convertFlexiblePx(17,2*17,35),
              height: this.convertFlexiblePx(17,2*17,35)
            },
            left: 'center',
            top: 'center',
            position: [100, 100]// 根据需要调整位置
          }]
        },
        series: [
          {
            type: 'gauge',
            radius: '100%',
            startAngle: 90,
            endAngle: -270,
            pointer: {
              show: false
            },
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
              width: this.convertFlexiblePx(8,2*8,12),
              itemStyle: {
                color: progressColor
              }
            },
            axisLine: {
              lineStyle: {
                width: this.convertFlexiblePx(8,2*8,12),
                color: [
                  [1, axisColor]
                ]
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            detail: {
              show: false
            },
            data: datas
          }
        ]
      }
      return option
    },

    initStackDataType(datas,option){
      option.series = []
      for (const e of datas) {
        let _seriesOption = {
          name: e.name,
          type: 'bar',
          stack: 'a',
          barWidth: this.convertFlexiblePx(9,2*9,12),
          emphasis: { focus: 'series' },
          data:[]
        }
        for (const _e of e.datas) {
          _seriesOption.data.push(_e)
        }
        option.series.push(_seriesOption)
      }

      let newArr = [];
      let newServices = option.series;
      for (var i = 0; i < newServices.length; i++) {
        option.series[i].data.forEach(function(val, _i) {
          if (newArr[_i] == undefined || newArr[_i] == null) {
            newArr[_i] = 0;
          }
          newArr[_i] += val.value ? Number(val.value) : Number(val);
        });
      }

      option.series.push({
        name: '合计',
        type: 'bar',
        stack: 'b',
        barWidth: this.convertFlexiblePx(9,2*9,12),
        emphasis: { focus: 'series' },
        label:{
          normal:{
            show:true,
            position:'top',
            fontSize: this.convertFlexiblePx(8,2*6,11),
            color:'#333'
          }
        },
        z: 1,
        data: newArr,
        barGap:'-100%',
      })

      const stackInfo = {};
      for (let i = 0; i < option.series[0].data.length; ++i) {
        for (let j = 0; j < option.series.length; ++j) {
          const stackName = option.series[j].stack;
          if (!stackName) {
            continue;
          }
          if (!stackInfo[stackName]) {
            stackInfo[stackName] = {
              stackStart: [],
              stackEnd: []
            };
          }
          const info = stackInfo[stackName];
          const data = option.series[j].data[i];
          if (data && data != 0) {
            if (info.stackStart[i] == null) {
              info.stackStart[i] = j;
            }
            info.stackEnd[i] = j;
          }
        }
      }
      for (let i = 0; i < option.series.length; ++i) {
        const data = option.series[i].data;
        const info = stackInfo[option.series[i].stack];
        for (let j = 0; j < option.series[i].data.length; ++j) {
          const isEnd = info.stackEnd[j] === i;
          const topBorder = isEnd ? 20 : 0;
          data[j] = {
            value: data[j],
            itemStyle: {
              borderRadius: [topBorder, topBorder, 0, 0]
            }
          };
        }
      }
    },
    // 产品类别饼图
    initPieChart1() {
      const chart = echarts.init(this.$refs.pieChart1)
      this.charts.push(chart)
      const option = this.getPieOption(
        ['立项讨论', '新产品', '量产产品', '停止'],
        ['#2BD6AD', '#76ACE6', '#9996FF', '#D0D4E8'],
        this.chartDatas.productCate || []
        /* [
          { value: 129, name: '预研产品' },
          { value: 129, name: '新产品' },
          { value: 129, name: '量产产品' },
          { value: 129, name: '停止' }
        ] */
      )
      chart.setOption(option)
    },
    // 产品等级饼图
    initPieChart2() {
      const chart = echarts.init(this.$refs.pieChart2)
      this.charts.push(chart)
      const option = this.getPieOption(
        ['S级', 'A级', 'B级', 'C级'],
        ['#9996ff', '#76ACE6', '#7BE3E3', '#2BD6AD'],
        this.chartDatas.proudctLevel || []
      )
      chart.setOption(option)
    },
    // 产品状态柱状图
    initStackedBar1() {
      let that = this
      const chart = echarts.init(that.$refs.barChart1)
      that.charts.push(chart)

      const option = that.getStackedBarOption(
        ['立项讨论', '新产品', '量产产品', '停止'],
        ['储能所', '铁锂所', '锰铁锂所'],
        ['#2BD6AD', '#76ACE6', '#9996FF', '#D0D4E8'],
        that.chartDatas.productCateByDept || []
      )

      chart.on('legendselectchanged', function (event) {
        let datas = (that.chartDatas.productCateByDept || []).filter(item => {
          return event.selected[item.name]
        })
        that.initStackDataType(datas,option)
        chart.setOption(option)
      });
      chart.setOption(option)
    },
    // 项目进度堆叠图
    initStackedBar2() {
      let that = this
      const chart = echarts.init(that.$refs.stackedBar1)
      that.charts.push(chart)

      const option = that.getStackedBarOption(
        ['正常', '逾期', '停止'],
        ['储能所', '铁锂所', '锰铁锂所'],
        ['#50a1f8', '#fd8585','#afc4d3'],
        that.chartDatas.projectProcessByDept || []
      )

      chart.on('legendselectchanged', function (event) {
        let datas = (that.chartDatas.projectProcessByDept || []).filter(item => {
          return event.selected[item.name]
        })
        that.initStackDataType(datas,option)
        chart.setOption(option)
      });

      chart.on('click', function (params) {

        let deptIds = ['22269', '18711', '22487']
        that.$router.push({
          path: "/jm_project_process",
          query: {
            deptId: deptIds[params.dataIndex],
            projectStatu: params.seriesName.replace('进度',''),
            underDev: 1
          }
        })
      });

      chart.setOption(option)
    },
    // 项目费用堆叠图
    initStackedBar3() {
      let that = this
      const chart = echarts.init(that.$refs.stackedBar3)
      that.charts.push(chart)

      const option = that.getStackedBarOption(
        ['正常', '预警', '超支'],
        ['储能所', '铁锂所', '锰铁锂所'],
        ['#76ace6', '#fac714', '#fd8585'],
        that.chartDatas.projectBudgetByDept || []
      )

      chart.on('legendselectchanged', function (event) {
        let _datas = (that.chartDatas.projectBudgetByDept || []).filter(item => {
          return event.selected[item.name]
        })
        that.initStackDataType(_datas,option)
        chart.setOption(option)
      });

      chart.on('click', function (params) {

				let deptIds = ['22269', '18711', '22487']

        that.$router.push({
          path: "/finance/project_statistics",
          query: {
            deptId: deptIds[params.dataIndex],
          }
        })

      });

      chart.setOption(option)
    },
    // 产品变更堆叠图
    initBarChart1() {
      let that = this
      const chart = echarts.init(that.$refs.stackedBar2)
      that.charts.push(chart)
      const option = that.getBarChartOption(
        ['产品总数', '产品变更数'],
        ['储能所', '铁锂所', '锰铁锂所'],
        [{
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: '#5B8FF9'
          }, {
            offset: 1, color: '#B9D0FF'
          }],
          global: false
        },{
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: '#5AD0B5'
          }, {
            offset: 1, color: '#7CD3E4'
          }],
          global: false
        },],
        {
          dimensions: ['product', '产品总数', '产品变更数'],
          source: that.chartDatas.productAltersByDept || []
        }
      )
      chart.on('click', function (params) {
        console.log(params)
        let deptIds = ['22269', '18711', '22487']
        that.$router.push({
          path: "/jm_project_alter",
          query: {
            dept: params.name,
            deptId: deptIds[params.dataIndex],
            //techType: 2
          }
        })

      })
      chart.setOption(option)
    },
    // 问题发生率仪表盘
    initGaugeChart1() {
      const chart = echarts.init(this.$refs.gaugeChart1)
      this.charts.push(chart)
      const option = this.gettGaugeChartOption(
        require('@/assets/blue.svg'),
        '#76ace6',
        '#E9EEF3',
        [
          {
            value: this.problemCloseCount
          }
        ]
      )
      chart.setOption(option)
    },
    // 转化成交率仪表盘
    initGaugeChart2() {
      const chart = echarts.init(this.$refs.gaugeChart2)
      this.charts.push(chart)
      const option = this.gettGaugeChartOption(
        require('@/assets/green.svg'),
        '#45d2b0',
        '#E9EEF3',
        [
          {
            value: this.stageDocsCount
          }
        ]
      )
      chart.setOption(option)
    },

    convertFlexiblePx(min,px,max){
      px = px * this.convertNum;
      if(px < min) return min;
      if(px > max) return max;
      return px;
    },

    resetDimensions() {
      const root = document.documentElement;
      // 获取视窗尺寸
      const convertNum = (document.body.offsetHeight - 40 ) / 1080;
      this.convertNum = convertNum;
      // 设置CSS变量
      root.style.setProperty('--convert-num', `${convertNum}`);
      this.$nextTick(() => {
        setTimeout(() => {
          this.resizeCharts()
        }, 500);
      })
    },

    showFloatTip(text,tipDatas) {
      this.floatTipData.text = text
      this.floatTipData.show = true
      this.tipDatas = tipDatas
    },

    hideTip() {
      this.floatTipData.show = false;
    },
    mouseFollowFloatTip() {
      // 鼠标跟随tip
      let floatTip = this.$refs.floatTip;
      document.onmousemove = e => {
        let x = e.clientX,
          y = e.clientY;
        let mouseOffsetX = 10,
          mouseOffsetY = 30;
        let left = x ;
        let top = y - mouseOffsetY;
        let floatTipRect = floatTip.getBoundingClientRect();
        //边界判断
        left + floatTipRect.width > window.innerWidth &&
        (left -= floatTipRect.width + mouseOffsetX + 10);
        top + floatTipRect.height > window.innerHeight &&
        (top -= floatTipRect.height + mouseOffsetY + 10);
        this.floatTipData.style = {
          left: left + "px",
          top: top + "px"
        };
        this.floatTipData = JSON.parse(JSON.stringify(this.floatTipData));
      };
    }
  },

}
</script>
<style lang="less" scoped="">
@import "./dashboard.less";
</style>