<template>
	<div class="container">

		<jmDashboard />
	</div>
</template>

<script>

import jmDashboard from "./jmDashboard"
export default {
  components: {
    jmDashboard,
  },
  data() {
    return {
      isJMArea: null,
    }
  },
  methods: {},
  created() {
    this.isJMArea = 1
  }
}
</script>

<style lang="less" scoped>
.container {
  /* padding-top: 3px; */
  padding: 0;
}
.clearfix:after {
  content: ' ';
  display: block;
  clear: both;
  margin-bottom: 8px;
}
.head_title {
  color: #333;
  padding: 10px 0;
  font-size: 15px;
  font-weight: 600;
  float: left;
  margin-left: 5px;
  cursor: pointer;
  padding: 8px 10px;
  border-radius: 10px;
}
.head_title.active {
  background: #fff;
  cursor: initial;
}
.head_title::before {
  width: 8px;
  margin-right: 4px;
  content: "\2022"; //填充空格
  color: transparent;
}

.head_title.active::before {
  color: #5b9bd5;
}

</style>
