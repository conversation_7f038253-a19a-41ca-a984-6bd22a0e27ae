<template>
  <div class="product_width">
    <a-spin :spinning="loading">
      <!-- 筛选区域 start -->
      <!-- 筛选区域 end -->

      <!-- 表格 start -->
      <div class="table-wrapper">
        <a-table
          ref="table"
          :rowKey="record => record.issueId + record.productChildCate"
          :columns="columns"
          :dataSource="loadData"
        >

          <template slot="productSplitName" slot-scope="text, record">
            <div v-for="(item,i) in record.productSplitName.split(';')" :key="i">
              {{ item }}
            </div>
          </template>

          <template slot="lines" slot-scope="text, record">
                        <span v-if="record.lines && record.lines.length > 0" >
                                
                                {{ record.lines.map(item => dataLines[item]).filter((value, index, self) => {
                          return self.indexOf(value) === index;
                        }).join() }}
                            </span>
            <span v-else>-</span>
          </template>

          <template slot="projectFixedDate" slot-scope="text, record">
            <span v-if="record.actualFixedDate"><span class="fixedDateCls greenBg" ></span>{{record.actualFixedDate}}</span>
            <span v-else-if="text"><span class="fixedDateCls" :class="Date.parse(text) < (new Date()) ? 'yellowBg' : ''" ></span>{{ text }}</span>
            <span v-else>-</span>
          </template>

          <div slot="action" slot-scope="text, record">
            <a @click="$refs.infoForm.view(record.issueId)">查阅</a>
          </div>
        </a-table>
      </div>
      <infoForm ref="infoForm" />
      <!-- 表格 end -->
    </a-spin>
  </div>
</template>

<script>
import { getTreeProductsOfNotSplit,exportBaseInfo } from "@/api/modular/system/jmChartManage"
import { getwerklines } from "@/api/modular/system/bomManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import infoForm from "./modal/infoForm"


import _ from "lodash"

export default {
  components: {
    Treeselect,
    infoForm
  },
  data() {
    return {
      dataLines:{},
      queryparam: {
        productCates: [],
        cates: [],
        states: [],
        depts: [],
        keyword: null,
      },
      loading: true,
      columns: [
        {
          title: "序号",
          width: 60,
          dataIndex: "no",
          align: "center",
          customRender: (text, record, index) => {
            if (record.productOrProject == 1 && !record.stage) {
              return `${index + 1}`
            }
            return ''
          },
        },
        {
          title: "细分市场",
          width: 160,
          dataIndex: "productSplitName",
          align: "center",
          scopedSlots: {
            customRender: 'productSplitName'
          }
        },
        {
          title: "产品名称",
          width: 120,
          dataIndex: "productProjectName",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "项目名称",
          width: 140,
          dataIndex: "projectName",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "等级",
          width: 100,
          dataIndex: "projectLevelName",
          align: "center",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "PD",
          width: 100,
          align: "center",
          dataIndex: "productManagerName",
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "RPM",
          width: 100,
          dataIndex: "productRPMName",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "PM",
          width: 100,
          dataIndex: "largeProjectManagerName",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "客户代码",
          width: 100,
          dataIndex: "customer",
          align: "center",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "客户项目",
          width: 100,
          dataIndex:'customerProject',
          align: "center",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "定点日期",
          width: 100,
          align: "center",
          dataIndex: 'projectFixedDate',
          scopedSlots: {
            customRender: 'projectFixedDate'
          }
        },
        {
          title: "立项日期",
          width: 100,
          align: "center",
          dataIndex: 'initiationDate',
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "PPAP日期",
          width: 100,
          align: "center",
          dataIndex: 'productPlannedM5',
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "产品状态",
          width: 100,
          align: "center",
          dataIndex: 'productStateName',
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "适用工厂",
          width: 100,
          align: "center",
          scopedSlots: {
            customRender: 'lines'
          }
        },
        {
          title: "研究所",
          width: 150,
          align: "center",
          dataIndex: 'parentDeptName',
          ellipsis: true,
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: "操作",
          width: 100,
          align: "center",
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          }
        },
      ],
      loadData: [],
      totalData: [],
      tablesScroll: { x: "100%", y: 500 },
      tableScroll: 100
    }
  },
  props: {
    // 表格高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },

  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "31px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getTreeProducts()
    this.getwerklines()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  },
  methods: {

    flattenTree(tree) {
      return tree.reduce((acc, node) => {
        acc.push(node.id);
        if (node.children && node.children.length > 0) {
          acc.push(...this.flattenTree(node.children));
        }
        return acc;
      }, []);
    },

    downloadBaseInfo(){
      let _params = {
        projectIds: this.flattenTree(this.loadData)// this.loadData.map(item=>item.issueId)
      }
      exportBaseInfo(_params).then(res => {
        const fileName = `产品信息.xlsx`;
        const _res = res.data;
        let blob = new Blob([_res]);
        let downloadElement = document.createElement("a");
        //创建下载的链接
        let href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        //下载后文件名
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        //点击下载
        downloadElement.click();
        //下载完成移除元素
        document.body.removeChild(downloadElement);
        //释放掉blob对象
        window.URL.revokeObjectURL(href);
      })
    },

    // 数据筛选
    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

      // 产品分类
      if (this.queryparam["cates"].length > 0) {
        filterData = filterData.filter(
          item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
        )
        if (this.queryparam["cates"].indexOf(2) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["depts"].length > 0) {
        console.log(this.queryparam["depts"])
        filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
      }

      // 产品名称
      if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
        const temList = []
        this.searchParam.inputSearch.forEach(v => {
          if (v.keyword === "") return
          filterData.forEach(e => {
            if (e.productProjectName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
              temList.push(e)
            }
          })
        })
        filterData = _.uniqWith(temList, _.isEqual)
      }

      if (this.queryparam.startDate != null) {
        filterData = filterData.filter(
          item =>
            Date.parse(item.initiationDate) >= this.queryparam.startDate &&
            Date.parse(item.initiationDate) < this.queryparam.endDate
        )
      }

      // 表格数据
      this.loadData = filterData
    },
    dateChange(date, dateString) {
      if (dateString[0] != null && dateString[0] != "") {
        this.queryparam.startDate = Date.parse(dateString[0])
      } else {
        this.queryparam.startDate = null
      }
      if (dateString[1] != null && dateString[1] != "") {
        this.queryparam.endDate = Date.parse(dateString[1])
      } else {
        this.queryparam.endDate = null
      }
      this.callFilter()
    },
    getwerklines() {
      getwerklines().then((res) => {
        if (res.success) {
          let mapline = {}
          for (var key in res.data) {
            for (const _item of res.data[key]) {
              mapline[_item.id] = _item.lineName && _item.lineName.length > 0 && _item.lineName.indexOf('-') > -1 ? _item.lineName.split('-')[0] : _item.lineName
              //mapline[_item.id] = _item.werkNo + ( _item.lineName && _item.lineName.length > 0 ? '->' + _item.lineName : '')
            }
          }
          this.dataLines = mapline
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
      })
    },
    getTreeProducts() {
      let that = this
      that.loading = true
      getTreeProductsOfNotSplit({})
        .then(res => {

          if (res.success) {
            this.totalData = JSON.parse(JSON.stringify(res.data))
            this.callFilter()
          }

          res?.success
          || that.$message.error(res?.message, 1)
        })
        .finally(() => {
          that.loading = false
        })
    },
  }
}
</script>

<style lang="less" scoped=''>
@import "./productoption.less";
:root {
  --height: 200px;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}
.fixedDateCls{
  /* padding: 2px 6px; */
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 4px;
}
.greenBg{
  background: #58a55c;
}
.yellowBg{
  background: #fac858;
}
/* 固定列 */
/deep/ .ant-table tr td {
  background: #fff;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(1),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(2),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 60px;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(3),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: calc(60px + 160px);
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(4),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(4){
  position: sticky;
  left: calc(60px + 160px + 120px);
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr th:nth-child(5),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(5){
  position: sticky;
  left: calc(60px + 160px + 120px + 140px);
  z-index: 10;
}
</style>
