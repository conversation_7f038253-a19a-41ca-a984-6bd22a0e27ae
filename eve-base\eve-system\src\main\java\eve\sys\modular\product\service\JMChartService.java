package eve.sys.modular.product.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Strings;
import eve.sys.jiraModular.customTool.entity.Customfieldoption;
import eve.sys.jiraModular.customTool.param.OptionQueryParam;
import eve.sys.jiraModular.customTool.service.ICustomfieldoptionService;
import eve.sys.jiraModular.productManager.entity.ProductManagerProblemIssue;
import eve.sys.jiraModular.productManager.service.IProductManagerProblemIssueService;
import eve.sys.jiraModular.qualityManager.entity.QualityProblem;
import eve.sys.jiraModular.qualityManager.service.IQualityProblemService;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bomPush.service.ISysBomPushService;
import eve.sys.modular.bomhistory.entity.SysBomHistory;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.bomline.entity.SysBomLine;
import eve.sys.modular.bomline.service.ISysBomLineService;
import eve.sys.modular.dict.service.SysDictDataService;
import eve.sys.modular.doc.entity.DocTargetVersion;
import eve.sys.modular.doc.entity.Docs;
import eve.sys.modular.doc.service.IDocTargetVersionService;
import eve.sys.modular.doc.service.IDocsService;
import eve.sys.modular.file.entity.SysFileInfo;
import eve.sys.modular.file.service.SysFileInfoService;
import eve.sys.modular.gantt.entity.ProjectGantt;
import eve.sys.modular.gantt.service.IProjectGanttService;
import eve.sys.modular.product.entity.ProductManager;
import eve.sys.modular.product.param.Alter;
import eve.sys.modular.product.param.ProductCateBean;
import eve.sys.modular.product.param.ProductStageItem;
import eve.sys.modular.product.param.request.ChartParam;
import eve.sys.modular.product.param.response.*;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.productparam.entity.SysProductParam;
import eve.sys.modular.productparam.service.ISysProductParamService;
import eve.sys.modular.techdoc.entity.TechDoc;
import eve.sys.modular.techdoc.service.ITechDocService;
import eve.sys.modular.techhistory.entity.TechHistory;
import eve.sys.modular.techhistory.service.ITechHistoryService;
import eve.sys.modular.topic.param.Cate;
import eve.sys.modular.user.entity.SysUser;
import eve.sys.modular.user.service.SysUserService;
import eve.sys.modular.weekprocess.entity.WeekProcessDetail;
import eve.sys.modular.weekprocess.service.IWeekProcessDetailService;
import eve.sys.modular.weekprocess.service.IWeekProcessService;
import eve.sys.modular.werkline.entity.SysWerkLine;
import eve.sys.modular.werkline.service.ISysWerkLineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class JMChartService {

    @Resource
    private ISysProductParamService productParamService;

    @Resource
    private IProductManagerService productManagerService;

    @Resource
    private IProductManagerChildIssueService childIssueService;

    @Resource
    private ISysBomService bomService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Resource
    private ITechHistoryService techHistoryService;

    @Resource
    private IDocsService docsService;

    @Resource
    private ISysBomPushService bomPushService;

    @Resource
    private ITechDocService techDocService;

    @Resource
    private IWeekProcessService WeekProcessService;

    @Resource
    private ICustomfieldoptionService customfieldoptionService;

    @Resource
    private IProductManagerProblemIssueService problemIssueService;

    @Resource
    private IWeekProcessDetailService iWeekProcessDetailService;

    @Resource
    private ISysBomLineService bomLineService;

    @Resource
    IProjectGanttService projectGanttService;

    @Resource
    private SysUserService userService;

    @Resource
    private ISysWerkLineService werkLineService;

    @Resource
    private ISysProductParamService sysProductParamService;

    @Resource
    private SysFileInfoService fileInfoService;

    @Autowired
    private SysDictDataService dataService;

    @Autowired
    private IDocTargetVersionService docTargetVersionService;

    public List<ProductManager> splitByProductMultiCateByTrans(List<ProductManager> products) {

        List<ProductManager> newList = new ArrayList<>();

        // String vcyIds = getVcyCateIds();

        List<Cate> cateBeans = customfieldoptionService.cateList("fileTranProductCate");

        List<ProductStageItem> stages = new ArrayList<>();

        List<SysBomLine> bomLines = new ArrayList<>();

        List<ProjectGantt> gantts = new ArrayList<>();

        List<SysUser> users = new ArrayList<>();

        if (!products.isEmpty()) {

            List<Long> issueIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
            stages = childIssueService.getStages(issueIds);
            bomLines = bomLineService.list(Wrappers.<SysBomLine>lambdaQuery().in(SysBomLine::getBomIssueId, issueIds));
            gantts = projectGanttService.list(
                    Wrappers.lambdaQuery(ProjectGantt.class)
                            .in(ProjectGantt::getIssueId, issueIds));

            List<String> accounts = products.stream().map(ProductManager::getLargeProjectManager)
                    .collect(Collectors.toList());

            if (!accounts.isEmpty()) {
                users = userService.list(Wrappers.lambdaQuery(SysUser.class).in(SysUser::getAccount, accounts));
            }
        }

        for (ProductManager item : products) {

            if (ObjectUtil.isEmpty(item.getProductCateMulti())) {
                continue;
            }

            List<ProductStageItem> _stages = stages.stream().filter(e -> e.getParentId().equals(item.getIssueId()))
                    .collect(Collectors.toList());
            item.setStages(_stages);

            item.setLines(
                    bomLines.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId()))
                            .map(SysBomLine::getLineId).distinct().collect(Collectors.toList()));

            item.setGanttFileId(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileId).orElse(0L));

            item.setGanttFileType(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileType).orElse(""));

            item.setLargeProjectManagerName(
                    users.stream().filter(e -> e.getAccount().equals(item.getLargeProjectManager())).findFirst()
                            .map(SysUser::getName).orElse(""));

            String[] splits = item.getProductCateMulti().split(",");
            String[] splitNames = item.getProductCateMultiName().split("-");

            if (splits.length > 1) {
                for (int i = 0, j = splits.length; i < j; i++) {

                    /*
                     * if (vcyIds.indexOf(splits[i]) != -1) {
                     * continue;
                     * }
                     */

                    final String tempStr = splits[i];
                    Optional<Cate> childCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(Long.valueOf((tempStr)))).findFirst();
                    item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                    ProductManager _item = JSONObject.parseObject(JSONObject.toJSONString(item), ProductManager.class);
                    _item.setProductChildCate(splits[i]);

                    Optional<Cate> parentCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                            .findFirst();

                    _item.setProductSplitName(
                            parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[i]
                                    : splitNames[i]);
                    newList.add(_item);
                }
            } else {

                /*
                 * if (vcyIds.indexOf(splits[0]) != -1) {
                 * continue;
                 * }
                 */

                item.setProductChildCate(splits[0]);
                Optional<Cate> childCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(Long.valueOf((splits[0])))).findFirst();
                item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                Optional<Cate> parentCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                        .findFirst();

                item.setProductSplitName(
                        parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[0]
                                : splitNames[0]);
                newList.add(item);
            }

        }

        newList = newList.stream().sorted(Comparator.comparing(ProductManager::getProductProjectName))
                .collect(Collectors.toList());

        return newList;
    }

    public List<ProductManager> productMultiCateOfNotSplitByTrans(List<ProductManager> products) {

        List<ProductManager> newList = new ArrayList<>();

        // String vcyIds = getVcyCateIds();

        List<Cate> cateBeans = customfieldoptionService.cateList("fileTranProductCate");

        List<ProductStageItem> stages = new ArrayList<>();

        List<SysBomLine> bomLines = new ArrayList<>();

        List<ProjectGantt> gantts = new ArrayList<>();

        List<SysUser> users = new ArrayList<>();

        if (!products.isEmpty()) {

            List<Long> issueIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
            stages = childIssueService.getStages(issueIds);
            bomLines = bomLineService.list(Wrappers.<SysBomLine>lambdaQuery().in(SysBomLine::getBomIssueId, issueIds));
            gantts = projectGanttService.list(
                    Wrappers.lambdaQuery(ProjectGantt.class)
                            .in(ProjectGantt::getIssueId, issueIds)
            );

            List<String> accounts = products.stream().map(ProductManager::getLargeProjectManager)
                    .collect(Collectors.toList());

            if (!accounts.isEmpty()) {
                users = userService.list(Wrappers.lambdaQuery(SysUser.class).in(SysUser::getAccount, accounts));
            }
        }

        for (ProductManager item : products) {

            if (ObjectUtil.isEmpty(item.getProductCateMulti())) {
                continue;
            }

            List<ProductStageItem> _stages = stages.stream().filter(e -> e.getParentId().equals(item.getIssueId()))
                    .collect(Collectors.toList());
            item.setStages(_stages);

            item.setLines(
                    bomLines.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId()))
                            .map(SysBomLine::getLineId).distinct().collect(Collectors.toList()));

            item.setGanttFileId(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileId).orElse(0L));

            item.setGanttFileType(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileType).orElse(""));

            item.setLargeProjectManagerName(
                    users.stream().filter(e -> e.getAccount().equals(item.getLargeProjectManager())).findFirst()
                            .map(SysUser::getName).orElse(""));

            String[] splits = item.getProductCateMulti().split(",");
            String[] splitNames = item.getProductCateMultiName().split("-");

            item.setCateIds(new ArrayList<>());

            if (splits.length > 1) {

                List<String> _splitNames = new ArrayList<>();
                boolean flag = false;

                for (int i = 0, j = splits.length; i < j; i++) {

                    /*
                     * if (vcyIds.indexOf(splits[i]) != -1) {
                     * continue;
                     * }
                     */
                    flag = true;

                    final String tempStr = splits[i];
                    Optional<Cate> childCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(Long.valueOf((tempStr)))).findFirst();
                    Optional<Cate> parentCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                            .findFirst();

                    item.getCateIds().add(Long.valueOf(splits[i]));
                    item.getCateIds().add(childCateOptional.map(Cate::getPid).orElse(0L));
                    _splitNames.add(
                            parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[i]
                                    : splitNames[i]);

                }
                if (flag) {
                    item.setProductSplitName(_splitNames.stream().collect(Collectors.joining(";")));
                    newList.add(item);
                }

            } else {

                /*
                 * if (vcyIds.indexOf(splits[0]) != -1) {
                 * continue;
                 * }
                 */

                item.setProductChildCate(splits[0]);
                Optional<Cate> childCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(Long.valueOf((splits[0])))).findFirst();
                item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                Optional<Cate> parentCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                        .findFirst();

                item.getCateIds().add(Long.valueOf(splits[0]));
                item.getCateIds().add(childCateOptional.map(Cate::getPid).orElse(0L));

                item.setProductSplitName(
                        parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[0]
                                : splitNames[0]);
                newList.add(item);
            }

        }

        newList = newList.stream().sorted(Comparator.comparing(ProductManager::getProductProjectName))
                .collect(Collectors.toList());

        return newList;
    }

    public List<ProductManager> splitByProductMultiCate(List<ProductManager> products) {

        List<ProductManager> newList = new ArrayList<>();

        // String vcyIds = getVcyCateIds();

        List<Cate> cateBeans = customfieldoptionService.cateList("jmProductCate"); //获取荆门部门自定义字段

        List<ProductStageItem> stages = new ArrayList<>();

        List<SysBomLine> bomLines = new ArrayList<>();

        List<ProjectGantt> gantts = new ArrayList<>();

        List<SysUser> users = new ArrayList<>();

        if (!products.isEmpty()) {

            List<Long> issueIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
            stages = childIssueService.getStages(issueIds);
            bomLines = bomLineService.list(Wrappers.<SysBomLine>lambdaQuery()
                    .in(SysBomLine::getBomIssueId, issueIds));
            gantts = projectGanttService.list(Wrappers.lambdaQuery(ProjectGantt.class)
                    .in(ProjectGantt::getIssueId, issueIds));

            List<String> accounts = products.stream()
                    .map(ProductManager::getLargeProjectManager)
                    .collect(Collectors.toList());

            if (!accounts.isEmpty()) {
                users = userService.list(Wrappers.lambdaQuery(SysUser.class)
                        .in(SysUser::getAccount, accounts));
            }
        }

        for (ProductManager item : products) {
            if (ObjectUtil.isEmpty(item.getProductCateMulti())) continue;

            List<ProductStageItem> _stages = stages.stream()
                    .filter(e -> e.getParentId().equals(item.getIssueId()))
                    .collect(Collectors.toList());
            item.setStages(_stages);
            item.setLines(bomLines.stream()
                    .filter(e -> e.getBomIssueId().equals(item.getIssueId()))
                    .map(SysBomLine::getLineId).distinct().collect(Collectors.toList()));
            item.setGanttFileId(gantts.stream()
                    .filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileId).orElse(0L));
            item.setGanttFileType(gantts.stream()
                    .filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileType).orElse(""));
            item.setLargeProjectManagerName(users.stream()
                    .filter(e -> e.getAccount().equals(item.getLargeProjectManager())).findFirst()
                    .map(SysUser::getName).orElse(""));
            String[] splits = item.getProductCateMulti().split(",");
            String[] splitNames = item.getProductCateMultiName().split("-");

            if (splits.length > 1) { // 细分市场有多个
                for (int i = 0, j = splits.length; i < j; i++) {

                    /*
                     * if (vcyIds.indexOf(splits[i]) != -1) {
                     * continue;
                     * }
                     */

                    final String tempStr = splits[i];
                    Optional<Cate> childCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(Long.valueOf((tempStr))))
                            .findFirst();
                    item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                    ProductManager _item = JSONObject.parseObject(JSONObject.toJSONString(item), ProductManager.class);

                    _item.setProductChildCate(splits[i]);

                    Optional<Cate> parentCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(childCateOptional
                                    .map(Cate::getPid).orElse(0L)))
                            .findFirst();

                    _item.setProductSplitName(
                            parentCateOptional.isPresent()
                                    ? parentCateOptional.get().getValue() + "-" + splitNames[i]
                                    : splitNames[i]);
                    newList.add(_item);
                }
            } else {

                /*
                 * if (vcyIds.indexOf(splits[0]) != -1) {
                 * continue;
                 * }
                 */

                item.setProductChildCate(splits[0]);
                Optional<Cate> childCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(Long.valueOf((splits[0])))).findFirst();
                item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                Optional<Cate> parentCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                        .findFirst();

                item.setProductSplitName(
                        parentCateOptional.isPresent()
                                ? parentCateOptional.get().getValue() + "-" + splitNames[0]
                                : splitNames[0]);
                newList.add(item);
            }

        }

        newList = newList.stream()
                .sorted(Comparator.comparing(ProductManager::getProductProjectName))
                .collect(Collectors.toList());

        return newList;
    }

    public List<ProductManager> productMultiCateOfNotSplit(List<ProductManager> products) {

        List<ProductManager> newList = new ArrayList<>();

        // String vcyIds = getVcyCateIds();

        List<Cate> cateBeans = customfieldoptionService.cateList("jmProductCate"); //获取荆门部门自定义字段

        List<ProductStageItem> stages = new ArrayList<>();

        List<SysBomLine> bomLines = new ArrayList<>();

        List<ProjectGantt> gantts = new ArrayList<>();

        List<SysUser> users = new ArrayList<>();

        if (!products.isEmpty()) {

            List<Long> issueIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
            stages = childIssueService.getStages(issueIds);
            bomLines = bomLineService.list(Wrappers.<SysBomLine>lambdaQuery().in(SysBomLine::getBomIssueId, issueIds));
            gantts = projectGanttService.list(
                    Wrappers.lambdaQuery(ProjectGantt.class)
                            .in(ProjectGantt::getIssueId, issueIds));

            List<String> accounts = products.stream().map(ProductManager::getLargeProjectManager)
                    .collect(Collectors.toList());

            if (!accounts.isEmpty()) {
                users = userService.list(Wrappers.lambdaQuery(SysUser.class).in(SysUser::getAccount, accounts));
            }
        }

        for (ProductManager item : products) {

            if (ObjectUtil.isEmpty(item.getProductCateMulti())) continue;

            List<ProductStageItem> _stages = stages.stream().filter(e -> e.getParentId().equals(item.getIssueId()))
                    .collect(Collectors.toList());
            item.setStages(_stages);

            item.setLines(
                    bomLines.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId()))
                            .map(SysBomLine::getLineId).distinct().collect(Collectors.toList()));

            item.setGanttFileId(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileId).orElse(0L));

            item.setGanttFileType(gantts.stream().filter(e -> e.getIssueId().equals(item.getIssueId())).findFirst()
                    .map(ProjectGantt::getFileType).orElse(""));

            item.setLargeProjectManagerName(
                    users.stream().filter(e -> e.getAccount().equals(item.getLargeProjectManager())).findFirst()
                            .map(SysUser::getName).orElse(""));

            String[] splits = item.getProductCateMulti().split(",");
            String[] splitNames = item.getProductCateMultiName().split("-");

            item.setCateIds(new ArrayList<>());

            if (splits.length > 1) {

                List<String> _splitNames = new ArrayList<>();
                boolean flag = false;

                for (int i = 0, j = splits.length; i < j; i++) {

                    /*
                     * if (vcyIds.indexOf(splits[i]) != -1) {
                     * continue;
                     * }
                     */
                    flag = true;

                    final String tempStr = splits[i];
                    Optional<Cate> childCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(Long.valueOf((tempStr)))).findFirst();
                    Optional<Cate> parentCateOptional = cateBeans.stream()
                            .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                            .findFirst();

                    item.getCateIds().add(Long.valueOf(splits[i]));
                    item.getCateIds().add(childCateOptional.map(Cate::getPid).orElse(0L));
                    _splitNames.add(
                            parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[i]
                                    : splitNames[i]);

                }
                if (flag) {
                    item.setProductSplitName(_splitNames.stream().collect(Collectors.joining(";")));
                    newList.add(item);
                }

            } else {

                /*
                 * if (vcyIds.indexOf(splits[0]) != -1) {
                 * continue;
                 * }
                 */

                item.setProductChildCate(splits[0]);
                Optional<Cate> childCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(Long.valueOf((splits[0])))).findFirst();
                item.setProductParentCate(childCateOptional.map(Cate::getPid).map(String::valueOf).orElse("0"));

                Optional<Cate> parentCateOptional = cateBeans.stream()
                        .filter(cate -> cate.getId().equals(childCateOptional.map(Cate::getPid).orElse(0L)))
                        .findFirst();

                item.getCateIds().add(Long.valueOf(splits[0]));
                item.getCateIds().add(childCateOptional.map(Cate::getPid).orElse(0L));

                item.setProductSplitName(
                        parentCateOptional.isPresent() ? parentCateOptional.get().getValue() + "-" + splitNames[0]
                                : splitNames[0]);
                newList.add(item);
            }

        }

        newList = newList.stream().sorted(Comparator.comparing(ProductManager::getProductProjectName))
                .collect(Collectors.toList());

        return newList;
    }


    @Resource
    private IQualityProblemService qualityProblemService;

    private LocalDate getProductCurStageDate(ProductManager product) {

        DateTimeFormatter strFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        switch (product.getProductStage().intValue()) {
            case 1:
                return Strings.isNullOrEmpty(product.getK0PlannedDate()) || product.getK0PlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getK0PlannedDate(), strFormat);

            case 2:
                return Strings.isNullOrEmpty(product.getM1PlannedDate()) || product.getM1PlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getM1PlannedDate(), strFormat);
            case 3:
                return Strings.isNullOrEmpty(product.getTraPlannedDate()) || product.getTraPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getTraPlannedDate(), strFormat);

            case 4:
                return Strings.isNullOrEmpty(product.getDfaPlannedDate()) || product.getDfaPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getDfaPlannedDate(), strFormat);

            case 5:
                return Strings.isNullOrEmpty(product.getM2PlannedDate()) || product.getM2PlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getM2PlannedDate(), strFormat);

            case 6:
                return Strings.isNullOrEmpty(product.getTrbPlannedDate()) || product.getTrbPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getTrbPlannedDate(), strFormat);

            case 7:
                return Strings.isNullOrEmpty(product.getDfbPlannedDate()) || product.getDfbPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getDfbPlannedDate(), strFormat);

            case 8:
                return Strings.isNullOrEmpty(product.getM3PlannedDate()) || product.getM3PlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getM3PlannedDate(), strFormat);

            case 9:
                return Strings.isNullOrEmpty(product.getM4PlannedDate()) || product.getM4PlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getM4PlannedDate(), strFormat);

            case 10:
                return Strings.isNullOrEmpty(product.getPpapPlannedDate()) || product.getPpapPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getPpapPlannedDate(), strFormat);

            case 11:
                return Strings.isNullOrEmpty(product.getSopPlannedDate()) || product.getSopPlannedDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(product.getSopPlannedDate(), strFormat);
        }
        return LocalDate.now();
    }

    private CompletableFuture<List<JSONObject>> productStage(List<ProductManager> products) {
        return CompletableFuture.supplyAsync(() -> {

            List<DocTargetVersion> docsVersion = docTargetVersionService.getListByIssueIds(products.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

            Map<String, String> versionMap = docsVersion.stream()
                    .collect(Collectors.toMap(
                            e -> e.getIssueId() + "" + e.getStage(),
                            e -> e.getVersion() + "",
                            (existing, replacement) -> existing));

            List<Long> deptIds = Arrays.asList(22269L, 18711L, 22487L);
            List<Long> docIsNeedList = Arrays.asList(1L, 2L);
            List<Docs> docs = docsService.list(
                    Wrappers.<Docs>lambdaQuery()
                            .in(Docs::getIssueId,
                                    products.stream().map(ProductManager::getIssueId).collect(Collectors.toList()))
                            .in(Docs::getDocIsNeed, docIsNeedList)
                            .apply("VERSION = VERSION_ID")
                            .in(Docs::getStage, Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8))
                            .isNotNull(Docs::getVersion)
                            .select(Docs::getIssueId, Docs::getVersion, Docs::getVersionId, Docs::getIsOver,
                                    Docs::getDocIsNeed, Docs::getScore, Docs::getStage));

            Map<Long, List<String>> projectByDept = products.stream()
                    .collect(Collectors.groupingBy(ProductManager::getParentId,
                            Collectors.mapping(e -> e.getIssueId() + "" + e.getProductStage(), Collectors.toList())));

            if (deptIds.size() > projectByDept.size()) {
                Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                    if (!projectByDept.containsKey(Long.valueOf(n)))
                        projectByDept.put(Long.valueOf(n), new ArrayList<>());
                });
            }

            List<JSONObject> countStageByDept = projectByDept.entrySet().stream().map(
                    entry -> {
                        // 部门项目数
                        long count = entry.getValue().size();
                        // 部门项目当前阶段有效性分数总和
                        BigDecimal effetiveTotal = new BigDecimal(0);
                        for (String val : entry.getValue()) {

                            long sum = docs.stream()
                                    .filter(e -> versionMap.getOrDefault(e.getIssueId() + "" + e.getStage(),"").equals(e.getVersion() + ""))
                                    .filter(e -> null != e.getScore())
                                    .filter(e -> val.equals(e.getIssueId() + "" + e.getStage()))
                                    .mapToLong(Docs::getScore)
                                    .sum();
                            long sumCount = docs.stream()
                                    .filter(e -> versionMap.getOrDefault(e.getIssueId() + "" + e.getStage(),"").equals(e.getVersion() + ""))
                                    .filter(e -> val.equals(e.getIssueId() + "" + e.getStage()))
                                    .count() * 10L;
                            BigDecimal effetive = sumCount == 0L ? BigDecimal.valueOf(0L)
                                    : BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(sumCount), 8,
                                    BigDecimal.ROUND_HALF_UP);

                            effetiveTotal = effetiveTotal.add(effetive);

                        }
                        JSONObject object = new JSONObject();
                        object.put("effetiveTotal", effetiveTotal);
                        object.put("count", count);

                        object.put(entry.getKey() + "",
                                count == 0L ? BigDecimal.valueOf(0L)
                                        : effetiveTotal.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP)
                                        .multiply(BigDecimal.valueOf(100L)));
                        return object;
                    }).collect(Collectors.toList());

            // 项目总数
            long count = products.size();
            BigDecimal effetive = countStageByDept.stream().map(e -> e.getBigDecimal("effetiveTotal"))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            JSONObject object = new JSONObject();
            object.put("count",
                    count == 0L ? BigDecimal.valueOf(0L)
                            : effetive.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP)
                            .multiply(BigDecimal.valueOf(100L)));

            countStageByDept.add(object);
            return countStageByDept;
        });
    }

    private CompletableFuture<List<JSONObject>> projectBudgets(List<ProductManager> productManagers) {
        return CompletableFuture.supplyAsync(() -> {

            List<JSONObject> projectBudgetByDept = new ArrayList<>();

            List<String> budgetLabels = Arrays.asList("正常", "预警", "超支");

            List<Long> deptIds = Arrays.asList(22269L, 18711L, 22487L);


            for (ProductManager e : productManagers) {

                e.setValue(null);

                if (e.getBudgetExe().compareTo(new BigDecimal(80)) < 0) {
                    e.setValue(0);
                }
                if (e.getBudgetExe().compareTo(new BigDecimal(80)) >= 0
                        && e.getBudgetExe().compareTo(new BigDecimal(100)) < 0) {
                    e.setValue(1);
                }
                if (e.getBudgetExe().compareTo(new BigDecimal(100)) >= 0) {
                    e.setValue(2);
                }

            }

            for (int i = 0, j = budgetLabels.size(); i < j; i++) {

                final int m = i;
                Map<Long, Long> countMap = productManagers.stream()
                        .filter(e -> null != e.getValue())
                        .filter(e -> e.getValue().equals(m))
                        .collect(
                                Collectors.groupingBy(
                                        ProductManager::getParentId,
                                        Collectors.counting()));

                if (deptIds.size() > countMap.size()) {
                    Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                        if (!countMap.containsKey(Long.valueOf(n.intValue())))
                            countMap.put(Long.valueOf(n.intValue()), 0L);
                    });
                }

                JSONObject object = new JSONObject();
                object.put("name", budgetLabels.get(i));
                object.put("datas", countMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                        .map(Map.Entry::getValue).collect(Collectors.toList()));

                projectBudgetByDept.add(object);
            }
            return projectBudgetByDept;
        });
    }

    /* 首页看板接口--新 start */
    public JSONObject getProductStatics() throws ParseException, InterruptedException, ExecutionException {

        JSONObject obj = new JSONObject();

        // 获取荆门产品部门 产品&项目
        List<Long> deptIds = Arrays.asList(22269L, 18711L, 22487L);

        List<ProductManager> productManagers = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept, deptIds)
                        .isNotNull(ProductManager::getProductCateMulti) //产品市场分类非空
                        .ne(ProductManager::getProductClassification, 1) // 产品分类不等于1（预研产品）
        );
        productManagers = productMultiCateOfNotSplit(productManagers);

        obj.put("projectCount", productManagers.size());

        // 计算是否逾期 & 逾期天数
        productManagers.forEach(e -> {
            e.setParentId(Long.valueOf(deptIds.indexOf(e.getParentDept()))); // 设置项目的父部门id，映射为索引(0,1,2)

            LocalDate planDate = getProductCurStageDate(e); // 获取当前阶段计划完成日期
            long daysDiff = planDate.until(LocalDate.now(), ChronoUnit.DAYS); // 计算与当前日期的差值
            e.setDelayDays(Math.max(daysDiff, 0)); // 逾期天数(负数，未逾期，视为0)
        });

        /* 根据部门统计项目费用 start */

        // 无费用，返回异常，先注释掉 --20250625 090000
        // TODO: 待整体检查完成后确认费用逻辑恢复方案
        // CompletableFuture<List<JSONObject>> budgetFuture = projectBudgets(productManagers);

        /* 根据部门统计项目费用 end */

        /* 产品 */
        List<ProductManager> products = productManagers.stream()
                .filter(e -> e.getProductOrProject().equals(1L))
                .collect(Collectors.toList());
        obj.put("productCount", products.size());

        /* 产品转阶段 start */
        CompletableFuture<List<JSONObject>> stageFuture = productStage(products);
        /* 产品转阶段 end */

        /* 产品类别统计 start */
        // 映射：7 立项讨论, 2 & 3 新产品, 4 量产产品, 6 停止
        List<Predicate<Long>> catePredicateList = Arrays.asList(
                (num -> num == 7L),
                (num -> num == 2L || num == 3L),
                (num -> num == 4L),
                (num -> num == 6L));

        List<String> cateLabels = Arrays.asList("立项讨论", "新产品", "量产产品", "停止");

        products.forEach(e -> {
            e.setValue(null);
            for (int i = 0, j = catePredicateList.size(); i < j; i++) {
                if (catePredicateList.get(i).test(e.getProductClassification())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        // 统计各类别数量
        Map<Integer, Long> cateCountMap = products.stream()
                .filter(p -> null != p.getValue())
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > cateCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!cateCountMap.containsKey(n))
                    cateCountMap.put(n, 0L);
            });
        }

        List<JSONObject> proudctCateObjects = cateCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("name", cateLabels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    return object;
                }).collect(Collectors.toList());

        obj.put("productCate", proudctCateObjects);

        /* 根据部门统计产品状态 start */
        List<JSONObject> proudctCateObjectsByDept = new ArrayList<>();
        for (int i = 0, j = cateLabels.size(); i < j; i++) {

            final int m = i;
            Map<Long, Long> countMap = products.stream()
                    .filter(e -> null != e.getValue())
                    .filter(e -> e.getValue().equals(m))
                    .collect(
                            Collectors.groupingBy(
                                    ProductManager::getParentId,
                                    Collectors.counting()));

            if (deptIds.size() > countMap.size()) {
                Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                    if (!countMap.containsKey(Long.valueOf(n.intValue())))
                        countMap.put(Long.valueOf(n.intValue()), 0L);
                });
            }

            JSONObject object = new JSONObject();
            object.put("name", cateLabels.get(i));
            object.put("datas", countMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).collect(Collectors.toList()));

            proudctCateObjectsByDept.add(object);

        }
        obj.put("productCateByDept", proudctCateObjectsByDept);
        /* 根据部门统计产品状态 end */

        /* 产品类别统计 end */

        /* 产品等级统计 start */
        List<Predicate<Long>> levelPredicateList = Arrays.asList(
                (num -> num == 2L),
                (num -> num == 3L),
                (num -> num == 4L),
                (num -> num == 5L));

        List<String> levelLabels = Arrays.asList("S级", "A级", "B级", "C级");

        products.forEach(e -> {
            e.setValue(null);
            for (int i = 0, j = levelPredicateList.size(); i < j; i++) {
                if (levelPredicateList.get(i).test(e.getProjectLevel())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> levelCountMap = products.stream()
                .filter(e -> null != e.getValue())
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > levelCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!levelCountMap.containsKey(n))
                    levelCountMap.put(n, 0L);
            });
        }

        List<JSONObject> proudctLevelObjects = levelCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("name", levelLabels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    return object;
                }).collect(Collectors.toList()
                );

        obj.put("proudctLevel", proudctLevelObjects);
        /* 产品等级统计 end */

        /* 项目等级统计 start */
        productManagers.forEach(e -> {
            e.setValue(null);
            for (int i = 0, j = levelPredicateList.size(); i < j; i++) {
                if (levelPredicateList.get(i).test(e.getProjectLevel())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> projectLevelCountMap = productManagers.stream()
                .filter(e -> null != e.getValue())
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > projectLevelCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!projectLevelCountMap.containsKey(n))
                    projectLevelCountMap.put(n, 0L);
            });
        }

        List<JSONObject> projectLevelObjects = projectLevelCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("name", levelLabels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    return object;
                }).collect(Collectors.toList());
        /* 项目等级统计 end */
        obj.put("projectLevel", projectLevelObjects);

        /* 项目进度统计 start */

        List<String> processLabels = Arrays.asList("正常", "逾期", "停止");

        for (ProductManager e : productManagers) {
            e.setValue(null);
            if (!e.getState().equals(3L) && e.getDelayDays() < 7) {
                e.setValue(0);
            }
            if (!e.getState().equals(3L) && e.getProductState() < 7 && e.getDelayDays() >= 7) {
                e.setValue(1);
            }
            if (e.getState().equals(3L)) {
                e.setValue(2);
            }
        }

        Map<Integer, Long> processCountMap = productManagers.stream()
                .filter(p -> null != p.getValue())
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (3 > processCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(3).forEach(n -> {
                if (!processCountMap.containsKey(n))
                    processCountMap.put(n, 0L);
            });
        }

        List<JSONObject> projectProcessObjects = processCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("name", processLabels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    return object;
                }).collect(Collectors.toList());

        /* 根据部门统计项目进度 start */
        List<JSONObject> projectProcessObjectsByDept = new ArrayList<>();

        for (int i = 0, j = processLabels.size(); i < j; i++) {

            final int m = i;
            Map<Long, Long> countMap = productManagers.stream()
                    .filter(e -> null != e.getValue())
                    .filter(e -> e.getValue().equals(m))
                    .collect(
                            Collectors.groupingBy(
                                    ProductManager::getParentId,
                                    Collectors.counting()));

            if (deptIds.size() > countMap.size()) {
                Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                    if (!countMap.containsKey(Long.valueOf(n.intValue())))
                        countMap.put(Long.valueOf(n.intValue()), 0L);
                });
            }

            JSONObject object = new JSONObject();
            object.put("name", processLabels.get(i));
            object.put("datas", countMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue)
                    .collect(Collectors.toList()));

            projectProcessObjectsByDept.add(object);

        }
        /* 根据部门统计项目进度 end */



        obj.put("projectProcess", projectProcessObjects);

        obj.put("projectProcessByDept", projectProcessObjectsByDept);
        /* 项目进度统计 end */


        /* BOM产品变更数统计 start */
        List<String> deptsLabels = Arrays.asList("储能所", "铁锂所", "锰铁锂所");

        Map<Long, List<Long>> productByDeptMap = products.stream()
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getParentId,
                                Collectors.mapping(ProductManager::getIssueId, Collectors.toList())));

        if (deptIds.size() > productByDeptMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                if (!productByDeptMap.containsKey(Long.valueOf(n)))
                    productByDeptMap.put(Long.valueOf(n), new ArrayList<>());
            });
        }
        List<SysBom> boms = bomService.list(
                Wrappers.<SysBom>lambdaQuery()
                        .in(SysBom::getBomType, Arrays.asList(0, 2, 3))
                        .in(SysBom::getBomIssueId,
                                products.size() <= 0
                                        ? Arrays.asList(0)
                                        : products.stream().map(ProductManager::getIssueId)
                                        .collect(Collectors.toList()))
                        .select(SysBom::getBomIssueId, SysBom::getId));

        List<SysBomHistory> bomHistories = bomHistoryService.list(
                Wrappers.<SysBomHistory>lambdaQuery()
                        .ge(SysBomHistory::getProductState, 4)
                        .eq(SysBomHistory::getType, 0)
                        .eq(SysBomHistory::getIsAddWerk, 0)
                        .in(SysBomHistory::getBomId,
                                boms.size() <= 0 ? Arrays.asList(0)
                                        : boms.stream().map(SysBom::getId).collect(Collectors.toList()))
                        .select(SysBomHistory::getBomId));

        List<SysBom> bomAlterList = boms.stream()
                .filter(b -> bomHistories.stream().anyMatch(h -> h.getBomId().equals(b.getId())))
                .collect(Collectors.toList());

        List<ProductManager> countBomAltersByDept = productByDeptMap.entrySet().stream()
                .map(
                        entry -> {
                            return ProductManager
                                    .builder()
                                    .parentId(entry.getKey())
                                    .count(
                                            entry.getValue().stream()
                                                    .filter(v -> bomAlterList.stream()
                                                            .anyMatch(b -> b.getBomIssueId().equals(v)))
                                                    .count())
                                    .build();
                        })
                .collect(Collectors.toList());

        List<JSONObject> productAltersObjectsByDept = new ArrayList<>();

        for (int i = 0, j = deptsLabels.size(); i < j; i++) {
            final int m = i;
            JSONObject object = new JSONObject();
            object.put("product", deptsLabels.get(i));
            object.put("产品总数", productByDeptMap.get(Long.valueOf(i)).size());
            object.put("产品变更数", countBomAltersByDept.stream().filter(e -> m == e.getParentId().intValue()).findFirst()
                    .map(ProductManager::getCount).orElse(0L));

            productAltersObjectsByDept.add(object);

        }

        obj.put("productAltersByDept", productAltersObjectsByDept);
        obj.put("productAltersCount", countBomAltersByDept.stream().mapToLong(ProductManager::getCount).sum());
        /* BOM产品变更数统计 end */

        /* 问题关闭率 start */
        List<QualityProblem> qualityProblems = qualityProblemService
                .list(QualityProblem.builder().instituteList(Arrays.asList("22531")).build()).stream() // institute：22531 荆门院
                .filter(e -> null != e.getJmDepartment()).collect(Collectors.toList()); // jmDepartment 部门（荆门院）
        List<Long> _deptIds = Arrays.asList(10700L, 10701L, 10702L); // 质量部门字段使用"部门（荆门院）"， 10700 储能所, 10701 铁锂所, 10702 锰铁锂所
        for (int i = 0, j = _deptIds.size(); i < j; i++) {
            for (QualityProblem e : qualityProblems) {
                if (e.getJmDepartment().equals(_deptIds.get(i) + "")) {
                    e.setParentDeptId(Long.valueOf(i));
                }
            }
        }
        qualityProblems = qualityProblems.stream().filter(q -> null != q.getParentDeptId())
                .sorted(Comparator.comparing(QualityProblem::getParentDeptId)).collect(Collectors.toList());

        Map<Long, List<QualityProblem>> qualityProblemMap = qualityProblems.stream()
                .collect(Collectors.groupingBy(QualityProblem::getParentDeptId));

        if (deptIds.size() > qualityProblemMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptIds.size()).forEach(n -> {
                if (!qualityProblemMap.containsKey(Long.valueOf(n)))
                    qualityProblemMap.put(Long.valueOf(n), new ArrayList<>());
            });
        }
        List<JSONObject> countQualityProblemByDept = qualityProblemMap.entrySet().stream().map(
            entry->{
                long count = entry.getValue().size();
                long sum = entry.getValue().stream().filter(v->v.getProblemStatus().equals(0L)).count();
                JSONObject object = new JSONObject();
                object.put(entry.getKey()+"",
                        count == 0L ? BigDecimal.valueOf(0L)
                                : BigDecimal.valueOf(sum)
                                .divide(BigDecimal.valueOf(count),2, BigDecimal.ROUND_HALF_UP)
                                .multiply(BigDecimal.valueOf(100L)));
                return object;
            }).collect(Collectors.toList());

        long _count = qualityProblems.stream().count();
        long _sum = qualityProblems.stream().filter(v -> v.getProblemStatus().equals(0L)).count();

        JSONObject _object = new JSONObject();
        _object.put("count",
                _count == 0L ? BigDecimal.valueOf(0L)
                        : BigDecimal.valueOf(_sum).divide(BigDecimal.valueOf(_count), 2, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100L)));
        countQualityProblemByDept.add(_object);
        obj.put("countQualityProblemByDept", countQualityProblemByDept);
        /* 问题关闭率 end */


        obj.put("countStageByDept", stageFuture.get());

        // 无费用，返回异常，先注释掉 --20250625 090000
        // TODO: 待整体检查完成后确认费用逻辑恢复方案
        // obj.put("projectBudgetByDept", budgetFuture.get());

        return obj;
    }

    /* 首页看板接口--新 end */

    /* 首页看板接口 start */
    public List<ProductManager> getAlls() {

        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)

        );
        products = productMultiCateOfNotSplit(products);
        return products;
    }

    public List<ProductManager> getAllOfNotSplit() {
        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti));
        products = productMultiCateOfNotSplit(products);
        return products;
    }

    public List<ProductManager> getTreeProducts() {

        List<ProductManager> _productManagers = getAlls();

        List<ProductManager> productManagers = _productManagers.stream().filter(e -> e.getProductOrProject().equals(1L))
                .collect(Collectors.toList());

        for (ProductManager item : productManagers) {
            List<ProductManager> childrens = _productManagers.stream()
                    .filter(e -> !e.getProductOrProject().equals(1L)
                            && e.getProductProjectName().equals(item.getProductProjectName()))
                    .collect(Collectors.toList());
            item.setChildren(childrens.size() > 0 ? childrens : null);

        }
        return productManagers.stream().collect(Collectors.toList());
    }

    public List<ProductManager> getTreeProductsOfNotSplit() {

        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L))
                        .isNotNull(ProductManager::getProductCateMulti));

        List<ProductManager> _productManagers = productMultiCateOfNotSplit(products);

        _productManagers.forEach(e -> {

            e.setDelayDays(0L);

            LocalDate planDate = getProductCurStageDate(e);

            e.setDelayDays(planDate.until(LocalDate.now(), ChronoUnit.DAYS) < 0L ? 0L
                    : planDate.until(LocalDate.now(), ChronoUnit.DAYS));

            e.setPlanDate(planDate);
        });

        List<ProductManager> productManagers = _productManagers.stream().filter(e -> e.getProductOrProject().equals(1L))
                .collect(Collectors.toList());

        for (ProductManager item : productManagers) {
            List<ProductManager> childrens = _productManagers.stream()
                    .filter(e -> !e.getProductOrProject().equals(1L)
                            && e.getProductProjectName().equals(item.getProductProjectName()))
                    .collect(Collectors.toList());
            item.setChildren(childrens.size() > 0 ? childrens : null);

        }
        return productManagers.stream().collect(Collectors.toList());
    }

    public List<JSONObject> getCountByProductClass() {

        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProductClassification, Arrays.asList(1, 2, 3, 4, 7))
                        .eq(ProductManager::getProductOrProject, 1));

        products = productMultiCateOfNotSplit(products);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 7L),
                (num -> num == 1L),
                (num -> num == 2L || num == 3L),
                (num -> num == 4L)
                //(num->num == 6L),
                //(num->num == 5L)
        );

        List<String> labels = Arrays.asList("立项讨论", "预研产品", "新产品", "量产产品"/* ,"停止","其他" */);

        products.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getProductClassification())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> countMap = products.stream()
                .filter(p -> null != p.getValue())

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > countMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!countMap.containsKey(n))
                    countMap.put(n, 0L);
            });
        }

        Long sum = countMap.entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.summingLong(i -> i));

        return countMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("label", labels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    object.put("displayValue",
                            BigDecimal.valueOf(entry.getValue())
                                    .divide(BigDecimal.valueOf(sum), 2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100L)));
                    return object;
                }).collect(Collectors.toList());
    }

    public List<JSONObject> getCountByProductLevel() {

        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProjectLevel, Arrays.asList(2, 3, 4, 5))
                        .eq(ProductManager::getProductOrProject, 1));
        products = productMultiCateOfNotSplit(products);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 2L),
                (num -> num == 3L),
                (num -> num == 4L),
                (num -> num == 5L));

        List<String> labels = Arrays.asList("S级", "A级", "B级", "C级");

        products.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getProjectLevel())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> countMap = products.stream()

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > countMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!countMap.containsKey(n))
                    countMap.put(n, 0L);
            });
        }

        Long sum = countMap.entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.summingLong(i -> i));

        return countMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("label", labels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    object.put("displayValue",
                            BigDecimal.valueOf(entry.getValue())
                                    .divide(BigDecimal.valueOf(sum), 2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100L)));
                    return object;
                }).collect(Collectors.toList());
    }

    public List<JSONObject> getCountByProjectLevel() {

        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProjectLevel, Arrays.asList(2, 3, 4, 5)));
        products = productMultiCateOfNotSplit(products);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 2L),
                (num -> num == 3L),
                (num -> num == 4L),
                (num -> num == 5L));

        List<String> labels = Arrays.asList("S级", "A级", "B级", "C级");

        products.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getProjectLevel())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> countMap = products.stream()

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (4 > countMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(4).forEach(n -> {
                if (!countMap.containsKey(n))
                    countMap.put(n, 0L);
            });
        }

        Long sum = countMap.entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.summingLong(i -> i));
        return countMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("label", labels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    object.put("displayValue",
                            BigDecimal.valueOf(entry.getValue())
                                    .divide(BigDecimal.valueOf(sum), 2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100L)));
                    return object;
                }).collect(Collectors.toList());
    }

    public List<JSONObject> getCountByProductCate() {

        List<Customfieldoption> allOptionList = customfieldoptionService
                .listAsCascading(OptionQueryParam.builder().fieldName("jmProductCate").build()); //获取荆门部门自定义字段

        List<Long> cateIds = Arrays.asList(18884L, 18885L, 18948L, 18886L, 18945L, 22435L);
        allOptionList = allOptionList.stream().filter(e -> cateIds.indexOf(e.getId()) != -1)
                .collect(Collectors.toList());

        Map<Long, String> parentChildsMap = new HashMap<>();

        for (Customfieldoption item : allOptionList) {
            if (item.getSubOptionList().size() > 0) {
                parentChildsMap.put(item.getId(),
                        item.getSubOptionList().stream().sorted(Comparator.comparing(Customfieldoption::getId))
                                .map(e -> String.valueOf(e.getId())).collect(Collectors.joining(",")));
            } else {
                parentChildsMap.put(item.getId(), String.valueOf(item.getId()));
            }
        }
        List<ProductManager> products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .eq(ProductManager::getProductOrProject, 1));

        products = productMultiCateOfNotSplit(products);

        List<Predicate<String>> predicateList = Arrays.asList(
                (num -> parentChildsMap.get(18884L).indexOf(num) != -1),
                (num -> parentChildsMap.get(18885L).indexOf(num) != -1),
                (num -> parentChildsMap.get(18948L).indexOf(num) != -1),
                (num -> parentChildsMap.get(18886L).indexOf(num) != -1),

                (num -> parentChildsMap.get(18945L).indexOf(num) != -1),
                (num -> parentChildsMap.get(22435L).indexOf(num) != -1)

        );

        List<String> labels = Arrays.asList("乘用车", "商用车", "LV", "储能电池", "C圆柱", "软包");

        products.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getProductChildCate())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> countMap = products.stream()
                .filter(e -> null != e.getValue())
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (5 > countMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(5).forEach(n -> {
                if (!countMap.containsKey(n))
                    countMap.put(n, 0L);
            });
        }
        Long sum = countMap.entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.summingLong(i -> i));
        return countMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    JSONObject object = new JSONObject();
                    object.put("label", labels.get(entry.getKey()));
                    object.put("value", entry.getValue());
                    object.put("displayValue",
                            BigDecimal.valueOf(entry.getValue())
                                    .divide(BigDecimal.valueOf(sum), 2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100L)));
                    return object;
                }).collect(Collectors.toList());
    }

    public JSONObject getCountProcessForProject(ChartParam param) {

        List<String> deptList = Arrays.asList("22269", "18711", "22487");

        List<ProductManager> products = productManagerService.list(
                param.getUnderDev() == 1
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)

                        .in(ProductManager::getParentDept, deptList)
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProductClassification, Arrays.asList(1, 2, 3, 5))
                        .in(ProductManager::getParentDept, deptList));

        products = productMultiCateOfNotSplit(products);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 22269L),
                (num -> num == 18711L),
                (num -> num == 22487L)
        );

        //List<String> labels = Arrays.asList("方形电池所","动力圆柱所","新型电池所","动力电池所","储能电池所");

        List<ProductStageItem> stages = childIssueService
                .getStages(products.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        DateTimeFormatter strFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        products.forEach(e -> {

            e.setDelayDays(0L);

            stages.stream().filter(s -> s.getParentId().equals(e.getIssueId()))
                    .filter(s -> s.getStage().equals(e.getProductStage())).findFirst().ifPresent(val -> {

                LocalDate actualDate =
                        Strings.isNullOrEmpty(val.getActualCompletionDate())
                                || val.getActualCompletionDate().equals("-")
                                ? LocalDate.now()
                                : LocalDate.parse(val.getActualCompletionDate(), strFormat);

                LocalDate planDate = Strings.isNullOrEmpty(val.getPlanReviewDate())
                        || val.getPlanReviewDate().equals("-")
                        ? LocalDate.now()
                        : LocalDate.parse(val.getPlanReviewDate(), strFormat);

                e.setDelayDays(planDate.until(actualDate, ChronoUnit.DAYS));

            });

            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getParentDept())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, Long> rightCountMap = products.stream()
                .filter(p -> !p.getState().equals(3L))
                .filter(p -> p.getDelayDays() < 7)

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (deptList.size() > rightCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptList.size()).forEach(n -> {
                if (!rightCountMap.containsKey(n))
                    rightCountMap.put(n, 0L);
            });
        }

        Map<Integer, Long> delayCountMap = products.stream()
                .filter(p -> !p.getState().equals(3L))
                .filter(p -> p.getProductState() < 7)
                .filter(p -> p.getDelayDays() >= 7)

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (deptList.size() > delayCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptList.size()).forEach(n -> {
                if (!delayCountMap.containsKey(n))
                    delayCountMap.put(n, 0L);
            });
        }

        Map<Integer, Long> stopCountMap = products.stream()
                .filter(p -> p.getState().equals(3L))

                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.counting()));

        if (deptList.size() > stopCountMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptList.size()).forEach(n -> {
                if (!stopCountMap.containsKey(n))
                    stopCountMap.put(n, 0L);
            });
        }

        JSONArray jsonArray = new JSONArray();

        JSONObject rightObject = new JSONObject();
        rightObject.put("seriesname", "正常");
        rightObject.put("data",
                rightCountMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getValue());
                            return object;
                        }).collect(Collectors.toList()));
        jsonArray.add(rightObject);

        JSONObject delayObject = new JSONObject();
        delayObject.put("seriesname", "延期");
        delayObject.put("data",
                delayCountMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getValue());
                            return object;
                        }).collect(Collectors.toList()));
        jsonArray.add(delayObject);

        JSONObject stopObject = new JSONObject();
        stopObject.put("seriesname", "停止");
        stopObject.put("data",
                stopCountMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getValue());
                            return object;
                        }).collect(Collectors.toList()));

        jsonArray.add(stopObject);

        JSONObject jsonObject = new JSONObject();

        jsonObject.put("dataset", jsonArray);

        jsonObject.put("rightCount", rightCountMap.values().stream().mapToLong(v -> v).sum());
        jsonObject.put("delayCount", delayCountMap.values().stream().mapToLong(v -> v).sum());
        jsonObject.put("stopCount", stopCountMap.values().stream().mapToLong(v -> v).sum());

        return jsonObject;
    }

    public JSONObject getCountAlterForProduct(ChartParam param) {
        List<String> deptList = Arrays.asList("22269", "18711", "22487");

        List<ProductManager> products = productManagerService.list(
                param.getProductState() == 1
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProductState, Arrays.asList(4, 5, 6))
                        .in(ProductManager::getParentDept, deptList)
                        .eq(ProductManager::getProductOrProject, 1)

                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getProductState, Arrays.asList(2, 3))
                        .in(ProductManager::getParentDept, deptList)
                        .eq(ProductManager::getProductOrProject, 1)
        );

        products = productMultiCateOfNotSplit(products);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 22269L),
                (num -> num == 18711L),
                (num -> num == 22487L)
        );

        products.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getParentDept())) {
                    e.setValue(i);
                    return;
                }
            }
        });

        Map<Integer, List<Long>> productByDeptMap = products.stream()
                .collect(
                        Collectors.groupingBy(
                                ProductManager::getValue,
                                Collectors.mapping(ProductManager::getIssueId, Collectors.toList())));

        if (deptList.size() > productByDeptMap.size()) {
            Stream.iterate(0, n -> n + 1).limit(deptList.size()).forEach(n -> {
                if (!productByDeptMap.containsKey(n))
                    productByDeptMap.put(n, new ArrayList<>());
            });
        }

        /* 电芯BOM */
        List<SysBom> boms = bomService.list(
                Wrappers.<SysBom>lambdaQuery()
                        .in(SysBom::getBomType, Arrays.asList(0, 2, 3))
                        .in(SysBom::getBomIssueId,
                                products.stream().map(ProductManager::getIssueId).collect(Collectors.toList()))
                        .select(SysBom::getBomIssueId, SysBom::getId));

        JSONObject jsonObject = new JSONObject();

        if (boms.size() <= 0) {

            jsonObject.put("0",
                    productByDeptMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .map(entry -> {
                                JSONObject object = new JSONObject();
                                object.put("value", entry.getValue().size());
                                return object;
                            }).collect(Collectors.toList()));

            jsonObject.put("1", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("2", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("3", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("bomCount", 0);
            jsonObject.put("miCount", 0);
            jsonObject.put("mapCount", 0);
            return jsonObject;
        }

        List<SysBomHistory> bomHistories = bomHistoryService.list(
                param.getProductState() == 1
                        ? Wrappers.<SysBomHistory>lambdaQuery()
                        .ge(SysBomHistory::getProductState, 4)
                        .eq(SysBomHistory::getType, 0)
                        .eq(SysBomHistory::getIsAddWerk, 0)
                        .in(SysBomHistory::getBomId,
                                boms.stream().map(SysBom::getId).collect(Collectors.toList()))
                        .select(SysBomHistory::getBomId)
                        : Wrappers.<SysBomHistory>lambdaQuery()
                        .in(SysBomHistory::getProductState, Arrays.asList(2, 3))
                        .eq(SysBomHistory::getType, 0)
                        .eq(SysBomHistory::getIsAddWerk, 0)
                        .in(SysBomHistory::getBomId,
                                boms.stream().map(SysBom::getId).collect(Collectors.toList()))
                        .select(SysBomHistory::getBomId));

        if (bomHistories.size() <= 0) {

            jsonObject.put("0",
                    productByDeptMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .map(entry -> {
                                JSONObject object = new JSONObject();
                                object.put("value", entry.getValue().size());
                                return object;
                            }).collect(Collectors.toList()));

            jsonObject.put("1", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("2", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("3", productByDeptMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        JSONObject object = new JSONObject();
                        object.put("value", 0);
                        return object;
                    }).collect(Collectors.toList()));

            jsonObject.put("bomCount", 0);
            jsonObject.put("miCount", 0);
            jsonObject.put("mapCount", 0);
            return jsonObject;
        }

        /* bom变更 */
        List<SysBom> bomAlterList = boms.stream()
                .filter(b -> bomHistories.stream().anyMatch(h -> h.getBomId().equals(b.getId())))
                .collect(Collectors.toList());

        List<ProductManager> countBomAltersByDept = productByDeptMap.entrySet().stream()
                .map(
                        entry -> {
                            return ProductManager
                                    .builder()
                                    .value(entry.getKey())
                                    .count(
                                            entry.getValue().stream()
                                                    .filter(v -> bomAlterList.stream()
                                                            .anyMatch(b -> b.getBomIssueId().equals(v))).count()
                                    )
                                    .build();
                        })
                .collect(Collectors.toList());

        List<TechHistory> miTechHistories = techHistoryService.list(
                param.getProductState() == 1
                        ? Wrappers.<TechHistory>lambdaQuery()
                        .ge(TechHistory::getProductState, 4)
                        .eq(TechHistory::getType, 0)
                        .in(TechHistory::getBomId,
                                bomHistories.stream().map(SysBomHistory::getBomId).collect(Collectors.toList()))
                        .select(TechHistory::getBomId)
                        : Wrappers.<TechHistory>lambdaQuery()
                        .in(TechHistory::getProductState, Arrays.asList(2, 3))
                        .eq(TechHistory::getType, 0)
                        .in(TechHistory::getBomId,
                                bomHistories.stream().map(SysBomHistory::getBomId).collect(Collectors.toList()))
                        .select(TechHistory::getBomId));

        List<TechHistory> mapTechHistories = techHistoryService.list(
                param.getProductState() == 1
                        ? Wrappers.<TechHistory>lambdaQuery()
                        .ge(TechHistory::getProductState, 4)
                        .eq(TechHistory::getType, 1)
                        .in(TechHistory::getBomId,
                                bomHistories.stream().map(SysBomHistory::getBomId).collect(Collectors.toList()))
                        .select(TechHistory::getBomId)
                        : Wrappers.<TechHistory>lambdaQuery()
                        .in(TechHistory::getProductState, Arrays.asList(2, 3))
                        .eq(TechHistory::getType, 1)
                        .in(TechHistory::getBomId,
                                bomHistories.stream().map(SysBomHistory::getBomId).collect(Collectors.toList()))
                        .select(TechHistory::getBomId));

        List<SysBom> miAlterList = boms.stream()
                .filter(b -> miTechHistories.stream().anyMatch(h -> h.getBomId().equals(b.getId())))
                .collect(Collectors.toList());
        List<ProductManager> countMiAltersByDept = productByDeptMap.entrySet().stream()
                .map(
                        entry -> {
                            return ProductManager
                                    .builder()
                                    .value(entry.getKey())
                                    .count(
                                            entry.getValue().stream()
                                                    .filter(v -> miAlterList.stream()
                                                            .anyMatch(b -> b.getBomIssueId().equals(v)))
                                                    .count())
                                    .build();
                        })
                .collect(Collectors.toList());

        List<SysBom> mapAlterList = boms.stream()
                .filter(b -> mapTechHistories.stream().anyMatch(h -> h.getBomId().equals(b.getId())))
                .collect(Collectors.toList());
        List<ProductManager> countMapAltersByDept = productByDeptMap.entrySet().stream()
                .map(
                        entry -> {
                            return ProductManager
                                    .builder()
                                    .value(entry.getKey())
                                    .count(
                                            entry.getValue().stream()
                                                    .filter(v -> mapAlterList.stream()
                                                            .anyMatch(b -> b.getBomIssueId().equals(v)))
                                                    .count()
                                    )
                                    .build();
                        })
                .collect(Collectors.toList());

        //List<String> labels = Arrays.asList("方形电池所","动力圆柱所","新型电池所","动力电池所","储能电池所");

        jsonObject.put("0",
                productByDeptMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getValue().size());
                            return object;
                        }).collect(Collectors.toList()));

        jsonObject.put("1",
                countBomAltersByDept.stream()
                        .sorted(Comparator.comparing(ProductManager::getValue))
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getCount());
                            return object;
                        }).collect(Collectors.toList()));

        jsonObject.put("2",
                countMiAltersByDept.stream()
                        .sorted(Comparator.comparing(ProductManager::getValue))
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getCount());
                            return object;
                        }).collect(Collectors.toList()));

        jsonObject.put("3",
                countMapAltersByDept.stream()
                        .sorted(Comparator.comparing(ProductManager::getValue))
                        .map(entry -> {
                            JSONObject object = new JSONObject();
                            object.put("value", entry.getCount());
                            return object;
                        }).collect(Collectors.toList()));

        jsonObject.put("bomCount", bomAlterList.size());
        jsonObject.put("miCount", miAlterList.size());
        jsonObject.put("mapCount", mapAlterList.size());

        return jsonObject;
    }

    public JSONObject monitor() throws ParseException {

        JSONObject jsonObject = new JSONObject();

        List<ProductManager> projects = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)

        );
        if (projects.size() <= 0) {
            return null;
        }
        projects = productMultiCateOfNotSplit(projects);

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(1);
        final int nowWeek = calendar.get(Calendar.WEEK_OF_YEAR) - 1;

        List<Long> projectIds = projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList());

        List<ProductManagerProblemIssue> problemIssues = problemIssueService.list(
                Wrappers.<ProductManagerProblemIssue>lambdaQuery()
                        .in(ProductManagerProblemIssue::getParentId, projectIds)
                        .in(ProductManagerProblemIssue::getProblemStatus, Arrays.asList("21518", "21519")));

        jsonObject.put("redLampCount",
                problemIssues.stream().filter(e -> e.getProblemStatus().equals("21518")).count());
        jsonObject.put("yellowLampCount",
                problemIssues.stream().filter(e -> e.getProblemStatus().equals("21519")).count());

        jsonObject.put("curWeekRedLampCount",
                problemIssues.stream()
                        .filter(e -> e.getProblemStatus().equals("21518"))
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getFindDate());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        jsonObject.put("curWeekYellowLampCount",
                problemIssues.stream()
                        .filter(e -> e.getProblemStatus().equals("21519"))
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getFindDate());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());


        List<ProductStageItem> stages = childIssueService
                .getStages(projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        DateTimeFormatter strFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        projects.forEach(e -> {

            e.setDelayDays(0L);

            stages.stream().filter(s -> s.getParentId().equals(e.getIssueId()))
                    .filter(s -> s.getStage().equals(e.getProductStage())).findFirst().ifPresent(val -> {

                LocalDate actualDate =
                        Strings.isNullOrEmpty(val.getActualCompletionDate())
                                || val.getActualCompletionDate().equals("-")
                                ? LocalDate.now()
                                : LocalDate.parse(val.getActualCompletionDate(), strFormat);

                LocalDate planDate = Strings.isNullOrEmpty(val.getPlanReviewDate())
                        || val.getPlanReviewDate().equals("-")
                        ? null
                        : LocalDate.parse(val.getPlanReviewDate(), strFormat);

                e.setPlanCompleteDate(planDate);

                planDate = planDate == null ? LocalDate.now() : planDate;
                e.setDelayDays(planDate.until(actualDate, ChronoUnit.DAYS));
                e.setActualDate(actualDate);
                e.setPlanDate(planDate);

            });
            stages.stream().filter(s -> s.getParentId().equals(e.getIssueId()))
                    .filter(s -> s.getStage().equals(e.getProductStage() - 1)).findFirst().ifPresent(val -> {
                LocalDate actualDate =
                        Strings.isNullOrEmpty(val.getActualCompletionDate())
                                || val.getActualCompletionDate().equals("-")
                                ? null
                                : LocalDate.parse(val.getActualCompletionDate(), strFormat);
                e.setCompleteDate(actualDate);
            });
        });

        jsonObject.put("curWeekStartCounts",
                projects.stream()
                        .filter(e -> null != e.getProjectStartDate())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getProjectStartDate());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        jsonObject.put("curWeekDelayCounts",
                projects.stream()
                        .filter(e -> null != e.getPlanCompleteDate())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            Date _date = Date
                                    .from(e.getPlanCompleteDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            _calendar.setTime(_date);
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .filter(e -> e.getDelayDays() >= 7)
                        .count());

        jsonObject.put("curWeekStopCounts",
                projects.stream()
                        .filter(e -> null != e.getStopTime())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getStopTime());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        jsonObject.put("curWeekPlanCounts",
                projects.stream()
                        .filter(e -> null != e.getPlanCompleteDate())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            Date _date = Date
                                    .from(e.getPlanCompleteDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            _calendar.setTime(_date);
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        jsonObject.put("curWeekActualCounts",
                projects.stream()
                        .filter(e -> null != e.getCompleteDate())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            Date _date = Date
                                    .from(e.getCompleteDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            _calendar.setTime(_date);
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        List<SysBom> boms = bomService.list(
                Wrappers.<SysBom>lambdaQuery()
                        .in(SysBom::getBomType, Arrays.asList(0, 2, 3))
                        .in(SysBom::getBomIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList()))
                        .select(SysBom::getId));

        List<SysBomHistory> bomHistories = bomHistoryService.list(
                Wrappers.<SysBomHistory>lambdaQuery()
                        .in(SysBomHistory::getBomId, boms.stream().map(SysBom::getId).collect(Collectors.toList()))
                        .select(SysBomHistory::getId, SysBomHistory::getCreateTime));

        jsonObject.put("curWeekBomAddCounts",
                bomHistories.stream()
                        .filter(e -> null != e.getType())
                        .filter(e -> e.getType() == 1)
                        .filter(e -> null != e.getCreateTime())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getCreateTime());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());

        jsonObject.put("curWeekBomEditCounts",
                bomHistories.stream()
                        .filter(e -> null != e.getType())
                        .filter(e -> e.getType() == 0)
                        .filter(e -> null != e.getCreateTime())
                        .filter(e -> {
                            Calendar _calendar = Calendar.getInstance();
                            _calendar.setFirstDayOfWeek(1);
                            _calendar.setTime(e.getCreateTime());
                            return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                        })
                        .count());
        return jsonObject;
    }

    public List<JSONObject> getStageCount() {

        List<ProductManager> projects = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .eq(ProductManager::getProductOrProject, 1)
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L)));

        projects = productMultiCateOfNotSplit(projects);

        List<Predicate<Long>> predicateList = Arrays.asList(
                (num -> num == 22269L),
                (num -> num == 18711L),
                (num -> num == 22487L));

        projects.forEach(e -> {
            for (int i = 0, j = predicateList.size(); i < j; i++) {
                if (predicateList.get(i).test(e.getParentDept())) {
                    e.setValue(i);
                    return;
                }
            }
        });
        List<Long> docIsNeedList = Arrays.asList(1L, 2L);
        List<Docs> docs = docsService.list(
                Wrappers.<Docs>lambdaQuery()
                        .in(Docs::getDocIsNeed, docIsNeedList)
                        .isNotNull(Docs::getVersion)
                        .apply("VERSION = VERSION_ID")
                        .in(Docs::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList()))
                        .select(Docs::getIssueId, Docs::getVersion, Docs::getVersionId, Docs::getIsOver,
                                Docs::getDocIsNeed, Docs::getScore, Docs::getStage));

        //List<Docs> docs = _docs.stream().filter(e->e.getVersion().equals(e.getVersionId())).collect(Collectors.toList());

        Map<Integer, List<Long>> projectByDept = projects.stream()
                .filter(e -> null != e.getValue())
                .collect(Collectors.groupingBy(ProductManager::getValue,
                        Collectors.mapping(ProductManager::getIssueId, Collectors.toList())));

        if (predicateList.size() > projectByDept.size()) {
            Stream.iterate(0, n -> n + 1).limit(predicateList.size()).forEach(n -> {
                if (!projectByDept.containsKey(n))
                    projectByDept.put(n, new ArrayList<>());
            });
        }

        List<JSONObject> countStageByDept = projectByDept.entrySet().stream().map(
                entry -> {
                    long count = docs.stream().filter(e -> entry.getValue().indexOf(e.getIssueId()) != -1).count()
                            * 10L;
                    long sum = docs.stream().filter(e -> null != e.getScore())
                            .filter(e -> entry.getValue().indexOf(e.getIssueId()) != -1).mapToLong(Docs::getScore)
                            .sum();
                    JSONObject object = new JSONObject();
                    object.put(entry.getKey() + "",
                            count == 0L ? BigDecimal.valueOf(0L)
                                    : BigDecimal.valueOf(sum)
                                    .divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100L)));
                    return object;
                }).collect(Collectors.toList());

        long count = docs.stream().count() * 10L;
        long sum = docs.stream().filter(e -> null != e.getScore()).mapToLong(Docs::getScore).sum();

        JSONObject object = new JSONObject();
        object.put("count",
                count == 0L ? BigDecimal.valueOf(0L)
                        : BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100L)));

        countStageByDept.add(object);

        return countStageByDept;

    }
    /* 首页看板接口 end */

    /* 监控接口 start */
    public List<ProductManagerProblemIssue> moniterDetails(ProductManagerProblemIssue params) {

        List<ProductManager> _projects = productManagerService.list(
                Optional.ofNullable(params).map(ProductManagerProblemIssue::getProductName).isPresent()
                        ? Wrappers.<ProductManager>lambdaQuery()
                                .in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L))
                                .isNotNull(ProductManager::getProductCateMulti)
                                .like(ProductManager::getProductName, params.getProductName())
                        : Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L))
                        .isNotNull(ProductManager::getProductCateMulti));
        if (_projects.size() <= 0) {
            return null;
        }
        List<ProductManager> projects = productMultiCateOfNotSplit(_projects);

        List<Long> projectIds = projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList());

        LambdaQueryWrapper<ProductManagerProblemIssue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductManagerProblemIssue::getParentId, projectIds);
        lambdaQueryWrapper.orderByDesc(ProductManagerProblemIssue::getCreated);

        if (ObjectUtil.isNotNull(params)) {
            if (ObjectUtil.isNotEmpty(params.getProblemStatus())) {
                lambdaQueryWrapper.eq(ProductManagerProblemIssue::getProblemStatus, params.getProblemStatus());
            }
            if (ObjectUtil.isNotEmpty(params.getProblemDimension())) {
                lambdaQueryWrapper.eq(ProductManagerProblemIssue::getProblemDimension, params.getProblemDimension());
            }
            if (ObjectUtil.isNotEmpty(params.getProductStates())) {
                lambdaQueryWrapper.in(ProductManagerProblemIssue::getProductState,
                        Arrays.asList(params.getProductStates().split(",")));
            }
            if (ObjectUtil.isNotEmpty(params.getIsCurWeek()) && 1 == params.getIsCurWeek()) {

                Calendar calendar = Calendar.getInstance();
                calendar.setFirstDayOfWeek(1);
                final int nowWeek = calendar.get(Calendar.WEEK_OF_YEAR) - 1;

                return problemIssueService.list(lambdaQueryWrapper).stream().filter(e -> {
                    Calendar _calendar = Calendar.getInstance();
                    _calendar.setFirstDayOfWeek(1);
                    _calendar.setTime(e.getFindDate());
                    return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                }).collect(Collectors.toList());

            }
        }

        List<ProductManagerProblemIssue> problemIssues = problemIssueService.list(lambdaQueryWrapper);

        /*
         * issueId 对应的产品状态
         */
        Map<Long, Long> productStates = new HashMap<Long, Long>(7) {
            {
                put(18903L, 1L);
                put(18899L, 2L);
                put(18900L, 3L);
                put(18901L, 4L);
                put(18902L, 5L);
                put(18986L, 6L);
                put(18904L, 7L);
                put(22100L, 8L);
            }
        };

        problemIssues.forEach(e -> {
            e.setProductCateMultiName(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductCateMultiName).findFirst().orElse(""));

            e.setProductSplitName(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductSplitName).findFirst().orElse(""));
            e.setParentDept(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getParentDept).findFirst().orElse(0L));

            e.setProductParentCate(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductParentCate).findFirst().orElse(""));

            e.setProductChildCate(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductChildCate).findFirst().orElse(""));

            e.setProductState(
                    productStates.getOrDefault(Long.valueOf(Optional.ofNullable(e.getProductState()).orElse("0")), 0L)
                            + "");
        });

        return problemIssues;
    }

    /* 监控接口 start */
    public List<ProductManagerProblemIssue> moniterDetailsOfNotSplit(ProductManagerProblemIssue params) {

        List<ProductManager> _projects = productManagerService.list(
                Optional.ofNullable(params).map(ProductManagerProblemIssue::getProductName).isPresent()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L))
                        .isNotNull(ProductManager::getProductCateMulti)
                        .like(ProductManager::getProductName, params.getProductName())
                        : Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L))
                        .isNotNull(ProductManager::getProductCateMulti));
        if (_projects.size() <= 0) {
            return null;
        }
        List<ProductManager> projects = productMultiCateOfNotSplit(_projects);

        List<Long> projectIds = projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList());

        LambdaQueryWrapper<ProductManagerProblemIssue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductManagerProblemIssue::getParentId, projectIds);
        lambdaQueryWrapper.orderByDesc(ProductManagerProblemIssue::getCreated);

        if (ObjectUtil.isNotNull(params)) {
            if (ObjectUtil.isNotEmpty(params.getProblemStatus())) {
                lambdaQueryWrapper.eq(ProductManagerProblemIssue::getProblemStatus, params.getProblemStatus());
            }
            if (ObjectUtil.isNotEmpty(params.getProblemDimension())) {
                lambdaQueryWrapper.eq(ProductManagerProblemIssue::getProblemDimension, params.getProblemDimension());
            }
            if (ObjectUtil.isNotEmpty(params.getProductStates())) {
                lambdaQueryWrapper.in(ProductManagerProblemIssue::getProductState,
                        Arrays.asList(params.getProductStates().split(",")));
            }
            if (ObjectUtil.isNotEmpty(params.getIsCurWeek()) && 1 == params.getIsCurWeek()) {

                Calendar calendar = Calendar.getInstance();
                calendar.setFirstDayOfWeek(1);
                final int nowWeek = calendar.get(Calendar.WEEK_OF_YEAR) - 1;

                return problemIssueService.list(lambdaQueryWrapper).stream().filter(e -> {
                    Calendar _calendar = Calendar.getInstance();
                    _calendar.setFirstDayOfWeek(1);
                    _calendar.setTime(e.getFindDate());
                    return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
                }).collect(Collectors.toList());

            }
        }

        List<ProductManagerProblemIssue> problemIssues = problemIssueService.list(lambdaQueryWrapper);

        /*
         * issueId 对应的产品状态
         */
        Map<Long, Long> productStates = new HashMap<Long, Long>(7) {
            {
                put(18903L, 1L);
                put(18899L, 2L);
                put(18900L, 3L);
                put(18901L, 4L);
                put(18902L, 5L);
                put(18986L, 6L);
                put(18904L, 7L);
                put(22100L, 8L);
            }
        };

        problemIssues.forEach(e -> {
            e.setProductCateMultiName(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductCateMultiName).findFirst().orElse(""));

            e.setProductSplitName(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductSplitName).findFirst().orElse(""));
            e.setParentDept(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getParentDept).findFirst().orElse(0L));

            e.setProductParentCate(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductParentCate).findFirst().orElse(""));

            e.setProductChildCate(
                    projects.stream().filter(_e -> _e.getIssueId().equals(e.getParentId()))
                            .map(ProductManager::getProductChildCate).findFirst().orElse(""));

            e.setProductState(
                    productStates.getOrDefault(Long.valueOf(Optional.ofNullable(e.getProductState()).orElse("0")), 0L)
                            + "");
        });

        return problemIssues;
    }
    /* 监控接口 end */

    /* 问题监控导出 start*/
    public void exportMoniterDetails(HttpServletResponse response, ProductManagerProblemIssue params) {

        List<ProductManagerProblemIssue> problemIssues = moniterDetails(params);

        List<ProductManagerProblemIssueParams> problems = new ArrayList<>();

        for (ProductManagerProblemIssue item : problemIssues) {
            problems.add(
                    ProductManagerProblemIssueParams.builder()
                            .productCateMultiName(item.getProductCateMultiName())
                            .productName(item.getProductName())
                            .productStateName(item.getProductStateName())
                            .problemDimensionName(item.getProblemDimensionName())
                            .problemStatusName(item.getProblemStatusName())
                            .problemDescription(item.getProblemDescription())
                            .causeAnalysis(item.getCauseAnalysis())
                            .problemSolving(item.getProblemSolving())
                            .productProcess(item.getProductProcess())
                            .responsiblePersonName(item.getResponsiblePersonName())
                            .findDate(item.getFindDate())
                            .plannedCompletionDate(item.getPlannedCompletionDate())
                            .actualCompletionDate(item.getActualCompletionDate())
                            .build());
        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("问题升级管理", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductManagerProblemIssueParams.class)
                    .sheet("问题升级管理")
                    .doWrite(problems);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /* 问题监控导出 end*/

    public List<ProductManager> getProcessForProject(ChartParam param) {
        List<Long> deptIds = Arrays.asList(22269L, 18711L, 22487L);
        LambdaQueryWrapper<ProductManager> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNotNull(ProductManager::getProductCateMulti);
        // lambdaQueryWrapper.in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L));
        lambdaQueryWrapper.in(ProductManager::getProductClassification, Arrays.asList(1, 2, 3, 4, 6));
        if (ObjectUtil.isNotNull(param)) {

            if (ObjectUtil.isNotEmpty(param.getProductProjectName())) {
                lambdaQueryWrapper.like(ProductManager::getProductProjectName, param.getProductProjectName());
            }

            if (ObjectUtil.isNotEmpty(param.getProjectName())) {
                lambdaQueryWrapper.like(ProductManager::getProjectName, param.getProjectName());
            }

            if (ObjectUtil.isNotEmpty(param.getDepts())) {
                lambdaQueryWrapper.in(ProductManager::getParentDept, param.getDepts());
            }
        }

        if (Optional.ofNullable(param).map(ChartParam::getDeptId).isPresent()) {
            lambdaQueryWrapper.eq(ProductManager::getParentDept, param.getDeptId() + "");
        }

        List<ProductManager> products = productManagerService.list(lambdaQueryWrapper);
        products = productMultiCateOfNotSplit(products);

        products = products.stream().filter(e -> deptIds.indexOf(e.getParentDept()) != -1).collect(Collectors.toList());

        // List<ProductStageItem> stages =
        // childIssueService.getStages(products.stream().map(ProductManager::getIssueId).collect(Collectors.toList()));

        // DateTimeFormatter strFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        products.forEach(e -> {

            e.setDelayDays(0L);

            LocalDate planDate = getProductCurStageDate(e);

            e.setDelayDays(planDate.until(LocalDate.now(), ChronoUnit.DAYS) < 0L ? 0L
                    : planDate.until(LocalDate.now(), ChronoUnit.DAYS));

            e.setPlanDate(planDate);

            /*
             * stages.stream().filter(s->s.getParentId().equals(e.getIssueId())).filter(s->s
             * .getStage().equals(e.getProductStage())).findFirst().ifPresent(val->{
             *
             * LocalDate actualDate =
             * Strings.isNullOrEmpty(val.getActualCompletionDate()) ||
             * val.getActualCompletionDate().equals("-")
             * ? LocalDate.now()
             * : LocalDate.parse(val.getActualCompletionDate(), strFormat);
             *
             * LocalDate planDate = Strings.isNullOrEmpty(val.getPlanReviewDate()) ||
             * val.getPlanReviewDate().equals("-")
             * ? LocalDate.now()
             * : LocalDate.parse(val.getPlanReviewDate(), strFormat);
             *
             * e.setDelayDays(planDate.until(actualDate, ChronoUnit.DAYS) < 0L ? 0L :
             * planDate.until(actualDate, ChronoUnit.DAYS));
             *
             * e.setPlanDate(planDate);
             *
             * });
             */
        });
        return products;
    }

    public List<Alter> getAltersForProduct(ChartParam params) {

        LambdaQueryWrapper<ProductManager> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductManager::getProductState, Arrays.asList(4, 5, 6, 7, 8))
                .eq(ProductManager::getProductOrProject, 1);

        LambdaQueryWrapper<SysBomHistory> bomhistoryqueryWrapper = Wrappers.<SysBomHistory>lambdaQuery()
                .ge(SysBomHistory::getProductState, 4)
                .eq(SysBomHistory::getType, 0)
                .eq(SysBomHistory::getIsAddWerk, 0);

        if (ObjectUtil.isNotNull(params)) {

            if (ObjectUtil.isNotEmpty(params.getDepts())) {
                queryWrapper.in(ProductManager::getParentDept, params.getDepts());
            }

            if (ObjectUtil.isNotEmpty(params.getProductProjectName())) {
                queryWrapper.like(ProductManager::getProductProjectName, params.getProductProjectName());
            }

            if (ObjectUtil.isNotEmpty(params.getAlterTypes())) {
                bomhistoryqueryWrapper.in(SysBomHistory::getAlterType, params.getAlterTypes());

            }
        }

        List<ProductManager> products = productManagerService.list(
                queryWrapper);
        products = productMultiCateOfNotSplit(products);

        List<Long> productIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());

        Map<Long, String> issueIdMapChildDept = products.stream().collect(
                Collectors.toMap(
                        ProductManager::getIssueId,
                        v -> v.getParentDeptName(),
                        (v1, v2) -> v1));

        Map<Long, String> issudeIdMapProductName = products.stream()
                .filter(e -> ObjectUtil.isNotNull(e.getProductSplitName())).collect(
                        Collectors.toMap(
                                ProductManager::getIssueId,
                                v -> v.getProductProjectName(),
                                (v1, v2) -> v1));

        if (ObjectUtil.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        List<SysBom> boms = bomService
                .list(Wrappers.<SysBom>lambdaQuery().ne(SysBom::getBomType, 1).in(SysBom::getBomIssueId, productIds));

        List<Long> bomIds = boms.stream().map(SysBom::getId).collect(Collectors.toList());

        Map<Long, Long> bomMapIssueId = boms.stream().collect(
                Collectors.toMap(
                        SysBom::getId,
                        v -> v.getBomIssueId(),
                        (v1, v2) -> v1));

        bomhistoryqueryWrapper.in(SysBomHistory::getBomId, bomIds);

        List<SysBomHistory> bomHistories = bomHistoryService.list(
                bomhistoryqueryWrapper);

        /*
         * if (params.getTechType().equals(2L)) {
         * return bomHistories.stream().map(item->{
         * return Alter.builder()
         * .product(
         * issudeIdMapProductName.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .alterType("BOM")
         * .alertTypeFlag(2)
         * .alterReason(item.getRemark())
         * .alterContent(item.getBomChange())
         * .alterDept(
         * issueIdMapChildDept.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .alterCheckDept(
         * issueIdMapChildDept.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .productState(item.getProductState())
         * .alterTypes(item.getAlterType())
         * .alterCharge(item.getSummitor())
         * .alterDate(item.getCreateTime())
         * .pushStatus(2)
         * .alterProcessDate(item.getCreateTime())
         * .build();
         * }).collect(Collectors.toList());
         * }
         */

        return bomHistories.stream().map(item -> {

            return Alter.builder()
                    .product(
                            issudeIdMapProductName.getOrDefault(
                                    bomMapIssueId.getOrDefault(item.getBomId(), 0L), ""))
                    .alterType("BOM")
                    .alertTypeFlag(2)
                    .alterReason(item.getRemark())
                    .alterContent(item.getBomChange())
                    .alterDept(
                            issueIdMapChildDept.getOrDefault(
                                    bomMapIssueId.getOrDefault(item.getBomId(), 0L), ""))
                    .alterCheckDept(
                            issueIdMapChildDept.getOrDefault(
                                    bomMapIssueId.getOrDefault(item.getBomId(), 0L), ""))
                    .productState(item.getProductState())
                    .alterTypes(item.getAlterType())
                    .alterCharge(item.getSummitor())
                    .alterDate(item.getCreateTime())
                    .pushStatus(2)
                    .alterProcessDate(item.getCreateTime())
                    .build();
        }).collect(Collectors.toList());

        /*
         * List<TechDoc> docList = techDocService.list(
         * Wrappers.<TechDoc>lambdaQuery()
         * .eq(TechDoc::getTechType, params.getTechType())
         * .in(TechDoc::getBomId, bomIds)
         * .in(TechDoc::getTechStatus, Arrays.asList(1,2))
         * );
         *
         * List<Long> docIds =
         * docList.stream().map(TechDoc::getId).collect(Collectors.toList());
         *
         * List<TechHistory> techHistories = techHistoryService.list(
         * Wrappers.<TechHistory>lambdaQuery()
         * .in(TechHistory::getTechId, docIds)
         * .isNotNull(TechHistory::getSummitor)
         * );
         *
         * return techHistories.stream().map(item->{
         * return Alter.builder()
         * .product(
         * issudeIdMapProductName.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .alterType(params.getTechType().equals(0L) ? "MI" : "图纸")
         * .alertTypeFlag(item.getType().intValue())
         * .alterReason(item.getRemark())
         * .alterContent(item.getRemark())
         * .alterDept(
         * issueIdMapChildDept.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .alterCheckDept(
         * issueIdMapChildDept.getOrDefault(
         * bomMapIssueId.getOrDefault(item.getBomId(), 0L),""
         * )
         * )
         * .productState(item.getProductState())
         * .alterTypes(null)
         * .alterCharge(item.getSummitor())
         * .alterDate(item.getCreateTime())
         * .pushStatus(2)
         * .alterProcessDate(item.getCreateTime())
         * .build();
         * }).collect(Collectors.toList());
         */
    }

    public List<ProductCateBean> getCates() {
        /* 构造前端多级表头 */
        List<ProductCateBean> nodes = new ArrayList<ProductCateBean>();

        List<Cate> cateBeans = customfieldoptionService.cateList("jmProductCate"); //获取荆门部门自定义字段

        List<ProductCateBean> productCateBeans = new ArrayList<>();

        for (Cate e : cateBeans) {
            productCateBeans.add(ProductCateBean.builder()
                    .id(e.getId())
                    .pid(e.getPid())
                    .field(e.getId() + "")
                    .key(e.getId() + "")
                    .title(e.getValue())
                    .build());
        }
        nodes.addAll(productCateBeans);

        nodes.forEach(e -> e.setWidth("110px"));

        return nodes;
    }

    public JSONObject getProductAlignTable(ProductManager param) {

        JSONObject resp = new JSONObject();
        List<Long> deptIds = Arrays.asList(22269L, 18711L, 22487L);

        List<ProductManager> _products = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .in(ProductManager::getParentDept, deptIds) // 部门id为荆门产品部门
                        .ne(ProductManager::getProductClassification, 1) // 产品分类不等于1
                        .isNotNull(ProductManager::getProductCateMulti) // 细分市场，产品多选字段不为空
        );

        _products = splitByProductMultiCate(_products); // 拆分产品（***将多个细分市场会拆成多条***）

        //查询为空直接返回
        if (_products.isEmpty()) return resp;

        // 过滤项目，获取产品
        List<ProductManager> products = _products.stream()
                .filter(e -> e.getProductOrProject().equals(1L)) // 1 产品，2 项目
                .collect(Collectors.toList());

        /* 产品类别的图表计数数据 -- start */
        // 1 预研产品（已禁用），2 A|B新产品，3 试产新产品，4 量产品，5 其他（已禁用），6 停止，7 立项讨论
        JSONObject chatDatas = new JSONObject();
        for (int i = 0; i < 7; i++) {
            final int classification = i + 1;
            long count = products.stream()
                    .filter(e -> e.getProductClassification() == classification)
                    .map(ProductManager::getId)         // 获取ID
                    .distinct()                         // 去重
                    .count();                           // 计数
            chatDatas.put(classification + "", count);
        }

        long distinctProductCount = products.stream()
                .map(ProductManager::getId)         // 获取ID
                .distinct()                         // 去重
                .count();                           // 计数
        chatDatas.put("z", distinctProductCount); // 总数
        /* 产品类别的图表数据 -- end */

        // 前端传入筛选查询条件
        if (ObjectUtil.isNotNull(param)) {
            // 初始未过滤
            Predicate<ProductManager> filter = e -> true;

            // 产品分类
            if (ObjectUtil.isNotEmpty(param.getProductClassifications())) {
                filter = filter.and(e -> param.getProductClassifications().contains(e.getProductClassification()));
            }

            // 产品分布
            if (ObjectUtil.isNotEmpty(param.getProductDistributes())) {
                filter = filter.and(e -> param.getProductDistributes().contains(e.getProductDistribute()));
            }

            // 日期过滤
            if (ObjectUtil.isNotEmpty(param.getStartDate())) {
                filter = filter.and(e -> ObjectUtil.isNotNull(e.getInitiationDate()))
                        .and(e -> e.getInitiationDate().compareTo(param.getStartDate()) >= 0)
                        .and(e -> e.getInitiationDate().compareTo(param.getEndDate()) <= 0);
            }

            // 关键字查询
            if (null != param.getKeyword()) {
                String keywordLower = param.getKeyword().toLowerCase();
                filter = filter.and(e -> e.getProductProjectName().toLowerCase().contains(keywordLower));
            }

            // 部门筛选
            if (ObjectUtil.isNotEmpty(param.getParentDepts())) {
                filter = filter.and(e -> param.getParentDepts().contains(e.getParentDept()));
            }

            // 汇总筛选查询结果
            products = products.stream()
                    .filter(filter)
                    .collect(Collectors.toList());
        }

        /* 产品类别对应的自定义排序 */
        Map<Long, Integer> sortMap = new HashMap<Long, Integer>() {{
            put(7L, 0);
            put(1L, 1);
            put(2L, 2);
            put(3L, 3);
            put(4L, 4);
            put(5L, 5);
            put(6L, 6);
        }};

        // 获取问题ID
        List<Long> issueIds = products.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
        // 按问题ID获取周进展
        List<WeekProcessDetail> weekProcesses = iWeekProcessDetailService.list(Wrappers.<WeekProcessDetail>lambdaQuery().in(WeekProcessDetail::getIssueId, issueIds));

        for (ProductManager item : products) {
            int state = weekProcesses.stream() //按周处理中获取最新的产品状态
                    .filter(e -> null != e.getProductState())
                    .filter(e -> e.getIssueId().equals(item.getIssueId()))
                    .sorted((o1, o2) -> o2.getWeekDate().compareTo(o1.getWeekDate())) //按日期倒序
                    .map(e -> Integer.valueOf(e.getProductState())).findFirst().orElse(-1);
            item.setWellState(state); // 设置状态

            item.setProjectIds(new ArrayList<>());
            item.getProjectIds().add(item.getIssueId());
            item.setSort(sortMap.getOrDefault(item.getProductClassification(), 0));
            _products.stream()
                    .filter(i -> i.getProductProjectName().equals(item.getProductProjectName()))
                    .filter(i -> i.getProductOrProject().equals(2L)) // 项目2
                    .forEach(_i -> {item.getProjectIds().add(_i.getIssueId());});
        }

        // 改为按产品分类排序
        products = products.stream()
                .sorted(Comparator
                        .comparing(ProductManager::getSort) // 先按自定义排序
                        .thenComparing(ProductManager::getProductProjectName)) // 再按名称
                .collect(Collectors.toList());

        /* 列表数据-- start */
        JSONArray arr = new JSONArray();
        Map<String, List<ProductManager>> mapProduct = products.stream()
                .collect(Collectors.groupingBy(ProductManager::getProductChildCate));

        // 同一列的重复数据过滤
        mapProduct.replaceAll((key, list) ->
                list.stream().collect(Collectors.toMap(
                                ProductManager::getProductProjectName, // 使用 getProductChildCate 作为键
                                Function.identity(),     // 使用自身作为值
                                (existing, replacement) -> existing)) // 选择第一个出现的元素
                        .values().stream().sorted(Comparator
                                        .comparing(ProductManager::getSort)
                                        .thenComparing(ProductManager::getProductProjectName))
                        .collect(Collectors.toList())
        );

        // 确定最大行数（列中最长列表）
        int maxSize = mapProduct.values().stream().mapToInt(List::size).max().orElse(0);

        // 表格 行数据
        for (int i = 0; i < maxSize; i++) {
            JSONObject jObject = new JSONObject();

            for (String key : mapProduct.keySet()) {
                List<ProductManager> colData = mapProduct.get(key);
                if (i < colData.size()) {
                    jObject.put(key, colData.get(i).getProductProjectName());
                    jObject.put(key+"_id", colData.get(i).getIssueId());
                    jObject.put(key+"_key", colData.get(i).getProductChildCate());
                    jObject.put(key+"_pid",colData.get(i).getProductChildCate());
                    jObject.put(key+"_productClassification", colData.get(i).getProductClassification());
                    jObject.put(key + "_isAllow", 1);
                    jObject.put(key+"_wellState", colData.get(i).getWellState());
                    jObject.put(key + "_projectIds", colData.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                }else{
                    jObject.put(key,"");
                }
            }
            arr.add(jObject);
        }
        /* 列表数据-- end */

        /* 表头处理-- start */
        List<ProductCateBean> cateBeans = getCates(); // 获取所有类别
        cateBeans.forEach(e -> e.setPid(e.getPid().equals(1L) ? 0L : e.getPid()));
        /* 获取children为空的数据，这集合是对应前端需要展示数据的表头列字段 */
        List<ProductCateBean> ProductCateBeans = Utils.getChilds(cateBeans);
        List<ProductCateBean> cateTree = Utils.buildTree(cateBeans);

        // 对类别id为18884（乘用车）
// 20250703
//        ProductCateBean _cate = cateTree.stream().filter(e->e.getId().equals(18884L)).findFirst().orElse(new ProductCateBean());
//        _cate.setChildren(
//            Arrays.asList(
//                ProductCateBean.builder()
//                .id(3L)
//                .pid(18884L)
//                .field("3")
//                .key("3")
//                .title("方形")
//                .build()
//            )
//        );
        /* 表头处理-- end */

        resp.put("tablecolumns", cateTree.stream().collect(Collectors.toList()));
        resp.put("tabledatas", arr);
        resp.put("rows", maxSize);
        resp.put("columns", ProductCateBeans.size() - 1);
        resp.put("chartdatas", chatDatas);
        return resp;
    }

    public List<ProductManager> getStageDocsDetail() {

        List<ProductManager> projects = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getParentDept,
                                Arrays.asList("22269","18711","22487")));
        projects = productMultiCateOfNotSplit(projects);

        if (projects.size() == 0) {
            return null;
        }

        List<Long> ids = projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
        List<Long> docIsNeedList = Arrays.asList(1L, 2L);
        LambdaQueryWrapper<Docs> docLambdaQueryWrapper = new LambdaQueryWrapper<>();
        docLambdaQueryWrapper.select(Docs::getIssueId, Docs::getIsOver, Docs::getDocIsNeed, Docs::getScore,
                Docs::getStage, Docs::getVersion, Docs::getVersionId);
        docLambdaQueryWrapper.in(Docs::getIssueId, ids);
        docLambdaQueryWrapper.in(Docs::getDocIsNeed, docIsNeedList);
        docLambdaQueryWrapper.apply("VERSION = VERSION_ID");
        List<Docs> docs = docsService.list(docLambdaQueryWrapper);

        for (ProductManager projectItem : projects) {

            long k0Count = docs.stream().filter(e -> e.getStage().equals(1L)
                            && e.getIssueId().equals(projectItem.getIssueId()) && docIsNeedList.contains(e.getDocIsNeed()))
                    .count() * 10L;
            long m1Count = docs.stream().filter(e -> e.getStage().equals(2L)
                            && e.getIssueId().equals(projectItem.getIssueId()) && docIsNeedList.contains(e.getDocIsNeed()))
                    .count() * 10L;
            long m2Count = docs.stream().filter(e -> e.getStage().equals(5L)
                            && e.getIssueId().equals(projectItem.getIssueId()) && docIsNeedList.contains(e.getDocIsNeed()))
                    .count() * 10L;
            long m3Count = docs.stream().filter(e -> e.getStage().equals(8L)
                            && e.getIssueId().equals(projectItem.getIssueId()) && docIsNeedList.contains(e.getDocIsNeed()))
                    .count() * 10L;

            long k0sum = docs.stream()
                    .filter(e -> null != e.getScore() && e.getStage().equals(1L)
                            && e.getIssueId().equals(projectItem.getIssueId())
                            && docIsNeedList.contains(e.getDocIsNeed()))
                    .mapToLong(Docs::getScore).sum();
            long m1sum = docs.stream()
                    .filter(e -> null != e.getScore() && e.getStage().equals(2L)
                            && e.getIssueId().equals(projectItem.getIssueId())
                            && docIsNeedList.contains(e.getDocIsNeed()))
                    .mapToLong(Docs::getScore).sum();
            long m2sum = docs.stream()
                    .filter(e -> null != e.getScore() && e.getStage().equals(5L)
                            && e.getIssueId().equals(projectItem.getIssueId())
                            && docIsNeedList.contains(e.getDocIsNeed()))
                    .mapToLong(Docs::getScore).sum();
            long m3sum = docs.stream()
                    .filter(e -> null != e.getScore() && e.getStage().equals(8L)
                            && e.getIssueId().equals(projectItem.getIssueId())
                            && docIsNeedList.contains(e.getDocIsNeed()))
                    .mapToLong(Docs::getScore).sum();

            projectItem.setK0Reach(k0Count == 0L ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(k0sum).divide(BigDecimal.valueOf(k0Count), 4, BigDecimal.ROUND_HALF_UP));
            projectItem.setM1Reach(m1Count == 0L ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m1sum).divide(BigDecimal.valueOf(m1Count), 4, BigDecimal.ROUND_HALF_UP));
            projectItem.setM2Reach(m2Count == 0L ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m2sum).divide(BigDecimal.valueOf(m2Count), 4, BigDecimal.ROUND_HALF_UP));
            projectItem.setM3Reach(m3Count == 0L ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m3sum).divide(BigDecimal.valueOf(m3Count), 4, BigDecimal.ROUND_HALF_UP));
        }
        return projects;
    }

    public List<ProductManager> getStageDocsDetailOfNotSplit(ProductManager param) {
        LambdaQueryWrapper<ProductManager> productManagerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productManagerLambdaQueryWrapper.eq(ProductManager::getProductOrProject, 1);
        productManagerLambdaQueryWrapper.isNotNull(ProductManager::getProductCateMulti);
        productManagerLambdaQueryWrapper.in(ProductManager::getParentDept,
                Arrays.asList(22269L, 18711L, 22487L));
        if (ObjectUtil.isNotNull(param)) {

            if (ObjectUtil.isNotEmpty(param.getProductStages())) {
                productManagerLambdaQueryWrapper.in(ProductManager::getProductStage, param.getProductStages());
            }
            if (ObjectUtil.isNotEmpty(param.getParentDepts())) {
                productManagerLambdaQueryWrapper.in(ProductManager::getParentDept, param.getParentDepts());
            }
            if (ObjectUtil.isNotEmpty(param.getProductProjectName())) {
                productManagerLambdaQueryWrapper.like(ProductManager::getProductProjectName,
                        param.getProductProjectName());
            }
        }

        List<ProductManager> projects = productManagerService.list(productManagerLambdaQueryWrapper);
        projects = productMultiCateOfNotSplit(projects);

        if (projects.size() == 0) {
            return projects;
        }

        List<Long> ids = projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList());
        List<Long> docIsNeedList = Arrays.asList(1L, 2L);
        LambdaQueryWrapper<Docs> docLambdaQueryWrapper = new LambdaQueryWrapper<>();
        docLambdaQueryWrapper.select(Docs::getIssueId, Docs::getIsOver, Docs::getDocIsNeed, Docs::getScore,
                Docs::getStage, Docs::getVersion, Docs::getVersionId);
        docLambdaQueryWrapper.in(Docs::getIssueId, ids);
        docLambdaQueryWrapper.in(Docs::getDocIsNeed, docIsNeedList);
        docLambdaQueryWrapper.apply("VERSION = VERSION_ID");
        docLambdaQueryWrapper.in(Docs::getStage, Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8));
        List<Docs> docs = docsService.list(docLambdaQueryWrapper);

        List<DocTargetVersion> docsVersion = docTargetVersionService.getListByIssueIds(ids);

        // 阶段
        Map<String, String> dictMap = dataService.getDictDataByType("product_stage_status_belong");
        Map<Long, List<Long>> belongList = new HashMap<>();
        dictMap.forEach((key, value) -> {
            Long val = Long.valueOf(value);
            belongList.computeIfAbsent(val, k -> new ArrayList<>()).add(Long.valueOf(key));
        });



        for (ProductManager projectItem : projects) {
            // 过滤版本 三个维度 issueId,stage,version
            Map<String, String> versionStream = docsVersion.stream()
                    .filter(e -> e.getIssueId().equals(projectItem.getIssueId())).collect(Collectors.toMap(
                            e -> String.valueOf(e.getStage()),
                            e -> String.valueOf(e.getVersion()),
                            (existing, replacement) -> existing));
            if (ObjectUtil.isEmpty(versionStream)) {
                continue;
            }

            // 第一次基础过滤
            List<Docs> docsStream = docs.stream().filter(
                            e -> e.getIssueId().equals(projectItem.getIssueId()) && docIsNeedList.contains(e.getDocIsNeed()))
                    .collect(Collectors.toList());
            // 过滤阶段
            List<Docs> docsStream1 = docsStream.stream().filter(e -> belongList.get(1L).contains(e.getStage()))
                    .collect(Collectors.toList());
            List<Docs> docsStream2 = docsStream.stream().filter(e -> belongList.get(2L).contains(e.getStage()))
                    .collect(Collectors.toList());
            List<Docs> docsStream5 = docsStream.stream().filter(e -> belongList.get(5L).contains(e.getStage()))
                    .collect(Collectors.toList());
            List<Docs> docsStream8 = docsStream.stream().filter(e -> belongList.get(8L).contains(e.getStage()))
                    .collect(Collectors.toList());

            // 拿到需统计的List<Docs>
            List<Docs> docs1 = docsStream1.stream()
                    .filter(e -> String.valueOf(e.getVersion()).equals(versionStream.get(String.valueOf(e.getStage()))))
                    .collect(Collectors.toList());
            List<Docs> docs2 = docsStream2.stream()
                    .filter(e -> String.valueOf(e.getVersion()).equals(versionStream.get(String.valueOf(e.getStage()))))
                    .collect(Collectors.toList());
            List<Docs> docs5 = docsStream5.stream()
                    .filter(e -> String.valueOf(e.getVersion()).equals(versionStream.get(String.valueOf(e.getStage()))))
                    .collect(Collectors.toList());
            List<Docs> docs8 = docsStream8.stream()
                    .filter(e -> String.valueOf(e.getVersion()).equals(versionStream.get(String.valueOf(e.getStage()))))
                    .collect(Collectors.toList());

            long k0sum = docs1.stream().filter(e -> null != e.getScore()).mapToLong(Docs::getScore).sum();
            long m1sum = docs2.stream().filter(e -> null != e.getScore()).mapToLong(Docs::getScore).sum();
            long m2sum = docs5.stream().filter(e -> null != e.getScore()).mapToLong(Docs::getScore).sum();
            long m3sum = docs8.stream().filter(e -> null != e.getScore()).mapToLong(Docs::getScore).sum();

            projectItem.setK0Reach(docs1.size() == 0L || k0sum == 0l ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(k0sum).divide(BigDecimal.valueOf(docs1.size() * 10L), 4,
                    BigDecimal.ROUND_HALF_UP));
            projectItem.setM1Reach(docs2.size() == 0L || m1sum == 0l ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m1sum).divide(BigDecimal.valueOf(docs2.size() * 10L), 4,
                    BigDecimal.ROUND_HALF_UP));
            projectItem.setM2Reach(docs2.size() == 0L || m2sum == 0l ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m2sum).divide(BigDecimal.valueOf(docs5.size() * 10L), 4,
                    BigDecimal.ROUND_HALF_UP));
            projectItem.setM3Reach(docs2.size() == 0L || m3sum == 0l ? BigDecimal.valueOf(0L)
                    : BigDecimal.valueOf(m3sum).divide(BigDecimal.valueOf(docs8.size() * 10L), 4,
                    BigDecimal.ROUND_HALF_UP));
        }
        return projects;
    }

    /* 文件传递平台 -- 产品参数传递 */
    public List<JSONObject> getProductParams(ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .ne(ProductManager::getParentDept, 18863)
                        .isNotNull(ProductManager::getProductCateMulti));
        projects = splitByProductMultiCateByTrans(projects);

        if (projects.size() == 0) {
            return null;
        }

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<TechDoc> techHistories = techDocService.list(
                Wrappers.lambdaQuery(TechDoc.class)
                        .eq(TechDoc::getTechType, 4)

                        .in(TechDoc::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // 转阶段对应的产品状态 A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<JSONObject> jsonArray = new ArrayList<>();

        int i = 0;

        for (ProductManager e : projects) {

            String params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            Optional<SysProductParam> first = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst();

            Long id = null;
            if (first.isPresent()) {
                id = first.get().getId();
            } else {
                SysProductParam productParam = new SysProductParam();
                productParam.setIssueId(e.getIssueId());
                productParam.setStage(stateMap.getOrDefault(e.getProductStage().intValue(), 0L));
                SysProductParam sysProductParam = sysProductParamService.getProductParam(productParam);
                id = sysProductParam.getId();
            }

            if (!params.isEmpty() && '[' == params.charAt(0)) {
                StringBuilder sb = new StringBuilder(params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                params = sb.toString();
            }

            Optional<TechDoc> opTechHistory = techHistories.stream()
                    .filter(item -> item.getIssueId().equals(e.getIssueId()))
                    .filter(item -> item.getProductState()
                            .equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .sorted(Comparator.comparing(TechDoc::getUpdateTime).reversed()) // 拿最新的
                    .findFirst();

            JSONObject obj = !params.isEmpty() ? (JSONObject) JSONObject.parse(params) : new JSONObject();
            i++;
            obj.put("no", i);
            obj.put("id", id);
            obj.put("issueId", e.getIssueId());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productOrProject", e.getProductOrProject());
            obj.put("productProjectName", e.getProductProjectName());
            obj.put("oproductProjectName",
                    e.getProductProjectName().substring(0,
                            e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                    : e.getProductProjectName().length()));
            obj.put("productVersion",
                    e.getProductProjectName()
                            .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                    : 0, e.getProductProjectName().length()));
            obj.put("productStateName", e.getProductStateName());
            obj.put("projectName", e.getProjectName());
            obj.put("productChildCate", e.getProductChildCate());
            obj.put("productClassification", e.getProductClassification());
            obj.put("productDistribute", e.getProductDistribute());
            obj.put("productParentCate", e.getProductParentCate());
            obj.put("productLevel", e.getProjectLevel());
            obj.put("mStatus", e.getMStatus());
            obj.put("parentDept", e.getParentDept());
            obj.put("customer", e.getCustomer());
            obj.put("initiationDate", e.getInitiationDate());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productState", e.getProductState());
            obj.put("docName", opTechHistory.isPresent() ? opTechHistory.get().getTechName() : "");
            obj.put("docId", opTechHistory.isPresent() ? opTechHistory.get().getTechDocFileId() : 0);
            obj.put("cateId", e.getCateId());
            obj.put("cateIds", e.getCateIds());

            if (e.getProductSplitName().indexOf("-") != -1) {
                String[] splits = e.getProductSplitName().split("-");
                obj.put("parentCateName", splits[0]);
                obj.put("childCateName", splits[1]);
            } else {
                obj.put("parentCateName", e.getProductSplitName());
            }

            jsonArray.add(obj);
        }

        if (Objects.nonNull(param)) {
            if (!CollectionUtils.isEmpty(param.getCateIds())) {
                List<Long> cateIds = param.getCateIds().stream().filter(e -> e > 5L).collect(Collectors.toList());
                List<Long> typeIds = param.getCateIds().stream().filter(e -> e < 6L).collect(Collectors.toList());
                List<JSONObject> jsonArr = new ArrayList<>();
                if (!cateIds.isEmpty()) {
                    jsonArr = jsonArray.stream()
                            .filter(obj -> cateIds.indexOf(obj.getLong("productChildCate")) != -1
                                    || cateIds.indexOf(obj.getLong("productParentCate")) != -1)
                            .collect(Collectors.toList());
                }
                List<JSONObject> _jsonArr = new ArrayList<>();
                if (!typeIds.isEmpty()) {
                    _jsonArr = jsonArray.stream().filter(obj -> typeIds.indexOf(obj.getLong("productType")) != -1)
                            .collect(Collectors.toList());
                }
                for (JSONObject jsonObject : _jsonArr) {
                    if (!jsonArr.stream()
                            .filter(e -> (e.getString("issueId") + e.getString("productChildCate"))
                                    .equals(jsonObject.getString("issueId") + jsonObject.getString("productChildCate")))
                            .findFirst().isPresent()) {
                        jsonArr.add(jsonObject);
                    }
                }

                return jsonArr;
            } else if (Objects.nonNull(param.getCateIds()) && param.getCateIds().isEmpty()) {
                return new ArrayList<>();
            }
        }

        return jsonArray;
    }

    /* 文件传递平台 -- 产品规格书传递 */
    public List<JSONObject> getProductParamsOfNotSplitByFileTrans(ProductManager param) {

        LambdaQueryWrapper<ProductManager> queryWrapper = Wrappers.<ProductManager>lambdaQuery()
                .isNotNull(ProductManager::getProductCateMulti);

        queryWrapper.ne(ProductManager::getParentDept, 18863);

        if (!CollectionUtils.isEmpty(param.getProductStates())) {
            queryWrapper.in(ProductManager::getProductState, param.getProductStates());
        }

        List<ProductManager> projects = productManagerService.list(queryWrapper);
        projects = productMultiCateOfNotSplitByTrans(projects);

        if (projects.size() == 0) {
            return null;
        }

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<TechDoc> techHistories = techDocService.list(
                Wrappers.lambdaQuery(TechDoc.class)
                        .eq(TechDoc::getTechType, 4)
                        .eq(TechDoc::getTechStatus, 2)

                        .in(TechDoc::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // 转阶段对应的产品状态 A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<JSONObject> jsonArray = new ArrayList<>();

        int i = 0;

        for (ProductManager e : projects) {

            String params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            Optional<SysProductParam> first = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst();

            Long id = null;
            if (first.isPresent()) {
                id = first.get().getId();
            } else {
                SysProductParam productParam = new SysProductParam();
                productParam.setIssueId(e.getIssueId());
                productParam.setStage(stateMap.getOrDefault(e.getProductStage().intValue(), 0L));
                SysProductParam sysProductParam = sysProductParamService.getProductParam(productParam);
                id = sysProductParam.getId();
            }

            if (!params.isEmpty() && '[' == params.charAt(0)) {
                StringBuilder sb = new StringBuilder(params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                params = sb.toString();
            }

            Optional<TechDoc> opTechHistory = techHistories.stream()
                    .filter(item -> item.getIssueId().equals(e.getIssueId()))
                    .filter(item -> item.getProductState()
                            .equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .sorted(Comparator.comparing(TechDoc::getUpdateTime).reversed()) // 拿最新的
                    .findFirst();

            JSONObject obj = !params.isEmpty() ? (JSONObject) JSONObject.parse(params) : new JSONObject();
            i++;
            obj.put("no", i);
            obj.put("id", id);
            obj.put("issueId", e.getIssueId());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productOrProject", e.getProductOrProject());
            obj.put("productProjectName", e.getProductProjectName());
            obj.put("oproductProjectName",
                    e.getProductProjectName().substring(0,
                            e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                    : e.getProductProjectName().length()));
            obj.put("productVersion",
                    e.getProductProjectName()
                            .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                    : 0, e.getProductProjectName().length()));
            obj.put("productStateName", e.getProductStateName());
            obj.put("projectName", e.getProjectName());
            obj.put("productChildCate", e.getProductChildCate());
            obj.put("productClassification", e.getProductClassification());
            obj.put("productDistribute", e.getProductDistribute());
            obj.put("productParentCate", e.getProductParentCate());
            obj.put("productLevel", e.getProjectLevel());
            obj.put("mStatus", e.getMStatus());
            obj.put("parentDept", e.getParentDept());
            obj.put("customer", e.getCustomer());
            obj.put("initiationDate", e.getInitiationDate());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productState", e.getProductState());
            obj.put("docName", opTechHistory.isPresent() ? opTechHistory.get().getTechName() : "");
            obj.put("docId", opTechHistory.isPresent() ? opTechHistory.get().getTechDocFileId() : 0);
            obj.put("cateId", e.getCateId());
            obj.put("cateIds", e.getCateIds());

            if (e.getProductSplitName().indexOf("-") != -1) {
                String[] splits = e.getProductSplitName().split("-");
                obj.put("parentCateName", splits[0]);
                obj.put("childCateName", splits[1]);
            } else {
                obj.put("parentCateName", e.getProductSplitName());
            }

            jsonArray.add(obj);
        }

        if (Objects.nonNull(param)) {
            if (!CollectionUtils.isEmpty(param.getCateIds())) {
                List<Long> cateIds = param.getCateIds().stream().filter(e -> e > 5L).collect(Collectors.toList());
                List<Long> typeIds = param.getCateIds().stream().filter(e -> e < 6L).collect(Collectors.toList());
                List<JSONObject> jsonArr = new ArrayList<>();
                if (!cateIds.isEmpty()) {
                    jsonArr = jsonArray.stream()
                            .filter(obj -> cateIds.indexOf(obj.getLong("productChildCate")) != -1
                                    || cateIds.indexOf(obj.getLong("productParentCate")) != -1)
                            .collect(Collectors.toList());
                }
                List<JSONObject> _jsonArr = new ArrayList<>();
                if (!typeIds.isEmpty()) {
                    _jsonArr = jsonArray.stream().filter(obj -> typeIds.indexOf(obj.getLong("productType")) != -1)
                            .collect(Collectors.toList());
                }
                for (JSONObject jsonObject : _jsonArr) {
                    if (!jsonArr.stream()
                            .filter(e -> (e.getString("issueId") + e.getString("productChildCate"))
                                    .equals(jsonObject.getString("issueId") + jsonObject.getString("productChildCate")))
                            .findFirst().isPresent()) {
                        jsonArr.add(jsonObject);
                    }
                }

                return jsonArr;
            } else if (Objects.nonNull(param.getCateIds()) && param.getCateIds().isEmpty()) {
                return new ArrayList<>();
            }
        }

        return jsonArray;
    }

    /* 文件传递平台 --产品规格书 测试数据传递  产品数据授权 */
    public List<JSONObject> getProductParamsOfTest(ProductManager param) {
        List<ProductManager> projects = productManagerService.list(
                Wrappers.<ProductManager>lambdaQuery()
                        .ne(ProductManager::getParentDept, 18863)
                        .isNotNull(ProductManager::getProductCateMulti));
        projects = productMultiCateOfNotSplitByTrans(projects);

        if (projects.size() == 0) {
            return null;
        }

        List<ProductManager> productManagers = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .collect(Collectors.toList());

        for (ProductManager item : productManagers) {
            item.setParentId(0L);
            List<ProductManager> childrens = projects.stream()
                    .filter(e -> !e.getProductOrProject().equals(1L)
                            && e.getProductProjectName().equals(item.getProductProjectName()))
                    .collect(Collectors.toList());

            item.setHasChild(childrens.size() > 0 ? 1 : 0);
            for (ProductManager _item : childrens) {
                _item.setParentId(item.getIssueId());
                _item.setHasChild(0);
            }
        }

        projects = projects.stream().sorted(Comparator.comparing(ProductManager::getProductProjectName)
                .thenComparing(ProductManager::getProductOrProject)).collect(Collectors.toList());

        List<Long> ids = projects.stream().map(ProductManager::getId).collect(Collectors.toList());

        List<String> docsNames = Arrays.asList("DV测试报告(A样)", "DV测试报告(B样)", "A样测试报告", "B样测试报告");

        LambdaQueryWrapper<Docs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.apply("VERSION = VERSION_ID");
        queryWrapper.in(Docs::getStage, Arrays.asList(4, 5, 7, 8));
        queryWrapper.in(Docs::getIssueId, ids);
        queryWrapper.in(Docs::getOutputFile, docsNames);

        List<Docs> docs = docsService.list(queryWrapper);

        List<TechDoc> techHistories = techDocService.list(
                Wrappers.lambdaQuery(TechDoc.class)
                        .in(TechDoc::getTechType, Arrays.asList(4, 13))
                        .eq(TechDoc::getTechStatus, 2)
                        .in(TechDoc::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        List<SysFileInfo> fileInfos = techHistories.isEmpty() ? new ArrayList<>()
                : fileInfoService.list(
                Wrappers.lambdaQuery(SysFileInfo.class)
                        .in(
                                SysFileInfo::getId,
                                techHistories.stream()
                                        .map(TechDoc::getTechDocFileId)
                                        .collect(Collectors.toList())));

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // 转阶段对应的产品状态 A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<JSONObject> jsonArray = new ArrayList<>();

        int i = 0;

        for (ProductManager e : projects) {

            // 4:DF-A 5 7:DF-B 8
            List<Docs> _docs = docs.stream().filter(d -> e.getId().equals(d.getIssueId()))
                    .sorted(Comparator.comparing(Docs::getStage)).collect(Collectors.toList());
            Docs m2Doc = null;
            Docs m3Doc = null;

            if (_docs.size() > 0) {
                if (_docs.get(0).getVersion() > 2) {
                    _docs = _docs.stream().filter(d -> Arrays.asList(4, 7).indexOf(d.getStage().intValue()) > -1)
                            .collect(Collectors.toList());
                }
                if (_docs.size() > 0) {
                    m2Doc = _docs.get(0);
                    if (_docs.size() > 1) {
                        m3Doc = _docs.get(1);
                    }
                }

            }

            String params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            Optional<SysProductParam> first = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst();

            Long id = null;
            if (first.isPresent()) {
                id = first.get().getId();
            } else {
                SysProductParam productParam = new SysProductParam();
                productParam.setIssueId(e.getIssueId());
                productParam.setStage(stateMap.getOrDefault(e.getProductStage().intValue(), 0L));
                SysProductParam sysProductParam = sysProductParamService.getProductParam(productParam);
                id = sysProductParam.getId();
            }

            if (!params.isEmpty() && '[' == params.charAt(0)) {
                StringBuilder sb = new StringBuilder(params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                params = sb.toString();
            }

            Optional<TechDoc> opTechHistory = techHistories.stream()
                    .filter(item -> item.getIssueId().equals(e.getIssueId()))
                    .filter(item -> item.getTechType() == 4)
                    .filter(item -> item.getProductState()
                            .equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .sorted(Comparator.comparing(TechDoc::getUpdateTime).reversed()) // 拿最新的
                    .findFirst();

            opTechHistory.ifPresent(h -> {
                fileInfos.stream().filter(f -> f.getId().equals(h.getTechDocFileId())).findFirst().ifPresent(f -> {
                    h.setTechName(f.getFileOriginName());
                });
            });

            Optional<TechDoc> opMiTechHistory = techHistories.stream()
                    .filter(item -> item.getIssueId().equals(e.getIssueId()))
                    .filter(item -> item.getTechType() == 13)
                    .filter(item -> item.getProductState()
                            .equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .sorted(Comparator.comparing(TechDoc::getUpdateTime).reversed()) // 拿最新的
                    .findFirst();

            opMiTechHistory.ifPresent(h -> {
                fileInfos.stream().filter(f -> f.getId().equals(h.getTechDocFileId())).findFirst().ifPresent(f -> {
                    h.setTechName(f.getFileOriginName());
                });
            });

            JSONObject obj = !params.isEmpty() ? (JSONObject) JSONObject.parse(params) : new JSONObject();
            i++;
            obj.put("no", i);
            obj.put("id", id);
            obj.put("parentId", e.getParentId());
            obj.put("hasChild", e.getHasChild());
            obj.put("expanded", false);
            obj.put("issueId", e.getIssueId());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productOrProject", e.getProductOrProject());
            obj.put("productProjectName", e.getProductProjectName());
            obj.put("oproductProjectName",
                    e.getProductProjectName().substring(0,
                            e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                    : e.getProductProjectName().length()));
            obj.put("productVersion",
                    e.getProductProjectName()
                            .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                    : 0, e.getProductProjectName().length()));
            obj.put("productStateName", e.getProductStateName());
            obj.put("projectName", e.getProjectName());
            obj.put("productChildCate", e.getProductChildCate());
            obj.put("productClassification", e.getProductClassification());
            obj.put("productDistribute", e.getProductDistribute());
            obj.put("productParentCate", e.getProductParentCate());
            obj.put("productLevel", e.getProjectLevel());
            obj.put("mStatus", e.getMStatus());
            obj.put("parentDept", e.getParentDept());
            obj.put("customer", e.getCustomer());
            obj.put("initiationDate", e.getInitiationDate());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productState", e.getProductState());
            obj.put("docName", opTechHistory.isPresent() ? opTechHistory.get().getTechName() : "");
            obj.put("docId", opTechHistory.isPresent() ? opTechHistory.get().getTechDocFileId() : 0);

            obj.put("miDoc", opMiTechHistory.map(TechDoc::getTechName).orElse(""));
            obj.put("miDocId", opMiTechHistory.map(TechDoc::getTechDocFileId).orElse(0L));

            obj.put("cateId", e.getCateId());
            obj.put("cateIds", e.getCateIds());
            obj.put("m2Doc", m2Doc != null ? m2Doc.getDocName() : "");
            obj.put("m2DocId", m2Doc != null ? m2Doc.getFileId() : 0);
            obj.put("m3Doc", m3Doc != null ? m3Doc.getDocName() : "");
            obj.put("m3DocId", m3Doc != null ? m3Doc.getFileId() : 0);

            if (e.getProductSplitName().indexOf("-") != -1) {
                String[] splits = e.getProductSplitName().split("-");
                obj.put("parentCateName", splits[0]);
                obj.put("childCateName", splits[1]);
            } else {
                obj.put("parentCateName", e.getProductSplitName());
            }

            jsonArray.add(obj);
        }

        if (Objects.nonNull(param)) {

            if (Objects.nonNull(param.getCateIds()) && !CollectionUtils.isEmpty(param.getCateIds())) {
                List<JSONObject> jsonArr = new ArrayList<>();
                List<Long> cateIds = param.getCateIds().stream().filter(e -> e > 5L).collect(Collectors.toList());
                List<Long> typeIds = param.getCateIds().stream().filter(e -> e < 6L).collect(Collectors.toList());
                if (!cateIds.isEmpty()) {
                    jsonArr = jsonArray.stream().filter(obj -> {
                        List<Long> cateids = JSONObject.parseArray(obj.getString("cateIds"), Long.class);
                        return cateids.stream().anyMatch(cateIds::contains);
                    }).collect(Collectors.toList());
                }
                List<JSONObject> _jsonArr = new ArrayList<>();
                if (!typeIds.isEmpty()) {
                    _jsonArr = jsonArray.stream().filter(obj -> typeIds.indexOf(obj.getLong("productType")) != -1)
                            .collect(Collectors.toList());
                }
                for (JSONObject jsonObject : _jsonArr) {
                    if (!jsonArr.stream().filter(e -> (e.getString("issueId")).equals(jsonObject.getString("issueId")))
                            .findFirst().isPresent()) {
                        jsonArr.add(jsonObject);
                    }
                }

                if (!CollectionUtils.isEmpty(param.getProjectIds())) {
                    jsonArr = jsonArr.stream().filter(obj -> param.getProjectIds().indexOf(obj.getLong("issueId")) > -1)
                            .collect(Collectors.toList());
                    return jsonArr;
                } else if (Objects.nonNull(param.getProjectIds()) && param.getProjectIds().isEmpty()) {
                    return new ArrayList<>();
                }

            } else if (Objects.nonNull(param.getCateIds()) && param.getCateIds().isEmpty()) {
                return new ArrayList<>();
            }
        }

        return jsonArray;
    }

    public List<JSONObject> getProductParamsOfNotSplit(ProductManager param) {

        LambdaQueryWrapper<ProductManager> queryWrapper = Wrappers.<ProductManager>lambdaQuery()
                .isNotNull(ProductManager::getProductCateMulti);

        queryWrapper.in(ProductManager::getParentDept, Arrays.asList(22269L, 18711L, 22487L));

        if (!CollectionUtils.isEmpty(param.getProductStates())) {
            queryWrapper.in(ProductManager::getProductState, param.getProductStates());
        }

        List<ProductManager> projects = productManagerService.list(queryWrapper);
        projects = productMultiCateOfNotSplit(projects);

        if (projects.size() == 0) {
            return null;
        }

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<TechDoc> techHistories = techDocService.list(
                Wrappers.lambdaQuery(TechDoc.class)
                        .eq(TechDoc::getTechType, 4)
                        .eq(TechDoc::getTechStatus, 2)

                        .in(TechDoc::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // 转阶段对应的产品状态 A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<JSONObject> jsonArray = new ArrayList<>();

        int i = 0;

        for (ProductManager e : projects) {

            String params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            Optional<SysProductParam> first = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst();

            Long id = null;
            if (first.isPresent()) {
                id = first.get().getId();
            } else {
                SysProductParam productParam = new SysProductParam();
                productParam.setIssueId(e.getIssueId());
                productParam.setStage(stateMap.getOrDefault(e.getProductStage().intValue(), 0L));
                SysProductParam sysProductParam = sysProductParamService.getProductParam(productParam);
                id = sysProductParam.getId();
            }

            if (!params.isEmpty() && '[' == params.charAt(0)) {
                StringBuilder sb = new StringBuilder(params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                params = sb.toString();
            }

            Optional<TechDoc> opTechHistory = techHistories.stream()
                    .filter(item -> item.getIssueId().equals(e.getIssueId()))
                    .filter(item -> item.getProductState()
                            .equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .sorted(Comparator.comparing(TechDoc::getUpdateTime).reversed()) // 拿最新的
                    .findFirst();

            JSONObject obj = !params.isEmpty() ? (JSONObject) JSONObject.parse(params) : new JSONObject();
            i++;
            obj.put("no", i);
            obj.put("id", id);
            obj.put("issueId", e.getIssueId());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productOrProject", e.getProductOrProject());
            obj.put("productProjectName", e.getProductProjectName());
            obj.put("oproductProjectName",
                    e.getProductProjectName().substring(0,
                            e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                    : e.getProductProjectName().length()));
            obj.put("productVersion",
                    e.getProductProjectName()
                            .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                    ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                    : 0, e.getProductProjectName().length()));
            obj.put("productStateName", e.getProductStateName());
            obj.put("projectName", e.getProjectName());
            obj.put("productChildCate", e.getProductChildCate());
            obj.put("productClassification", e.getProductClassification());
            obj.put("productDistribute", e.getProductDistribute());
            obj.put("productParentCate", e.getProductParentCate());
            obj.put("productLevel", e.getProjectLevel());
            obj.put("mStatus", e.getMStatus());
            obj.put("parentDept", e.getParentDept());
            obj.put("customer", e.getCustomer());
            obj.put("initiationDate", e.getInitiationDate());
            obj.put("productSplitName", e.getProductSplitName());
            obj.put("productState", e.getProductState());
            obj.put("docName", opTechHistory.isPresent() ? opTechHistory.get().getTechName() : "");
            obj.put("docId", opTechHistory.isPresent() ? opTechHistory.get().getTechDocFileId() : 0);
            obj.put("cateId", e.getCateId());
            obj.put("cateIds", e.getCateIds());

            if (e.getProductSplitName().indexOf("-") != -1) {
                String[] splits = e.getProductSplitName().split("-");
                obj.put("parentCateName", splits[0]);
                obj.put("childCateName", splits[1]);
            } else {
                obj.put("parentCateName", e.getProductSplitName());
            }

            jsonArray.add(obj);
        }

        if (Objects.nonNull(param)) {
            if (!CollectionUtils.isEmpty(param.getCateIds())) {
                List<Long> cateIds = param.getCateIds().stream().filter(e -> e > 5L).collect(Collectors.toList());
                List<Long> typeIds = param.getCateIds().stream().filter(e -> e < 6L).collect(Collectors.toList());
                List<JSONObject> jsonArr = new ArrayList<>();
                if (!cateIds.isEmpty()) {
                    jsonArr = jsonArray.stream()
                            .filter(obj -> cateIds.indexOf(obj.getLong("productChildCate")) != -1
                                    || cateIds.indexOf(obj.getLong("productParentCate")) != -1)
                            .collect(Collectors.toList());
                }
                List<JSONObject> _jsonArr = new ArrayList<>();
                if (!typeIds.isEmpty()) {
                    _jsonArr = jsonArray.stream().filter(obj -> typeIds.indexOf(obj.getLong("productType")) != -1)
                            .collect(Collectors.toList());
                }
                for (JSONObject jsonObject : _jsonArr) {
                    if (!jsonArr.stream()
                            .filter(e -> (e.getString("issueId") + e.getString("productChildCate"))
                                    .equals(jsonObject.getString("issueId") + jsonObject.getString("productChildCate")))
                            .findFirst().isPresent()) {
                        jsonArr.add(jsonObject);
                    }
                }

                return jsonArr;
            } else if (Objects.nonNull(param.getCateIds()) && param.getCateIds().isEmpty()) {
                return new ArrayList<>();
            }
        }

        return jsonArray;
    }

    public void exportMarketParmas(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .ne(ProductManager::getParentDept, 18863)
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .ne(ProductManager::getParentDept, 18863));
        projects = productMultiCateOfNotSplit(projects);

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<ProductMarketParams> params = new ArrayList<>();

        for (ProductManager e : projects) {

            String _params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            if (!_params.isEmpty() && '[' == _params.charAt(0)) {
                StringBuilder sb = new StringBuilder(_params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                _params = sb.toString();
            }

            JSONObject obj = !_params.isEmpty() ? (JSONObject) JSONObject.parse(_params) : new JSONObject();

            params.add(
                    ProductMarketParams.builder()

                            .cateName(e.getProductSplitName().replaceAll(";", "\n"))
                            .oproductProjectName(e.getProductProjectName().substring(0,
                                    e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                            : e.getProductProjectName().length()))
                            .productVersion(e.getProductProjectName()
                                    .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                            : 0, e.getProductProjectName().length()))
                            .chemicalSystem((String) obj.getOrDefault("chemicalSystem", ""))
                            .density((String) obj.getOrDefault("density", ""))
                            .capacity((String) obj.getOrDefault("capacity", ""))
                            .ah((String) obj.getOrDefault("ah", ""))
                            .voltage((String) obj.getOrDefault("voltage", ""))
                            .acr((String) obj.getOrDefault("acr", ""))
                            .dcr((String) obj.getOrDefault("dcr", ""))
                            .normal((String) obj.getOrDefault("normal", ""))
                            .rapid((String) obj.getOrDefault("rapid", ""))
                            .blueFilmWidth((String) obj.getOrDefault("blueFilmWidth", ""))
                            // .widthErrand((String)obj.getOrDefault("widthErrand", ""))
                            .blueFilmThickness((String) obj.getOrDefault("blueFilmThickness", ""))
                            // .thicknessErrand((String)obj.getOrDefault("thicknessErrand", ""))
                            .nonePolarHeight((String) obj.getOrDefault("nonePolarHeight", ""))
                            // .heightErrand((String)obj.getOrDefault("heightErrand", ""))
                            .diameter((String) obj.getOrDefault("diameter", ""))
                            // .diameterErrand((String)obj.getOrDefault("diameterErrand", ""))
                            .weight((String) obj.getOrDefault("weight", ""))
                            // .weightErrand((String)obj.getOrDefault("weightErrand", ""))
                            .productType((Integer) obj.getOrDefault("productType", 0))
                            // .rollCore((Integer)obj.getOrDefault("rollCore", 0))
                            // .earbud((String)obj.getOrDefault("earbud", ""))
                            // .terminal((String)obj.getOrDefault("terminal", ""))
                            // .bareWireCount((String)obj.getOrDefault("bareWireCount", ""))
                            .build());

        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品参数_市场部", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductMarketParams.class)
                    .sheet("产品参数")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void exportFactoryParmas(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .ne(ProductManager::getParentDept, 18863)
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .ne(ProductManager::getParentDept, 18863));
        projects = productMultiCateOfNotSplit(projects);

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<ProductFactoryParams> params = new ArrayList<>();

        for (ProductManager e : projects) {

            String _params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            if (!_params.isEmpty() && '[' == _params.charAt(0)) {
                StringBuilder sb = new StringBuilder(_params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                _params = sb.toString();
            }

            JSONObject obj = !_params.isEmpty() ? (JSONObject) JSONObject.parse(_params) : new JSONObject();

            params.add(
                    ProductFactoryParams.builder()
                            // .cateName(obj.containsKey("childCateName") ?
                            // (String)obj.getString("parentCateName") + "-" +
                            // (String)obj.getString("childCateName"):(String)obj.getString("parentCateName")
                            // )
                            .cateName(e.getProductSplitName().replaceAll(";", "\n"))
                            .oproductProjectName(e.getProductProjectName().substring(0,
                                    e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                            : e.getProductProjectName().length()))
                            .productVersion(e.getProductProjectName()
                                    .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                            : 0, e.getProductProjectName().length()))
                            .chemicalSystem((String) obj.getOrDefault("chemicalSystem", ""))
                            .density((String) obj.getOrDefault("density", ""))
                            .capacity((String) obj.getOrDefault("capacity", ""))
                            .ah((String) obj.getOrDefault("ah", ""))
                            .voltage((String) obj.getOrDefault("voltage", ""))
                            // .acr((String)obj.getOrDefault("acr", ""))
                            // .dcr((String)obj.getOrDefault("dcr", ""))
                            // .normal((String)obj.getOrDefault("normal", ""))
                            // .rapid((String)obj.getOrDefault("rapid", ""))
                            .blueFilmWidth((String) obj.getOrDefault("blueFilmWidth", ""))
                            // .widthErrand((String)obj.getOrDefault("widthErrand", ""))
                            .blueFilmThickness((String) obj.getOrDefault("blueFilmThickness", ""))
                            // .thicknessErrand((String)obj.getOrDefault("thicknessErrand", ""))
                            .nonePolarHeight((String) obj.getOrDefault("nonePolarHeight", ""))
                            // .heightErrand((String)obj.getOrDefault("heightErrand", ""))
                            .diameter((String) obj.getOrDefault("diameter", ""))
                            // .diameterErrand((String)obj.getOrDefault("diameterErrand", ""))
                            .weight((String) obj.getOrDefault("weight", ""))
                            // .weightErrand((String)obj.getOrDefault("weightErrand", ""))
                            .productType((Integer) obj.getOrDefault("productType", 0))
                            .rollCore((Integer) obj.getOrDefault("rollCore", 0))
                            .earbud((String) obj.getOrDefault("earbud", ""))
                            .terminal((String) obj.getOrDefault("terminal", ""))
                            // .bareWireCount((String)obj.getOrDefault("bareWireCount", ""))
                            .build());

        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品参数_工厂部", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductFactoryParams.class)
                    .sheet("产品参数")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void exportSpecsParmas(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .ne(ProductManager::getParentDept, 18863)
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .ne(ProductManager::getParentDept, 18863));
        projects = productMultiCateOfNotSplit(projects);

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<ProductSpecParams> params = new ArrayList<>();

        for (ProductManager e : projects) {

            String _params = productParams.stream()
                    .filter(_e -> null != _e.getStage())
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            if (!_params.isEmpty() && '[' == _params.charAt(0)) {
                StringBuilder sb = new StringBuilder(_params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                _params = sb.toString();
            }

            JSONObject obj = !_params.isEmpty() ? (JSONObject) JSONObject.parse(_params) : new JSONObject();

            // if (e.getProductSplitName().indexOf("-") != -1) {
            // String[] splits = e.getProductSplitName().split("-");
            // obj.put("parentCateName",splits[0]);
            // obj.put("childCateName",splits[1]);
            // }else{
            // obj.put("parentCateName",e.getProductSplitName());
            // }
            params.add(
                    ProductSpecParams.builder()
                            // .cateName(obj.containsKey("childCateName") ?
                            // (String)obj.getString("parentCateName") + "-" +
                            // (String)obj.getString("childCateName"):(String)obj.getString("parentCateName")
                            // )
                            .cateName(e.getProductSplitName().replaceAll(";", "\n"))
                            .oproductProjectName(e.getProductProjectName().substring(0,
                                    e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v")
                                            : e.getProductProjectName().length()))
                            .productVersion(e.getProductProjectName()
                                    .substring(e.getProductProjectName().toLowerCase().indexOf("-v") != -1
                                            ? e.getProductProjectName().toLowerCase().indexOf("-v") + 1
                                            : 0, e.getProductProjectName().length()))
                            .chemicalSystem((String) obj.getOrDefault("chemicalSystem", ""))
                            .density((String) obj.getOrDefault("density", ""))
                            .capacity((String) obj.getOrDefault("capacity", ""))
                            .ah((String) obj.getOrDefault("ah", ""))
                            .voltage((String) obj.getOrDefault("voltage", ""))
                            // .acr((String)obj.getOrDefault("acr", ""))
                            // .dcr((String)obj.getOrDefault("dcr", ""))
                            // .normal((String)obj.getOrDefault("normal", ""))
                            // .rapid((String)obj.getOrDefault("rapid", ""))
                            .blueFilmWidth((String) obj.getOrDefault("blueFilmWidth", ""))
                            // .widthErrand((String)obj.getOrDefault("widthErrand", ""))
                            .blueFilmThickness((String) obj.getOrDefault("blueFilmThickness", ""))
                            // .thicknessErrand((String)obj.getOrDefault("thicknessErrand", ""))
                            .nonePolarHeight((String) obj.getOrDefault("nonePolarHeight", ""))
                            // .heightErrand((String)obj.getOrDefault("heightErrand", ""))
                            .diameter((String) obj.getOrDefault("diameter", ""))
                            // .diameterErrand((String)obj.getOrDefault("diameterErrand", ""))
                            .weight((String) obj.getOrDefault("weight", ""))
                            // .weightErrand((String)obj.getOrDefault("weightErrand", ""))
                            .productType((Integer) obj.getOrDefault("productType", 0))
                            // .rollCore((Integer)obj.getOrDefault("rollCore", 0))
                            // .earbud((String)obj.getOrDefault("earbud", ""))
                            // .terminal((String)obj.getOrDefault("terminal", ""))
                            // .bareWireCount((String)obj.getOrDefault("bareWireCount", ""))
                            .build());

        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品参数_规格书", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductSpecParams.class)
                    .sheet("产品参数")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportParmas(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L))
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L)));
        projects = productMultiCateOfNotSplit(projects);

        projects = projects.stream().filter(e -> e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductManager::getProductProjectName)).collect(Collectors.toList());

        List<SysProductParam> productParams = productParamService.list(
                Wrappers.<SysProductParam>lambdaQuery()
                        .in(SysProductParam::getIssueId,
                                projects.stream().map(ProductManager::getIssueId).collect(Collectors.toList())));

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {
            {
                put(1, 2L);
                put(2, 2L);
                put(3, 2L);
                put(4, 2L);
                put(5, 2L);
                put(6, 3L);
                put(7, 3L);
                put(8, 3L);
                put(9, 4L);
                put(10, 4L);
                put(11, 4L);
                put(12, 4L);
            }
        };

        List<ProductParams> params = new ArrayList<>();

        for (ProductManager e : projects) {

            String _params = productParams.stream()
                    .filter(_e -> e.getIssueId().equals(_e.getIssueId()))
                    .filter(_e -> null != _e.getStage()
                            && _e.getStage().equals(stateMap.getOrDefault(e.getProductStage().intValue(), 0L)))
                    .findFirst()
                    .map(SysProductParam::getParams)
                    .orElse("");

            if (!_params.isEmpty() && '[' == _params.charAt(0)) {
                StringBuilder sb = new StringBuilder(_params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                _params = sb.toString();
            }

            JSONObject obj = !_params.isEmpty() ? (JSONObject) JSONObject.parse(_params) : new JSONObject();

            params.add(
                    ProductParams.builder()
                            .productProjectName(e.getProductProjectName())
                            .chemicalSystem((String) obj.getOrDefault("chemicalSystem", ""))
                            .density((String) obj.getOrDefault("density", ""))
                            .capacity((String) obj.getOrDefault("capacity", ""))
                            .ah((String) obj.getOrDefault("ah", ""))
                            .voltage((String) obj.getOrDefault("voltage", ""))
                            .acr((String) obj.getOrDefault("acr", ""))
                            .dcr((String) obj.getOrDefault("dcr", ""))
                            .normal((String) obj.getOrDefault("normal", ""))
                            .rapid((String) obj.getOrDefault("rapid", ""))
                            .blueFilmWidth((String) obj.getOrDefault("blueFilmWidth", ""))
                            .widthErrand((String) obj.getOrDefault("widthErrand", ""))
                            .blueFilmThickness((String) obj.getOrDefault("blueFilmThickness", ""))
                            .thicknessErrand((String) obj.getOrDefault("thicknessErrand", ""))
                            .nonePolarHeight((String) obj.getOrDefault("nonePolarHeight", ""))
                            .heightErrand((String) obj.getOrDefault("heightErrand", ""))
                            .diameter((String) obj.getOrDefault("diameter", ""))
                            .diameterErrand((String) obj.getOrDefault("diameterErrand", ""))
                            .weight((String) obj.getOrDefault("weight", ""))
                            .weightErrand((String) obj.getOrDefault("weightErrand", ""))
                            .productType((Integer) obj.getOrDefault("productType", 0))
                            .rollCore((Integer) obj.getOrDefault("rollCore", 0))
                            .earbud((String) obj.getOrDefault("earbud", ""))
                            .terminal((String) obj.getOrDefault("terminal", ""))
                            .bareWireCount((String) obj.getOrDefault("bareWireCount", ""))
                            .build());

        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品参数", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductParams.class)
                    .sheet("产品参数")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void exportBaseInfo(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L))
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L)));
        projects = productMultiCateOfNotSplit(projects);

        List<ProductInfo> params = new ArrayList<>();

        List<SysWerkLine> werkLines = werkLineService.list();

        for (ProductManager e : projects) {
            params.add(ProductInfo.builder()
                    .productSplitName(e.getProductSplitName())
                    .productProjectName(e.getProductProjectName())
                    .projectName(e.getProjectName())
                    .projectLevelName(e.getProjectLevelName())
                    .productManagerName(e.getProductManagerName())
                    .productRPMName(e.getProductRPMName())
                    .largeProjectManagerName(e.getLargeProjectManagerName())
                    .customer(e.getCustomer())
                    .customerProject(e.getCustomerProject())
                    .projectFixedDate(e.getProjectFixedDate())
                    .actualProjectFixedDate(e.getActualFixedDate())
                    .initiationDate(e.getInitiationDate())
                    .productPlannedM5(e.getProductPlannedM5())
                    .productStateName(e.getProductStateName())
                    .parentDeptName(e.getParentDeptName())
                    .lines(
                            werkLines.stream().filter(item -> e.getLines().indexOf(item.getId()) != -1)
                                    .map(SysWerkLine::getLineName)
                                    .flatMap(val -> {
                                        Stream<String> stream = Arrays.asList(val).stream().map(s -> s.split("-")[0]);
                                        return stream;
                                    })
                                    .distinct()
                                    .collect(Collectors.joining(",")))
                    .build());
        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品信息", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProductInfo.class)
                    .sheet("产品信息")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void exportStagePlan(HttpServletResponse response, ProductManager param) {

        List<ProductManager> projects = productManagerService.list(
                Objects.nonNull(param.getProjectIds()) && !param.getProjectIds().isEmpty()
                        ? Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getIssueId, param.getProjectIds())
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L))
                        : Wrappers.<ProductManager>lambdaQuery()
                        .isNotNull(ProductManager::getProductCateMulti)
                        .in(ProductManager::getParentDept,
                                Arrays.asList(22269L, 18711L, 22487L)));
        projects = productMultiCateOfNotSplit(projects);

        List<ProdutPlan> params = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        DateTimeFormatter strFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (ProductManager e : projects) {

            e.setDelayDays(0L);

            e.getStages().stream().filter(s -> s.getParentId().equals(e.getIssueId()))
                    .filter(s -> s.getStage().equals(e.getProductStage())).findFirst().ifPresent(val -> {

                        LocalDate actualDate = Strings.isNullOrEmpty(val.getActualCompletionDate())
                                || val.getActualCompletionDate().equals("-")
                                ? LocalDate.now()
                                : LocalDate.parse(val.getActualCompletionDate(), strFormat);

                        LocalDate planDate = Strings.isNullOrEmpty(val.getPlanReviewDate())
                                || val.getPlanReviewDate().equals("-")
                                ? LocalDate.now()
                                : LocalDate.parse(val.getPlanReviewDate(), strFormat);

                        e.setDelayDays(planDate.until(actualDate, ChronoUnit.DAYS));

                    });

            params.add(
                    ProdutPlan
                            .builder()
                            .productProjectName(e.getProductProjectName())
                            .projectName(e.getProjectName())
                            .projectLevelName(e.getProjectLevelName())
                            .productManagerName(e.getProductManagerName())
                            .productRPMName(e.getProductRPMName())
                            .productDQEName(e.getProductDQEName())
                            .parentDeptName(e.getParentDeptName())
                            .productClassificationName(e.getProductClassificationName())
                            .productStateName(e.getProductStateName())
                            .productStageName(e.getProductStageName())
                            .isDelay(e.getProductState() < 7 && e.getDelayDays() > 6L ? "是" : "否")
                            .delayDays(e.getDelayDays() + "")

                            .planK0ReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(1L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualK0CompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(1L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .k0IsEnd(e.getStages().stream().filter(s -> s.getStage().equals(1L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(1L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planM1ReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(2L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualM1CompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(2L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .m1IsEnd(e.getStages().stream().filter(s -> s.getStage().equals(2L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(2L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planTRAReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(3L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualTRACompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(3L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .traIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(3L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(3L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planDRReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(4L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualDRCompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(4L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .drIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(4L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(4L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planM2ReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(5L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualM2CompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(5L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .m2IsEnd(e.getStages().stream().filter(s -> s.getStage().equals(5L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(5L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planTRBReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(6L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualTRBCompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(6L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .trbIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(6L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(6L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planDFReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(7L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualDFCompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(7L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .dfIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(7L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(7L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planM3ReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(8L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualM3CompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(8L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .m3IsEnd(e.getStages().stream().filter(s -> s.getStage().equals(8L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(8L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planM4ReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(9L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualM4CompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(9L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .m4IsEnd(e.getStages().stream().filter(s -> s.getStage().equals(9L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(9L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planPPAPReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(10L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualPPAPCompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(10L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .ppapIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(10L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(10L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .planSOPReviewDate(e.getStages().stream().filter(s -> s.getStage().equals(11L)).findFirst()
                                    .map(ProductStageItem::getPlanReviewDate).orElse(""))
                            .actualSOPCompletionDate(e.getStages().stream().filter(s -> s.getStage().equals(11L))
                                    .findFirst().map(ProductStageItem::getActualCompletionDate).orElse(""))
                            .sopIsEnd(e.getStages().stream().filter(s -> s.getStage().equals(11L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).isPresent()
                                    ? (e.getStages().stream().filter(s -> s.getStage().equals(11L)).findFirst()
                                    .map(ProductStageItem::getIsEnd).get() == 1 ? "是" : "否")
                                    : "否")

                            .stopTime(null != e.getStopTime() ? sdf.format(e.getStopTime()) : "")

                            .build());
        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
                    .encode("产品里程碑", "UTF-8")
                    .replaceAll("\\+", "%20");
            response
                    .setHeader(
                            "Content-disposition",
                            "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
                    .write(response.getOutputStream(), ProdutPlan.class)
                    .sheet("产品里程碑")
                    .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
