package eve.sys.modular.product.param.request;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

@Data
public class CateParams implements Serializable{
    private String cateName;
    private Long parentId;
    private Long cateId;
    private String projectId;
    private Long flag;
    private List<Long> productDistribute;
    private List<String> productLevel;

    private String startDate;
    private String endDate;
    private List<Long> cates;
    private List<Long> statuses;
    private List<Long> productClassification;
    private List<Long> depts;
    private String keyword;
    private String dept;

    private Long isVcylinder;
    private Long isJMArea;
    private Long wealthState;
}
