<template>
	<div class="container" style="position: fixed;left: 0;right: 0;bottom:0;top:40px;">
		<div :style="`width:${elWidth}px;height:${elHeight}px;position: fixed;left: 0;right: 0;margin:0 auto;`">
			<!-- breadcrumb start -->
			<div :style="`width:${_elWidth}px;zoom:${meter_zoom};`">
				<a-breadcrumb class="breadcrumb" separator=">">
					<a-breadcrumb-item
						><a @click="gohome"><a-icon class="rollback-icon" type="rollback" />产品看板</a></a-breadcrumb-item
					>
					<a-breadcrumb-item>产品矩阵图</a-breadcrumb-item>
				</a-breadcrumb>
			</div>
			<!-- breadcrumb end -->

			<a-spin :spinning="false">
				<div class="content" :style="`height: ${contentHeight}px;`">
					<!-- echarts start -->
					<div class="chart" :style="`zoom:${meter_zoom};position:sticky;top:0;z-index: 999;`">

            <!-- 立项讨论 -->
						<div class="chart_table" ref="otherProduct"></div>

						<!-- 预研产品 -->
						<!-- <div class="chart_table" ref="prediction"></div> -->
						<!-- A|B新产品 -->
						<div class="chart_table" ref="abProduct"></div>
						<!-- 试产新产品 -->
						<div class="chart_table" ref="trialProduction"></div>
						<!-- 量产品 -->
						<div class="chart_table" ref="outputProduct"></div>
						<!-- 停止 -->
						<div class="chart_table" ref="stopProduct"></div>
					</div>
					<!-- echarts end -->

					<!-- 筛选框 start -->
					<div class="table-page-search-wrapper" :style="`zoom:${meter_zoom};margin:auto;margin:auto;position:sticky;top:70px;z-index: 999;background: #fff;`">
						<a-form layout="inline">
							<a-row :gutter="48">
								<a-col :md="5" :sm="24">
									<a-form-item label="立项日期">
										<a-range-picker
											class="filter-form"
											:placeholder="['开始日期', '结束日期']"
											size="small"
											@change="dateChange"
										/>
									</a-form-item>
								</a-col>
								<a-col :md="5" :sm="24">
									<a-form-item label="产品类别">
										<treeselect
											:limit="1"
											class="filter-form"
											@input="changeQuery"
											:max-height="200"
											placeholder="请选择产品类别"
											value-consists-of="BRANCH_PRIORITY"
											v-model="queryparam.productClassifications"
											:options="typeOptions"
											:multiple="true"
										/>
									</a-form-item>
								</a-col>
								<a-col :md="5" :sm="24">
									<a-form-item label="产品部门">
										<treeselect
											class="filter-form"
											:limit="1"
											@input="changeQuery"
											:max-height="200"
											placeholder="请选择所属部门"
											:multiple="true"
											:options="deptsOptions"
											value-consists-of="BRANCH_PRIORITY"
											v-model="queryparam.parentDepts"
										>
										</treeselect>
									</a-form-item>
								</a-col>
                                <a-col :md="5" :sm="24">
									<a-form-item label="">
										<a-input
											size="small"
											class="filter-form"
											@keyup.enter.native="changeQuery"
											v-model="queryparam.keyword"
											placeholder="请输入产品名称"
										>
											<a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
										</a-input>
									</a-form-item>
								</a-col>

								<a-col :md="2" :sm="24" :style="{ float: 'right' }">
									<div class="table-page-search-submitButtons" :style="{ float: 'right' }">
										<a-button size="small" style="margin-left: 120px;" type="primary" @click="getProductAlignTable"
											>查询</a-button
										>
										<a-button size="small" style="margin-left: 10px;margin-top:6px" @click="resetquery">重置</a-button>
										<a-button size="small" style="margin-left: 10px;margin-top:6px"  type="primary" @click="next">跳转下一页</a-button>
									</div>
								</a-col>
							</a-row>
						</a-form>
					</div>
					<!-- 筛选框 end -->

					<!-- table start -->
					<div class="table-wrapper" :style="`height: ${tableHeight}px;`">
						<ve-table
							v-if="showDetail"
							class="vetable"
							border-y
							rowKeyFieldName="rowKey"
							fixed-header
							:max-height="windowHeight"
							:border="false"
							:columns="tablecolumns"
							:table-data="tabledatas"
							:cell-style-option="cellStyleOption"
							:cell-span-option="cellSpanOption"
							id="loading-container"
							:style="`zoom:${meter_zoom};`"
						/>
					</div>
					<!-- table end -->
				</div>
			</a-spin>
		</div>
	</div>
</template>

<script>
import { getProductAlignTable } from "@/api/modular/system/jmChartManage"
import { mapActions, mapGetters } from "vuex"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";


export default {
	components: {
		Treeselect
	},
	data() {
		return {
			cate: [],
			deptsOptions: [],
			typeOptions: [
                {
					id: 7,
					label: "立项讨论"
				},
				/* {
					id: 1,
					label: "预研产品"
				}, */
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 6,
					label: "停止"
				} 
			],
			statuses: [
				{
					id: 0,
					label: "立项讨论"
				},
				{
					id: 1,
					label: "A/B样"
				},
				{
					id: 2,
					label: "C/D样"
				},
				{
					id: 3,
					label: "暂停开发"
				},
				{
					id: 4,
					label: "停产"
				},
				{
					id: 5,
					label: "SOP"
				}
			],
			queryparam: {
				statuses: [],
				parentDepts:[]
			},
			projectStatus: {},
			statuxTxt: ["", "fail", "warning", "success", "info", "blues", "sop","blues"],
			lampStatus: {
				1:'yellow',
				2:'red'
			},
			showDetail: false,
			elWidth: 0,
			_elWidth: 0,
			elHeight: 0,
			zoom: 1,
			meter_zoom: 0,
			windowHeight: document.documentElement.clientHeight - 185,
			loadingInstance: null,
			rowcount: 0,
			columncount: 0,
			tablecolumns: [],
			// 20 :页面padding 18 面包屑 20 20 :margin /padding  110:echarts 40 select
			contentHeight: document.documentElement.clientHeight - 20 - 18 - 20 - 20 - 10,
			tableHeight: document.documentElement.clientHeight - 20 - 18 - 20 - 20 - 10 - 110 - 40 - 20,
			columfield: "",
			tabledatas: [],
			merges: ["class"],
			cellStyleOption: {
				bodyCellClass: ({ row, column, rowIndex }) => {
				}
			},
			cellSpanOption: {
				bodyCellSpan: this.bodyCellSpan
				/* footerCellSpan: this.footerCellSpan, */
			}
		}
	},
	computed: {
		...mapGetters(["userInfo"])
	},
	created() {
		this.loadingInstance = this.$veLoading({
			target: document.querySelector("#loading-container"),
			name: "flow"
		})
		this.getJiraOptionList()
		this.getProductAlignTable()
		this.initBodySize()

		window.addEventListener("mousewheel", this.handleScroll, { passive: false })
	},
	methods: {
		next(){
			this.$router.push({
				path: "/jm_product_dashboard"
			})
		},
    getJiraOptionList() {
      getJiraOptionList({fieldName: 'jmDepartment'}).then(res => {
        if (res.success) {
          let depts = []
          let _depts = ['22269', '18711', '22487']//储能、铁锂、锰铁锂
          let parentNodes = res.data.filter(o => o.parentoptionid == null)
          for (const item of parentNodes) {
            if (_depts.indexOf(item.id) == -1) {
              continue
            }
            let _item = {
              id: (item.id),
              label: item.customvalue
            }
            depts.push(_item)
          }
          //this.deptsOptions = depts
          this.deptsOptions = [{
            id: '22269',
            label: '储能所',
          }, {
            id: '18711',
            label: '铁锂所',
          }, {
            id: '22487',
            label: '锰铁锂所',
          }];
        }
      })
    },
		gohome() {
			this.$router.push({
				path: "/jm_product_chart"
			})
		},

		// 获取数据
		getProductAlignTable() {
			this.show() //展示loading
			getProductAlignTable(this.queryparam)
				.then(res => {
					if (res.success) {
						if (res.data.tablecolumns)
							for (var item of res.data.tablecolumns) {
								if (item.children) {
									for (var _item of item.children) {
										_item.renderHeaderCell = this.renderHeaderCell
										_item.renderBodyCell = this.renderBodyCell
									}
									this.columfield = item.children[0].field
									item.renderHeaderCell = this.renderHeaderCell
								}  else {
									item.renderHeaderCell = this.renderHeaderCell
									item.renderBodyCell = this.renderBodyCell
								}
							}
						
						this.tablecolumns = res.data.tablecolumns ? res.data.tablecolumns : []
						this.tabledatas = res.data.tabledatas ? res.data.tabledatas : []
						this.rowcount = res.data.rows
						this.columncount = res.data.columns
						this.projectStatus = res.data.chartdatas ? res.data.chartdatas : {}
						this.initChart()
						this.showDetail = true
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.close() //关闭loading
				})
				.catch(err => {
					this.close()
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
		renderHeaderCellTop({ column }) {
			return <span style="font-size:20px;display:block;padding:10px 0;">{column.title}</span>
		},
		renderHeaderCell({ column }) {
			return (
				<span
					class="clickheadstyle"
					onClick={() => {
						this.handleTo(column)
					}}
				>
					{column.title}
				</span>
			)
		},
		renderBodyCell({ row, column, rowIndex }) {
			if (rowIndex < this.rowcount) {
				let statusCls = this.statuxTxt[row[column.field + "_productClassification"]]
				let lampCls = this.lampStatus[row[column.field + "_wellState"]]

				if (!row[column.field + "_isAllow"]) {
					return (
						<div class={lampCls? lampCls+' statusrow' : 'statusrow'}>
							<span class={statusCls}></span>
							<span>{row[column.key]}</span>
						</div>
					)
				}
				return (
					<div
						class={lampCls? lampCls+' statusrow clickheadstyle' : 'statusrow clickheadstyle'}
						onClick={() => {
							this.goto(row, column)
						}}
					>
						<span class={statusCls}></span>
						<span>{row[column.key]}</span>
					</div>
				)
			}
			return <span>{row[column.key]}</span>
		},
		
		bodyCellSpan({ row, column, rowIndex }) {
			if (this.merges.includes(column.field)) {
				const _col = row.rowSpan > 0 ? 1 : 0
				return {
					colspan: _col,
					rowspan: row.rowSpan
				}
			}
			if (rowIndex >= this.rowcount) {
				if (column.field == this.columfield) {
					return {
						colspan: this.columncount,
						rowspan: 1
					}
				} else {
					return {
						colspan: 0,
						rowspan: 0
					}
				}
			}
		},
		handleTo(column) {
			let $query = {}
			if (column.id == 3 || column.id == 2) {
				$query.cateId = ["18892","22438", "22439",column.id].join(',')
			}else{
				$query.cateId = column.id
			}
			
			this.$router.push({
				path: "/jm_product_dashboard",
				query: $query
			})
		},
		goto(row, column) {
			let $query = {}
			if (row[column.key + "_pid"] == row[column.key + "_key"]) {
				if (row[column.key + "_pid"] == 3 || row[column.key + "_pid"] == 2) {
					$query.cateId = ["18892","22438", "22439",row[column.key + "_pid"]].join(',')
				}else{
					$query.cateId = row[column.key + "_pid"]
				}
			} else {
				$query.cateId = row[column.key + "_key"]
			}
			$query.keyword = row[column.key]
		
			this.$router.push({
				path: "/jm_product_dashboard",
				query: $query
			})
		},
		
		initBodySize() {
			this.initWidth = document.documentElement.clientWidth // 拿到父元素宽
			this.initHeight = (document.documentElement.clientHeight * document.documentElement.clientWidth) / this.initWidth // 根据宽计算高实现自适应
			this.elWidth = this.initWidth
			this._elWidth = this.elWidth * 0.94
			this.elHeight = this.initHeight
			this.meter_zoom = 1
		},
		handleScroll(e) {
			if (e.ctrlKey) {
				// 取消浏览器默认的放大缩小网页行为
				e.preventDefault()
				// 判断是向上滚动还是向下滚动
				if (e.deltaY > 0) {
					// 放大重写，业务代码
					this.handwheel(e)
				} else {
					// 缩小重写，业务代码
					this.handwheel(e)
				}
			}
		},
		handwheel(e) {
			if (e.wheelDelta < 0) {
				this.zoom -= 0.05
			} else {
				this.zoom += 0.05
			}
			if (this.zoom >= 1.05) {
				this.windowHeight = document.documentElement.clientHeight - 170
				this.zoom = 1
				return
			}
			if (this.zoom <= 0.75) {
				this.zoom = 0.75
				return
			}

			this.elWidth = this.initWidth * this.zoom
			this._elWidth = this.elWidth * 0.97
			this.elHeight = this.initHeight * this.zoom
			this.windowHeight = document.documentElement.clientHeight - 170
			this.meter_zoom = this.zoom
			this.initChart()
		},
		show() {
			this.loadingInstance.show()
		},
		close() {
			this.loadingInstance.close()
		},

		...mapActions(["MenuChange"]),

		initChart() {
			this.$nextTick(() => {
				//this.initPrediction()
				this.initAbProduct()
				this.initTrialProduction()
				this.initOutputProduct()
				this.initOtherProduct()
				this.initStopProduct()
			})
		},

		// 数据筛选
		changeQuery(value, label, extra) {
			this.getProductAlignTable()
		},

		// 数据筛选重置
		resetquery() {
			this.queryparam = {
				statuses: [],
				keyword: null
			}
			this.getProductAlignTable()
		},

		// 日期修改
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = dateString[0]
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = dateString[1]
			} else {
				this.queryparam.endDate = null
			}

			this.getProductAlignTable()
		},

		//  预研产品
		initPrediction() {
			let chart = this.echarts.init(this.$refs.prediction)
			chart.off("click")
			let status = this.projectStatus
			let datas = [
				{
					name: "预研产品",
					value: parseInt(status["0"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["0"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item",
					appendToBody: true
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#4aabc6", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#4aabc6",
							formatter: `预研产品\n${status["0"]}/${sum}`,
							fontSize: "11",
							lineHeight: 20
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassifications = []
				that.queryparam.productClassifications.push(1)
				that.getProductAlignTable()
			})
			chart.resize()
		},
    // 立项讨论
    initOtherProduct() {
      let chart = this.echarts.init(this.$refs.otherProduct)
      chart.off("click")

      let status = this.projectStatus

      let datas = [
        {
          name: "立项讨论",
          value: parseInt(status["7"])
        },
        {
          name: "其余",
          value: parseInt(status["z"]) - parseInt(status["7"])
        }
      ]

      let sum = parseInt(status["z"])

      chart.clear()
      const options = {
        tooltip: {
          trigger: "item",
          appendToBody: true
        },
        legend: {
          show: false,
          itemWidth: 8,
          itemHeight: 8,
          y: "bottom",
          x: "center",
          textStyle: {
            fontSize: 10
          }
        },
        color: ["#93cddd", "#eceaea"],
        grid: {},
        series: [
          {
            type: "pie",
            minAngle: 10,
            radius: ["95%", "120%"],
            center: ["50%", "59%"],
            itemStyle: {
              borderRadius: 4,
              borderColor: "#fff",
              borderWidth: 1
            },
            label: {
              show: true,
              position: "center",
              color: "#93cddd",
              formatter: `立项讨论\n${status["7"]}/${sum}`,
              fontSize: "11",
              lineHeight: 20
            },
            labelLine: {
              show: false,
              length: 0.0001
            },
            data: datas
          },
          {
            type: "pie",
            minAngle: 10,
            radius: ["95%", "120%"],
            center: ["50%", "59%"],
            itemStyle: {
              borderRadius: 4,
              borderColor: "#fff",
              borderWidth: 1
            },
            label: {
              show: false,
              formatter: function(data) {
                return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
              }
            },
            tooltip: {
              formatter: function(data) {
                return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
                  data.color
                };"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
              }
            },
            labelLine: {
              show: false,
              length: 0.0001
            },
            data: datas
          }
        ]
      }
      chart.setOption(options)
      let that = this
      chart.on("click", function(params) {
        that.queryparam.productClassifications = []
        that.queryparam.productClassifications.push(7)
        that.getProductAlignTable()
      })
      chart.resize()
    },
		// A|B新产品
		initAbProduct() {
			let chart = this.echarts.init(this.$refs.abProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "A|B新产品",
					value: parseInt(status["2"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["2"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item",
					appendToBody: true
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#a0cdf5", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#a0cdf5",
							formatter: `A|B新产品\n${status["2"]}/${sum}`,
							fontSize: "11",
							lineHeight: 20
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassifications = []
				that.queryparam.productClassifications.push(2)
				that.getProductAlignTable()
			})
			chart.resize()
		},
		// 试产新产品
		initTrialProduction() {
			let chart = this.echarts.init(this.$refs.trialProduction)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "试产新产品",
					value: parseInt(status["3"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["3"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item",
					appendToBody: true
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#4b97e3", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#4b97e3",
							formatter: `试产新产品\n${status["3"]}/${sum}`,
							fontSize: "11",
							lineHeight: 20
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassifications = []
				that.queryparam.productClassifications.push(3)
				that.getProductAlignTable()
			})
			chart.resize()
		},
		// 量产品
		initOutputProduct() {
			let chart = this.echarts.init(this.$refs.outputProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "量产品",
					value: parseInt(status["4"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["4"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item",
					appendToBody: true
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#4371c4", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#4371c4",
							formatter: `量产品\n${status["4"]}/${sum}`,
							fontSize: "11",
							lineHeight: 20
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassifications = []
				that.queryparam.productClassifications.push(4)
				that.getProductAlignTable()
			})
			chart.resize()
		},
		// 停止
		initStopProduct() {
			let chart = this.echarts.init(this.$refs.stopProduct)
			chart.off("click")

			let status = this.projectStatus

			let datas = [
				{
					name: "停止",
					value: parseInt(status["6"])
				},
				{
					name: "其余",
					value: parseInt(status["z"]) - parseInt(status["6"])
				}
			]

			let sum = parseInt(status["z"])

			chart.clear()
			const options = {
				tooltip: {
					trigger: "item",
					appendToBody: true
				},
				legend: {
					show: false,
					itemWidth: 8,
					itemHeight: 8,
					y: "bottom",
					x: "center",
					textStyle: {
						fontSize: 10
					}
				},
				color: ["#a5a5a5", "#eceaea"],
				grid: {},
				series: [
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: true,
							position: "center",
							color: "#a5a5a5",
							formatter: `停止\n${status["6"]}/${sum}`,
							fontSize: "11",
							lineHeight: 20
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					},
					{
						type: "pie",
						minAngle: 10,
						radius: ["95%", "120%"],
						center: ["50%", "59%"],
						itemStyle: {
							borderRadius: 4,
							borderColor: "#fff",
							borderWidth: 1
						},
						label: {
							show: false,
							formatter: function(data) {
								return `${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						tooltip: {
							formatter: function(data) {
								return `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${
									data.color
								};"></span>
                            ${data.name}\n(${data.percent.toFixed(1)}%,${data.value})`
							}
						},
						labelLine: {
							show: false,
							length: 0.0001
						},
						data: datas
					}
				]
			}
			chart.setOption(options)
			let that = this
			chart.on("click", function(params) {
				that.queryparam.productClassifications = []
				that.queryparam.productClassifications.push(6)
				that.getProductAlignTable()
			})
			chart.resize()
		}
	},
	beforeDestroy() {
		window.removeEventListener("mousewheel", this.handleScroll)
	},
	destroyed() {
		this.loadingInstance.destroy()
	}
}
</script>
<style scoped="">
html,
body,
.ant-layout {
	background: #fff;
}
</style>
<style lang="less">
@import "./vetable.less";

.yellow{
	background: #fff2cc !important;
}

.red{
	background: #f8cbad !important;

}

.fail {
	background: #4aabc6 !important;
	/* color: #000; */
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.success {
	background: #4b97e3 !important;
	/* color: #000; */
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.warning {
	background: #a0cdf5 !important;
	/* color: #000; */
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}

.info {
	background: #4371c4 !important;
	display: inline-block;
	/* color: #909399; */
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
.sop {
	background: #a5a5a5 !important;
	/* color: #909399; */
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
.blues {
	background: #93cddd !important;
	/* color: #000; */
	display: inline-block;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	margin-right: 3px;
	margin-left: 3px;
}
/* .statusrow {
	text-align: left;
	// display: flex;
	// align-items: center;
	// justify-content: start;
} */
.statusrow {
		white-space: nowrap;
		display: flex;
		/* justify-content: center; */
		align-items: center;
		height: 100%;
	}
.chart {
	display: flex;
	height: 70px;
	border: 1px solid #e8e8e8;
}
.chart .chart_table {
	flex: 1;
	height: 100%;
	margin: auto 2px;
	padding: 0 0 5px;
}
/* .vetable{
  zoom: 1;
}
@media screen and (max-width: 1300px){
    .vetable{
        zoom: 75%;
    }
} */
</style>
<style lang="less" scoped="">
/deep/.ant-form label{
	font-size: 12px;
}
/deep/.ant-input{
	font-size: 12px;
}
/deep/.ant-form-item{
	font-size: 12px;
}
/deep/.vue-treeselect__multi-value-item {
	background: transparent;
	font-size: 12px;
	vertical-align: initial;
}
/deep/.vue-treeselect {
	/* display: inline-block; */
	min-width: 80%;
	max-width: 95%;
	margin-top: 4px;
}
/deep/.vue-treeselect__control {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 26px !important;
	overflow: hidden;
	border-radius: initial;
}
/deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
}
/deep/.vue-treeselect__limit-tip-text {
	margin-top: 0px !important;
}
/deep/.vue-treeselect__value-remove {
	color: #e9e9e9;
}
/deep/.vue-treeselect__multi-value-item {
	color: #695959;
}
/deep/ .ant-col {
	padding: 2px !important;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin: 0;
}
/deep/.ant-row {
	margin: 0 !important;
}
/deep/.table-page-search-wrapper .table-page-search-submitButtons {
	margin: 0;
}

// 筛选框高度
/deep/.ant-input-affix-wrapper .ant-input {
	height: 26px !important;
}

// 为了调整筛选框的统一性
/deep/.vue-treeselect {
	margin-top: 3px;
}

/deep/.vue-treeselect__multi-value-item-container {
	vertical-align: text-top;
}

// 特殊处理 产品类别居中
/deep/.filter-box .vue-treeselect__multi-value-item {
	margin: 8px 0 0;
}

/deep/.vue-treeselect__limit-tip-text {
	margin: 15px 4px 0;
}

// 表头颜色
/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	thead.ve-table-header
	tr.ve-table-header-tr
	th.ve-table-header-th {
	color: #333;
	background-color: #f3f3f3 !important;
}

.select-box {
	margin-top: 2px;
}
/deep/.filter-form{
	height: 26px !important;
}
/deep/.ant-calendar-picker-input.ant-input{
	height: 26px !important;
}
/deep/.ant-btn-sm{
	font-size: 12px !important;
}
// 全局
.container {
	background-color: #f0f2f5;
}

.content {
	background-color: #fff;
	margin: 2px;
	padding: 2px;
	border-radius: 10px;
	box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
	//overflow: auto;
}

// 面包屑
.breadcrumb {
	padding: 0 10px;
}
.ant-breadcrumb a {
	color: #5d90fa !important;
}
.ant-breadcrumb a:first-child {
	color: rgba(0, 0, 0, 0.65) !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.65) !important;
}
/deep/.ant-breadcrumb .anticon.anticon-home {
	font-size: 19px;
}

// 表格

/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	tbody.ve-table-body
	tr.ve-table-body-tr
	td.ve-table-body-td {
	border: none;
	border-bottom: 1px solid #e8e8e8;
	// height: 40px;
}

/deep/.ve-table.ve-table-border-around {
	border: none;
	border-top: 1px solid #e8e8e8;
}

// /deep/.ve-table-header-th:hover {
// 	background-color: #5d90fa !important;
// }

/deep/tr td:hover {
	background-color: #dbe2e43c !important;
	color: #333 !important;
}

/deep/.ve-table
	.ve-table-container
	.ve-table-content-wrapper
	table.ve-table-content
	thead.ve-table-header
	tr.ve-table-header-tr
	th.ve-table-header-th:hover {
	background-color: #dbe2e4 !important;
	color: #333 !important;
}



/deep/.clickheadstyle {
	color: #333 !important;
}
/deep/.ve-table .ve-table-container .ve-table-content-wrapper table.ve-table-content tbody.ve-table-body.ve-table-row-hover tr.ve-table-body-tr:hover td {
    background-color: #fff;
}
</style>
