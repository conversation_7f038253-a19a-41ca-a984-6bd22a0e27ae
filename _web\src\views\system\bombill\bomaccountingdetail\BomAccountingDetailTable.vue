<template>
  <div>
    <a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item><a @click="$router.push('/bombill/cost')">返回</a></a-breadcrumb-item>
        <a-breadcrumb-item>BOM成本核算明细</a-breadcrumb-item>
			</a-breadcrumb>
    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='2'
      :pageTitleShow=false 
      :loading='loading'
      :paginationShow= false
      @tableFocus="tableFocus"
      @tableBlur="tableBlur" 
    >
      <template #search>
        <pbiSearchContainer>

           <pbiSearchItem label='客户类型' :span="6">
            <a-input 
              size='small' 
              readOnly
              :value="getDictName('bom_bill_customer_type',bomCostOverview.customerType)"
            />
          </pbiSearchItem>

          <pbiSearchItem label='产品名称' :span="6">
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.productName"
            />
          </pbiSearchItem>

          <pbiSearchItem label='BOM文件编号' :span="6">
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.bomFileNumber"
            />
          </pbiSearchItem>

          <pbiSearchItem label='核算代码' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.accountingCode"
            />
          </pbiSearchItem>

          <pbiSearchItem label='正极体系' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.positiveElectrodeSystem"
            />
          </pbiSearchItem>

          <pbiSearchItem label='正极核算' :span="6" v-if='isShowAllSearch'>
            <a-select
              size='small'
              @change='callFilter'
              v-model='bomCostOverview.accountingType'
              placeholder='请选择正极核算'
              :disabled="!isEditEnabled"
            >
              <a-select-option v-for="(item,i) in getDict('bom_bill_account_type')" :value="parseInt(item.code)" :key="i">
                  {{ item.name }}
              </a-select-option>
            </a-select>
          </pbiSearchItem>

          <pbiSearchItem label='额定容量(Ah)' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.ratedCapacity"
            />
          </pbiSearchItem>

          <pbiSearchItem label='额定能量(Wh)' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              readOnly
              :value="bomCostOverview.ratedEnergy"
            />
          </pbiSearchItem>


          
          <pbiSearchItem type='btn' :span="isShowAllSearch ? 24 : 6">
            <div class="main-btn">
              <a-button type="primary" @click="toggleEditMode" size="small" class="mr10" v-if="bomCostOverview.auditStatus == 1 && !isEditEnabled">编辑</a-button>
            </div>

            <div class="main-btn" v-if="isShowAllSearch">
              <a-tooltip title="更新核算将同步最新的材料价格&正极材料加工费，同时会覆盖之前的修改记录">
                <a-popconfirm placement="rightTop" :title="`更新核算将同步最新的材料价格&正极材料加工费，\n 同时会覆盖之前的修改记录，确认是否继续？`" @confirm="() => syncBomAccountingDetail()">
                    <a-button class="mr10" type="default" size='small' :loading="syncLoading" v-if="isEditEnabled">
                      更新核算
                    </a-button>
                </a-popconfirm>
              </a-tooltip>
            </div>

            <div class="main-btn">
              <a-button type="primary" @click="addScenario" size="small" class="mr10" v-if="isEditEnabled" :loading="syncLoading">添加场景</a-button>
            </div>
            <div class="main-btn">
              <a-button type="primary" size="small" class="mr10" @click="showPositiveElectrodeAccounting" v-if="isEditEnabled" :loading="syncLoading">正极材料核算</a-button>
            </div>

            <div class="main-btn">
              <a-popconfirm placement="topRight" title="确认核算完成？" @confirm="() => bomCostOverviewFinish()">
                <a-button type="primary" size="small" class="mr10" v-if="isEditEnabled" :loading="syncLoading">完成核算</a-button>
              </a-popconfirm>
            </div>

            <div class="main-btn" v-if="isShowAllSearch">
              <a-button class="mr10" type="default" size="small" @click="handleExport" :loading="exportLoading">
                <a-icon type="download" />
                导出
              </a-button>
            </div>

            <div class="main-btn" v-if="isShowAllSearch">
              <a-button type="default" size="small" @click="showAccountingResult">
                <a-icon type="double-right" />
                核算结果分析
              </a-button>
            </div>

            <div class='toggle-btn'>
                <a-button size='small' type='link' @click='handleChangeSearch'>
                    {{isShowAllSearch ? '收起' : '展开'}}
                    <span v-if='isShowAllSearch'>
                        <a-icon type='double-left' />
                    </span>
                    <span v-else>
                        <a-icon type='double-right' />
                    </span>
                </a-button>
            </div>
            
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      
      <template #table>
        <ag-grid-vue
          :style='`height: ${tableHeight}px`'
          class='table ag-theme-balham'
          :tooltipShowDelay="0"
          :defaultColDef="defaultColDef"
          :grid-options="gridOptions"
          :columnDefs='columnDefs'
          :rowData='rowData'
          :enableCellSpan="true"
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
          @grid-ready="onGridReady"
        />
      </template>
    </tableIndex>

    <!-- 正极材料核算弹窗 -->
    <PositiveElectrodeAccountingModal
      ref="positiveElectrodeAccountingModal"
      @refresh-main-table="handleRefreshMainTable"
    />

    <!-- 核算结果弹窗 -->
    <AccountingResultModal
      ref="accountingResultModal"
    />
  </div>
</template>

<script>
import { getBomAccountingDetailList, bomAccountingDetailEdit, bomAccountingDetailUpdateBaseUse, bomAccountingDetailExport, bomAccountingDetailDelete, syncBomAccountingDetail } from "@/api/modular/system/bomAccountingDetailManage"
import { getScenarioIds, scenarioDelete, scenarioBatchUpdate } from "@/api/modular/system/scenarioManage"
import { bomCostOverviewGet, bomCostOverviewEdit, bomCostOverviewFinish } from "@/api/modular/system/bomCostOverviewManage"
import { scenarioCopy } from "@/api/modular/system/scenarioManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import PositiveElectrodeAccountingModal from './PositiveElectrodeAccountingModal'
import AccountingResultModal from './AccountingResultModal'

export default {
  components:{
    addBomAccountingDetail: () => import("./addBomAccountingDetail"),
    editBomAccountingDetail: () => import("./editBomAccountingDetail"),
    PositiveElectrodeAccountingModal,
    AccountingResultModal,
    showRender:{
       template:`
          <div>{{params.onShow(params.data)}}</div>
       `
    },
    
    mergedActionRender:{
        template: `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; width: 100%;">
            <div class="btns">
                <a v-if="!params.data['hide'+ params.scenarioId]" @click="params.toggleColumn(params.data,params.scenarioId)">隐藏</a>
                <a v-else @click="params.toggleColumn(params.data,params.scenarioId)">展开</a>
                <a-popconfirm v-if="params.isedit()" placement="topRight" title="确认删除该场景？" @confirm="() => params.onDel(params.scenarioId)">
                    <a>删除</a>
                </a-popconfirm>
                <span v-else>删除</span>
            </div>
        </div>
        `
    },

    materialDeleteRender:{
        template: `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; width: 100%;">
            <div class="btns">
                <a-popconfirm v-if="params.isEditEnabled()" placement="topRight" title="确认删除该物料？" @confirm="() => params.onDelete(params.data)">
                    <a style="color: #ff4d4f;">删除物料</a>
                </a-popconfirm>
                <span v-else style="color: #ccc;">删除物料</span>
            </div>
        </div>
        `
    },
  },
  data() {
    return {
      isEditEnabled: false,
      isShowAllSearch: true,
      tableHeight: document.documentElement.clientHeight - 40 - 16 - 100 - 80,
      templateHeight: document.documentElement.clientHeight - 40 - 16 - 100 -80,
      loading: false,
      saving: false,
      exportLoading: false,
      syncLoading: false,
      bomCostOverview: {},
      queryParam: {
        bomCostOverviewId: undefined,
      },
      rowData: [],
      changedRows: new Set(), // 跟踪已变更的行
      updateTimers: new Map(), // 防抖定时器
      gridApi: null, // ag-grid API实例
      accountingTypeTimer: null, // 核算类型切换防抖定时器
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false
      },
      gridOptions: {
        onCellValueChanged: this.onCellValueChanged,
        stopEditingWhenCellsLoseFocus: true,
        suppressRowTransform: true, // 确保rowSpan正常工作
        getRowClass: params => {
          // 为已编辑的行添加样式
          return this.changedRows.has(params.data.id) ? 'row-edited' : ''
        }
      },
      columnDefs: [
        {
          headerName: "序号",
          width: 70,
          field: "no",
          align: "center",
          pinned: 'left',
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "物料代码",
          width: 120,
          field: "partNumber",
          pinned: 'left',
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            if (this.isEditEnabled) {
              const value = params.value || ''
              const rowId = params.node.id
              return `<input type="text"
                             value="${value}"
                             data-row-id="${rowId}"
                             data-field="partNumber"
                             style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                    text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                             onchange="window.handleCellChange && window.handleCellChange(this)"
                             onblur="window.handleCellBlur && window.handleCellBlur(this)" />`
            }
            return params.value || ''
          }
        },
        {
          headerName: "物料名称",
          width: 150,
          field: "partName",
          pinned: 'left',
          align: "center"
        },
        {
          headerName: "物料类型",
          width: 100,
          field: "materialType",
          align: "center",
          cellRenderer: "showRender",
          cellRendererParams:{
             onShow: this.materialTypeShow,
          }
        },
        {
          headerName: "物料规格",
          minWidth: 150,
          field: "partDesc",
          align: "center",
          flex:1
        },
        {
          headerName: "单位",
          width: 100,
          field: "partUnit",
          align: "center",
          editable: false,
          cellRenderer: params => {
            if (this.isEditEnabled) {
              const value = params.value || ''
              const rowId = params.node.id
              return `<input type="text"
                             value="${value}"
                             data-row-id="${rowId}"
                             data-field="partUnit"
                             style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                    text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                             onchange="window.handleCellChange && window.handleCellChange(this)"
                             onblur="window.handleCellBlur && window.handleCellBlur(this)" />`
            }
            return params.value || ''
          }
        },
        {
          headerName: "理论用量(B0)",
          width: 100,
          field: "baseUse",
          align: "center",
          editable: false,
          cellRenderer: params => {
            if (this.isEditEnabled) {
              const value = params.value !== null && params.value !== undefined ? Number(params.value).toFixed(3) : ''
              const rowId = params.node.id
              return `<input type="number"
                             value="${value}"
                             data-row-id="${rowId}"
                             data-field="baseUse"
                             step="0.001"
                             style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                    text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                             onchange="window.handleCellChange && window.handleCellChange(this)"
                             onblur="window.handleCellBlur && window.handleCellBlur(this)" />`
            }
            if (params.value !== null && params.value !== undefined) {
              return Number(params.value).toFixed(3)
            }
            return '-'
          }
        },
        {
          headerName: "结构件标志",
          width: 100,
          field: "structureFlag",
          align: "center",
          editable: false,
          cellRenderer: params => {
            if (this.isEditEnabled) {
              const value = params.value !== null && params.value !== undefined ? params.value : 0
              const rowId = params.node.id
              return `<select data-row-id="${rowId}"
                              data-field="structureFlag"
                              style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                     outline: none; padding: 0; box-sizing: border-box;"
                              onchange="window.handleCellChange && window.handleCellChange(this)">
                        <option value="0" ${value === 0 ? 'selected' : ''}>否</option>
                        <option value="1" ${value === 1 ? 'selected' : ''}>是</option>
                      </select>`
            }
            const flagMap = { 1: '是', 0: '否' }
            return flagMap[params.value] || '-'
          }
        },
        {
          headerName: "正极体系",
          width: 100,
          field: "chemicalSystem",
          align: "center",
          editable: false,
        },
        {
          headerName: "物料规格描述",
          width: 150,
          field: "partDescription",
          align: "center",
          editable: false,
        }
      ]
    }
  },

  mounted() {
    // 添加全局方法用于处理单元格变更
    window.handleCellChange = (inputElement, isBlur = false) => {
      const rowId = inputElement.getAttribute('data-row-id')
      const field = inputElement.getAttribute('data-field')
      const value = inputElement.value

      if (this.gridApi && rowId && field) {
        // 找到对应的行数据
        const rowNode = this.gridApi.getRowNode(rowId)
        if (rowNode) {
          const oldValue = rowNode.data[field]
          let newValue = value

          // 根据字段类型转换值
          if (field === 'baseUse' || field.includes('untaxedUnitPrice')) {
            newValue = value === '' ? null : parseFloat(value)
          } else if (field === 'structureFlag') {
            newValue = parseInt(value)
          }

          // 更新数据
          rowNode.data[field] = newValue

          // 触发变更事件，传递是否为失去焦点事件
          this.onCellValueChanged({
            data: rowNode.data,
            oldValue: oldValue,
            newValue: newValue,
            colDef: { field: field },
            isBlur: isBlur // 标记是否为失去焦点事件
          })
        }
      }
    }

    // 添加专门处理失去焦点的方法
    window.handleCellBlur = (inputElement) => {
      window.handleCellChange(inputElement, true)
    }
  },

  beforeDestroy() {
    // 清理全局方法
    if (window.handleCellChange) {
      delete window.handleCellChange
    }
    if (window.handleCellBlur) {
      delete window.handleCellBlur
    }
  },

  watch: {
    isEditEnabled(newVal) {
      if (newVal) {
        // 进入编辑模式时，清空已修改行的记录
        this.changedRows.clear()
      }
      // 刷新表格以重新渲染单元格
      this.$nextTick(() => {
        if (this.gridApi) {
          this.gridApi.refreshCells()
        }
      })
    }
  },

  methods: {
    syncBomAccountingDetail(){
      this.syncLoading = true
      syncBomAccountingDetail({
        bomCostOverviewId: this.queryParam.bomCostOverviewId
      }).then(res => {
        if (res.success) {
          this.$message.success('同步成功')
          this.loadData()
        }
      }).finally(() => {
        this.syncLoading = false
      })
    },
    // 检查同一物料在不同场景下的未税单价是否存在差异
    checkUnitPriceDifference(rowData) {
      if (!rowData || !rowData.partNumber) {
        return false
      }

      // 获取当前物料的所有未税单价字段值
      const unitPrices = []

      // 遍历所有场景的未税单价字段
      Object.keys(rowData).forEach(key => {
        if (key.startsWith('untaxedUnitPrice') && rowData[key] !== null && rowData[key] !== undefined) {
          unitPrices.push(Number(rowData[key]))
        }
      })

      // 如果只有一个价格或没有价格，不需要高亮
      if (unitPrices.length <= 1) {
        return false
      }

      // 检查是否存在价格差异（允许小数点后3位的微小差异）
      const minPrice = Math.min(...unitPrices)
      const maxPrice = Math.max(...unitPrices)
      const priceDifference = Math.abs(maxPrice - minPrice)

      // 如果价格差异大于0.001，则认为存在差异
      return priceDifference > 0.001
    },

    // 导出Excel
    handleExport() {
      if (!this.queryParam.bomCostOverviewId) {
        this.$message.warning('请先选择BOM成本总览')
        return
      }

      this.exportLoading = true
      bomAccountingDetailExport({
        bomCostOverviewId: this.queryParam.bomCostOverviewId
      }).then(res => {
        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0')

        const fileName = `BOM核算明细_${timestamp}.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        //创建下载的链接
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        //下载后文件名
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        //点击下载
        downloadElement.click()
        //下载完成移除元素
        document.body.removeChild(downloadElement)
        //释放掉blob对象
        window.URL.revokeObjectURL(href)
        this.exportLoading = false
      }).catch(error => {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
        this.exportLoading = false
      })
    },

    // 显示正极材料核算弹窗
    showPositiveElectrodeAccounting() {
      if (!this.queryParam.bomCostOverviewId) {
        this.$message.warning('请先选择BOM成本总览')
        return
      }

      // 直接传递BOM成本总览ID，不限制特定场景
      this.$refs.positiveElectrodeAccountingModal.show(this.queryParam.bomCostOverviewId,this.bomCostOverview.accountingType)
    },

    // 显示核算结果弹窗
    showAccountingResult() {
      if (!this.queryParam.bomCostOverviewId) {
        this.$message.warning('请先选择BOM成本总览')
        return
      }

      this.$refs.accountingResultModal.show(this.queryParam.bomCostOverviewId)
    },

    addScenario(){
      scenarioCopy({bomCostOverviewId: this.queryParam.bomCostOverviewId}).then(res => {
        if (res.success) {
          this.$message.success('添加成功')
          this.getScenarioIds()

          // 如果正极核算弹出层是打开的，刷新其数据
          if (this.$refs.positiveElectrodeAccountingModal) {
            this.$refs.positiveElectrodeAccountingModal.refreshData()
          }
        } else {
          this.$message.error('添加失败：' + res.message)
        }
      })
    },
    materialTypeShow(record){
      return this.getDictName('bom_bill_material_type',record.materialType+'')
    },
    getDictName(code,key) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
          let name = dict.find(item=>item.code == key)?.name
          return name ?? '-'
    },
    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    bomCostOverviewGet(){
      bomCostOverviewGet({id: this.queryParam.bomCostOverviewId}).then(res => {
        this.bomCostOverview = res.data || {}
      })
    },
    handleChangeSearch() {
          this.isShowAllSearch = !this.isShowAllSearch;
          this.tableHeight = this.isShowAllSearch ? this.templateHeight  : this.templateHeight + 80
    },
    getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls));
			} else {
				var res = [];
				var reg = new RegExp(' ' + cls + ' ', 'i')
				var ele = parent.getElementsByTagName('*');
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(' ' + ele[i].className + ' ')) {
						res.push(ele[i]);
					}
				}
				return res;
			}
		},
		initMain() {
			let that = this
			that.$nextTick(() => {
				let items = that.getByClass(document, 'ant-layout-content')
				for (const e of items) {
					e.style.paddingLeft = 0
				}
			})
		},
    
    getScenarioIds() {
			this.loading = true
			getScenarioIds({ ...this.queryParam}).then(res => {
				if (res.success) {
          let columns = []
          for (const e of res.data) {
            let children = []
            children.push({
              headerName: `未税单价`,
              width: 120,
              field: `untaxedUnitPrice${e}`,
              align: "center",
              editable: false, // 禁用AG Grid的编辑
              cellRenderer: params => {
                const isEditable = this.isEditEnabled && (!params.data.chemicalSystem || params.data.chemicalSystem.trim() === '')
                const hasPriceDifference = this.checkUnitPriceDifference(params.data)

                if (isEditable) {
                  const value = params.value !== null && params.value !== undefined ? Number(params.value).toFixed(3) : ''
                  const rowId = params.node.id
                  const fieldName = `untaxedUnitPrice${e}`
                  const backgroundColor = hasPriceDifference ? '#fff3cd' : 'transparent' // 黄色高亮
                  return `<input type="number"
                                 value="${value}"
                                 data-row-id="${rowId}"
                                 data-field="${fieldName}"
                                 step="0.001"
                                 style="width: 100%; height: 90%; border: 1px solid #ddd; background: ${backgroundColor};
                                        text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                                 onchange="window.handleCellChange && window.handleCellChange(this)"
                                 onblur="window.handleCellBlur && window.handleCellBlur(this)" />`
                }
                if (params.value !== null && params.value !== undefined) {
                  const backgroundColor = hasPriceDifference ? 'background-color: #fff3cd;' : '' // 黄色高亮
                  return `<span style="${backgroundColor} display: block; width: 100%; height: 100%; line-height: 32px;">¥${Number(params.value).toFixed(3)}</span>`
                }
                return '-'
              }
            })
            children.push({
              headerName: `成本(CNY/EA)`,
              width: 120,
              field: `amountCnyEa${e}`,
              align: "center",
               cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `¥${Number(params.value).toFixed(3)}`
                }
                return '-'
              },
            })
            children.push({
              headerName: `成本(CNY/Wh)`,
              width: 120,
              field: `amountCnyWh${e}`,
              align: "center",
               cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `¥${Number(params.value).toFixed(3)}`
                }
                return '-'
              },
            })
            
            children.push({
              headerName: `占比`,
              width: 120,
              field: `proportion${e}`,
              align: "center",
              cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `${Number(params.value).toFixed(3)}%`
                }
                return '-'
              },
            })
            children.push({
              headerName: `操作`,
              width: 120,
              field: `action`,
              align: "center",
              cellRenderer: "mergedActionRender",
              rowSpan: (params) => {
                // 第一行跨越所有行，其他行返回0被合并
                if (params.node.rowIndex === 0) {
                  let totalRows = 0;
                  params.api.forEachNode(node => {
                    if (node.data) totalRows++;
                  });
                  return totalRows;
                } else {
                  return 0;
                }
              },
              cellClass: 'merged-action-cell',
              cellRendererParams: {
                onDel: this.delScenario,
                toggleColumn : this.toggleColumn ,
                scenarioId: e,
                isedit: this.getIsEditEnabled
              }
            })
            columns.push({
							headerName: `BOM成本-场景${e}`,
							children: children
						})
          }

          columns.push({
            headerName: `备注`,
            width: 120,
            pinned: 'right',
            field: `remark`,
            align: "center",
            editable: false, // 禁用AG Grid的编辑
            cellRenderer: params => {
              if (this.isEditEnabled) {
                const value = params.value || ''
                const rowId = params.node.id
                return `<input type="text"
                               value="${value}"
                               data-row-id="${rowId}"
                               data-field="remark"
                               style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                      text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                               onchange="window.handleCellChange && window.handleCellChange(this)"
                               onblur="window.handleCellBlur && window.handleCellBlur(this)" />`
              }
              return params.value || ''
            }
          })

          // 添加删除操作列
          columns.push({
            headerName: `删除操作`,
            width: 100,
            field: `deleteAction`,
            pinned: 'right',
            align: "center",
            editable: false,
            cellRenderer: "materialDeleteRender",
            cellRendererParams: {
              onDelete: this.deleteMaterial,
              isEditEnabled: () => this.isEditEnabled
            }
          })

					this.columnDefs = [...this.columnDefs.slice(0,10), ...columns]
					this.loadData()
				}
			}).finally(() => {
				this.loading = false
			})
		},
    getIsEditEnabled() {
      return this.isEditEnabled
    },
    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    
    loadData() {
      this.loading = true
      getBomAccountingDetailList({
        ...this.queryParam
      }).then(res => {
        if (res.success) {
          this.rowData = res.data
        }
      }).finally(() => {
        this.loading = false
      })
    },

    // 处理正极材料核算弹窗的刷新主表事件
    handleRefreshMainTable() {
      console.log('收到正极材料核算弹窗的刷新主表事件，开始刷新数据')
      this.loadData()
    },

    // 处理正极材料核算弹窗的刷新主表事件
    handleRefreshMainTable() {
      console.log('收到正极材料核算弹窗的刷新主表事件，开始刷新数据')
      this.loadData()
    },
    delScenario(scenarioId) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除该场景吗？',
        onOk: () => {
          this.loading = true
          scenarioDelete({ bomCostOverviewId: this.queryParam.bomCostOverviewId, scenarioId: scenarioId }).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.getScenarioIds()

              // 如果正极核算弹出层是打开的，刷新其数据
              if (this.$refs.positiveElectrodeAccountingModal) {
                this.$refs.positiveElectrodeAccountingModal.refreshData()
              }
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    // 删除物料
    deleteMaterial(record) {
      if (!record || !record.id) {
        this.$message.warning('无法删除该物料，缺少必要信息')
        return
      }

      this.$confirm({
        title: '确认删除物料',
        content: `确定要删除物料"${record.partName || record.partNumber}"吗？删除后将无法恢复。`,
        okText: '确认删除',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => {
          this.loading = true
          bomAccountingDetailDelete({ id: record.id }).then(res => {
            if (res.success) {
              this.$message.success('物料删除成功')
              this.loadData() // 重新加载数据

              // 如果正极核算弹出层是打开的，刷新其数据
              if (this.$refs.positiveElectrodeAccountingModal) {
                this.$refs.positiveElectrodeAccountingModal.refreshData()
              }
            } else {
              this.$message.error('删除失败：' + (res.message || '未知错误'))
            }
          }).catch(error => {
            console.error('删除物料失败:', error)
            this.$message.error('删除失败：' + (error.message || '网络错误'))
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    // 单元格值变更处理
    onCellValueChanged(params) {
      const { data, colDef, newValue, oldValue, api, isBlur } = params

      // 如果值没有变化，不处理
      if (newValue === oldValue) {
        return
      }

      console.log(`字段 ${colDef.field} 从 ${oldValue} 变更为 ${newValue}${isBlur ? ' (失去焦点)' : ''}`)

      // 标记该行已变更
      this.changedRows.add(data.id)

      // 刷新行样式
      if (api && api.redrawRows) {
        api.redrawRows({ rowNodes: [params.node] })
      }

      // 如果是失去焦点事件，立即保存；否则使用防抖
      if (isBlur) {
        this.updateFieldImmediately(data, colDef.field, newValue)
      } else {
        this.updateFieldByType(data, colDef.field, newValue)
      }
    },

    // 立即更新字段（失去焦点时调用）
    async updateFieldImmediately(data, fieldName, newValue) {
      const updateKey = `${data.id}_${fieldName}`

      // 清除防抖定时器（如果存在）
      if (this.updateTimers.has(updateKey)) {
        clearTimeout(this.updateTimers.get(updateKey))
        this.updateTimers.delete(updateKey)
      }

      try {
        if (fieldName === 'baseUse') {
          // 更新理论用量
          await this.updateBaseUse(data, newValue)
        } else if (fieldName.startsWith('untaxedUnitPrice')) {
          // 更新BOM场景的未税单价
          await this.updateScenarioPrice(data, fieldName, newValue)
        } else if (['partNumber', 'partUnit', 'structureFlag', 'remark'].includes(fieldName)) {
          // 更新物料代码、单位、结构件标志、备注
          await this.updateBasicField(data, fieldName, newValue)
        }
      } catch (error) {
        this.$message.error(`更新失败: ${error.message}`)
        console.error('字段更新失败:', error)
      }
    },

    // 根据字段类型调用不同的更新方法（带防抖）
    updateFieldByType(data, fieldName, newValue) {
      const updateKey = `${data.id}_${fieldName}`

      // 清除之前的定时器
      if (this.updateTimers.has(updateKey)) {
        clearTimeout(this.updateTimers.get(updateKey))
      }

      // 设置新的定时器，500ms后执行更新
      const timer = setTimeout(async () => {
        try {
          if (fieldName === 'baseUse') {
            // 更新理论用量
            await this.updateBaseUse(data, newValue)
          } else if (fieldName.startsWith('untaxedUnitPrice')) {
            // 更新BOM场景的未税单价
            await this.updateScenarioPrice(data, fieldName, newValue)
          } else if (['partNumber', 'partUnit', 'structureFlag','remark'].includes(fieldName)) {
            // 更新物料代码、单位、结构件标志
            await this.updateBasicField(data, fieldName, newValue)
          }
        } catch (error) {
          this.$message.error(`更新失败: ${error.message}`)
          console.error('字段更新失败:', error)
        } finally {
          // 清除定时器
          this.updateTimers.delete(updateKey)
        }
      }, 500)

      this.updateTimers.set(updateKey, timer)
    },

    // 更新理论用量
    async updateBaseUse(data, newValue) {
      console.log(`更新理论用量: ID=${data.id}, 新值=${newValue}`)

      const param = {
        id: data.id,
        baseUse: newValue
      }

      const result = await bomAccountingDetailUpdateBaseUse(param)
      if (result.success) {
        this.$message.success('理论用量更新成功')
        this.changedRows.clear()
        this.getScenarioIds()

        // 刷新表格行样式，移除编辑状态样式
        if (this.gridApi) {
          this.gridApi.redrawRows()
        }
      } else {
        this.$message.error('理论用量更新失败: ' + result.message)
        console.error('理论用量更新失败:', result)
      }
    },

    // 更新BOM场景的未税单价
    async updateScenarioPrice(data, fieldName, newValue) {
      // 从字段名中提取场景ID，例如 untaxedUnitPrice1 -> scenarioId = 1
      const scenarioId = fieldName.replace('untaxedUnitPrice', '')

      // 需要根据scenarioId找到对应的场景记录主键ID
      // 从当前行数据中获取对应场景的主键ID
      const scenarioIdKey = `id${scenarioId}`  // 对应后端的 "id"+s.getScenarioId()
      const scenarioRecordId = data[scenarioIdKey]  // 获取场景记录的主键ID

      if (!scenarioRecordId) {
        this.$message.error(`未找到场景${scenarioId}的记录ID`)
        return
      }

      console.log(`更新未税单价: BomAccountingDetailId=${data.id}, 场景ID=${scenarioId}, 场景记录ID=${scenarioRecordId}, 新值=${newValue}`)

      const param = {
        bomCostOverviewId: this.queryParam.bomCostOverviewId,
        bomAccountingDetailId: data.id,
        scenarioId: scenarioId,
        scenarios: [
          {
            id: scenarioRecordId,  // 使用场景记录的主键ID
            untaxedUnitPrice: newValue
          }
        ]
      }

      const result = await scenarioBatchUpdate(param)
      if (result.success) {
        this.$message.success(`场景${scenarioId}未税单价更新成功`)
        this.changedRows.clear()
        this.getScenarioIds()

        // 刷新表格行样式，移除编辑状态样式
        if (this.gridApi) {
          this.gridApi.redrawRows()
        }
      } else {
        this.$message.error('未税单价更新失败: ' + result.message)
        console.error('未税单价更新失败:', result)
      }
    },

    // 更新基础字段（物料代码、单位、结构件标志）
    async updateBasicField(data, fieldName, newValue) {
      const fieldNameMap = {
        partNumber: '物料代码',
        partUnit: '单位',
        structureFlag: '结构件标志',
        remark: '备注'
      }

      console.log(`更新${fieldNameMap[fieldName]}: ID=${data.id}, 新值=${newValue}`)

      const param = {
        id: data.id,
        [fieldName]: newValue
      }

      const result = await bomAccountingDetailEdit(param)
      if (result.success) {
        this.$message.success(`${fieldNameMap[fieldName]}更新成功`)
        this.changedRows.clear()

        // 刷新表格行样式，移除编辑状态样式
        if (this.gridApi) {
          this.gridApi.redrawRows()
        }

        console.log(`${fieldNameMap[fieldName]}更新成功:`, result)
      } else {
        this.$message.error(`${fieldNameMap[fieldName]}更新失败: ` + result.message)
        console.error(`${fieldNameMap[fieldName]}更新失败:`, result)
      }
    },

    // 批量保存所有更改
   /*  async saveAllChanges() {
      if (this.changedRows.size === 0) {
        this.$message.info('没有需要保存的更改')
        return
      }

      this.saving = true

      try {
        // 获取所有变更的行数据
        const changedData = this.rowData.filter(row => this.changedRows.has(row.id))

        // 逐个更新每条记录
        const updatePromises = changedData.map(item =>
          bomAccountingDetailUpdate(item)
        )

        await Promise.all(updatePromises)

        this.$message.success(`成功保存 ${changedData.length} 条记录`)
        this.changedRows.clear()

      } catch (error) {
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    }, */

    // 更新单个字段（可选实现）
    updateSingleField(id, field, value) {
      // 这里可以调用后端API保存单个字段的变更
      // 示例：
      // updateBomAccountingDetailField({ id, field, value }).then(res => {
      //   if (res.success) {
      //     this.$message.success('保存成功')
      //   } else {
      //     this.$message.error('保存失败：' + res.message)
      //   }
      // })

      console.log(`保存字段更新: ID=${id}, 字段=${field}, 值=${value}`)
    },

    toggleEditMode() {
      this.isEditEnabled = !this.isEditEnabled;
      // 刷新单元格（可选，仅当需要立即更新 UI 时）
      this.gridApi.refreshCells({ force: true });
    },

    // ag-grid准备就绪事件
    onGridReady(params) {
      this.gridApi = params.api
      this.columnApi = params.columnApi
    },

    toggleColumn(record,scenarioId){
      console.log(record)
      record['hide' + scenarioId] = !record['hide' + scenarioId]

      for(const item of this.rowData){
        item['hide' + scenarioId] = record['hide' + scenarioId]
      }
      
      const column1 = this.columnApi.getColumn('untaxedUnitPrice' + scenarioId)
      const column2 = this.columnApi.getColumn('amountCnyEa' + scenarioId)
      const column3 = this.columnApi.getColumn('amountCnyWh' + scenarioId)
      const column4 = this.columnApi.getColumn('proportion' + scenarioId)
      if(record['hide' + scenarioId]){
        this.columnApi.setColumnVisible(column1, false)
        this.columnApi.setColumnVisible(column2, false)
        this.columnApi.setColumnVisible(column3, false)
        this.columnApi.setColumnVisible(column4, false)
      }else{
        this.columnApi.setColumnVisible(column1, true)
        this.columnApi.setColumnVisible(column2, true)
        this.columnApi.setColumnVisible(column3, true)
        this.columnApi.setColumnVisible(column4, true)
      }
    },

    // 正极核算类型切换处理（带防抖）
    callFilter(accountingType) {
      console.log(`正极核算类型切换为: ${accountingType}`)

      // 清除之前的定时器
      if (this.accountingTypeTimer) {
        clearTimeout(this.accountingTypeTimer)
      }

      // 设置新的定时器，500ms后执行保存
      this.accountingTypeTimer = setTimeout(async () => {
        await this.saveAccountingType(accountingType)
        this.accountingTypeTimer = null
      }, 500)
    },

    bomCostOverviewFinish(){
      bomCostOverviewFinish({id:this.bomCostOverview.id}).then(res => {
        if (res.success) {
          this.$message.success('核算完成')

          setTimeout(() => {
            this.$router.push('/bombill/cost')
          }, 1000)

        } else {
        }
      })
    },

    // 保存核算类型
    async saveAccountingType(accountingType) {
      try {
        if (!this.bomCostOverview.id) {
          this.$message.error('BOM成本总览ID不存在')
          return
        }

        // 保存核算类型到bomCostOverview
        const param = {
          id: this.bomCostOverview.id,
          accountingType: accountingType
        }

        const result = await bomCostOverviewEdit(param)
        if (result.success) {
          this.$message.success('核算类型保存成功')
          console.log('核算类型保存成功:', result)

          // 更新本地数据
          this.bomCostOverview.accountingType = accountingType

          // 可选：重新加载相关数据
          // this.getBomAccountingDetailList()
        } else {
          this.$message.error('核算类型保存失败: ' + result.message)
          console.error('核算类型保存失败:', result)
        }

      } catch (error) {
        this.$message.error('核算类型保存失败: ' + error.message)
        console.error('核算类型保存失败:', error)
      }
    }

  },
  created() {
    this.initMain()
    this.queryParam.bomCostOverviewId = this.$route.query.id
    this.getScenarioIds()
    this.bomCostOverviewGet()
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    // 清理所有防抖定时器
    this.updateTimers.forEach(timer => {
      clearTimeout(timer)
    })
    this.updateTimers.clear()

    // 清理核算类型切换定时器
    if (this.accountingTypeTimer) {
      clearTimeout(this.accountingTypeTimer)
    }
  }
}
</script>
<style>
.ant-popover-message-title{
  white-space: pre-line;
}
</style>
<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: none;
}

/deep/.searchItem .label{
    width: initial;
}
.ant-breadcrumb a{
  color: rgba(0, 0, 0, 0.65);
}
.ant-breadcrumb {
	  font-size: 12px !important;
    margin: 12px;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
/deep/.search-container .vue-treeselect__multi-value-label{
    white-space: nowrap;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}
/deep/.search-container .vue-treeselect__limit-tip-text{
    font-weight: initial;
    text-indent: -32px;
    overflow: hidden;
    margin: 0;
}

// 已编辑行的样式
/deep/.ag-row.row-edited {
    
    background-color: #f6ffed !important;
    .ag-cell{
      background-color: #f6ffed !important;
    }
}

// 可编辑单元格的样式
/* /deep/.ag-cell-editable {
    background-color: #f6ffed !important;
    cursor: pointer;
} */

/deep/.ag-cell-editable:hover {
    background-color: #d9f7be !important;
}

// 合并操作列的样式
/deep/.merged-action-cell {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-right: 1px solid #eee !important;
    background-color: #fff !important;
}

/deep/.merged-action-cell .ag-cell-wrapper {
    height: 100% !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
</style>
