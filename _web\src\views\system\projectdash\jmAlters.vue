<template>
  <div class="container">
    <!-- Breadcrumb 面包屑 start -->
    <div>
      <a-breadcrumb class="breadcrumb" separator=">">
        <a-breadcrumb-item
        ><a @click="$router.push('/jm_product_chart')"
        ><span class="" style="width: 100%; height: 100%; min-width: 14px; min-height: 14px; margin-right:4px"
        ><svg
          xmlns="http://www.w3.org/2000/svg"
          class="styles__StyledSVGIconPathComponent-sc-16fsqc8-0 xuJzg svg-icon-path-icon fill"
          viewBox="0 0 48 48"
          width="14"
          height="14"
        >
								<defs data-reactroot=""></defs>
								<g>
									<rect width="48" height="48" fill="white" fill-opacity="0.01"></rect>
									<path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M44 40.8361C39.1069 34.8632 34.7617 31.4739 30.9644 30.6682C27.1671 29.8625 23.5517 29.7408 20.1182 30.303V41L4 23.5453L20.1182 7V17.167C26.4667 17.2172 31.8638 19.4948 36.3095 24C40.7553 28.5052 43.3187 34.1172 44 40.8361Z"
                    fill="none"
                    stroke="#333"
                    stroke-width="4"
                    stroke-linejoin="round"
                  ></path>
								</g></svg></span
        >首页看板</a
        ></a-breadcrumb-item
        >
        <a-breadcrumb-item>产品变更管理</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <!-- Breadcrumb 面包屑 start -->
    <div>
      <!-- 表格 start -->
      <tableIndex
        ref="pbiTableIndex"
        :pageLevel='4'
        :tableTotal= 'tableTotal'
        :pageTitleShow=false
        :height ="'auto'"
        :loading='loading'
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
      >
        <template #search>
          <pbiSearchContainer>

            <pbiSearchItem label='所属部门' :span="6">
              <treeselect :limit='1' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择所属部门'
                          value-consists-of='BRANCH_PRIORITY' v-model='queryParam.depts' :multiple='true'
                          :options='deptsOptions' />
            </pbiSearchItem>

            <pbiSearchItem label='变更分类' :span="6">
              <treeselect :limit='1' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择变更分类'
                          value-consists-of='BRANCH_PRIORITY' v-model='queryParam.alterTypes' :multiple='true'
                          :options='alterTypeOptions' />
            </pbiSearchItem>

            <pbiSearchItem label='产品名称' :span="6">
              <a-input size='small' @keyup.enter.native='callFilter' v-model='queryParam.productProjectName'
                       placeholder='请输入产品名称'>
                <a-icon slot='suffix' type='search' style='color: rgba(0,0,0,.45); width: 100%;' />
              </a-input>
            </pbiSearchItem>

            <pbiSearchItem type='btn' :span="6">
              <div class="main-btn">
                <a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
              </div>
            </pbiSearchItem>

          </pbiSearchContainer>

        </template>

        <template #table>
          <ag-grid-vue :style='`height: ${tableHeight}px`'
                       class='table ag-theme-balham'
                       :tooltipShowDelay="0"
                       :defaultColDef="defaultColDef"
                       :columnDefs='columnDefs'
                       :suppressDragLeaveHidesColumns="true"
                       :suppressMoveWhenColumnDragging="true"
                       :rowData='rowData'>
          </ag-grid-vue>
        </template>

      </tableIndex>
      <!-- 表格 end -->
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import { getAltersForProduct } from "@/api/modular/system/jmChartManage"
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
    Treeselect,
    alterContentCellRender:{
      template: `
            <div class="ellipsis" @click="params.onInfo(params.onSplit(params.value))">{{params.onCombine(params.value)}}</div>
            `
    },
    alterReasonCellRender:{
      template: `
            <div class="ellipsis" >{{params.value}}</div>
            `
    }
  },
  data() {
    return {
      tableHeight: document.documentElement.clientHeight - 200 ,
      deptsOptions: [],
      alterTypeOptions: [],
      queryParam: {
        depts: [],
        alterTypes: [],
        productProjectName: null
      },
      pageNo: 1,
      pagesize: 20,
      loading: false,
      tableTotal: 0,
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
        wrapText: true,
        autoHeight: true,
        flex:1,
        tooltipValueGetter: this.pbiTooltip
      },
      columnDefs: [
        {
          headerName: "序号",
          maxWidth: 50,
          cellRenderer: function (params) {
            return parseInt(params.node.id) + 1
          },
        },{
          headerName: "产品名称",
          field: "product",
          width: 140,
        },
        {
          headerName: "变更类型",
          field: "alterType",
          width: 80,
          align: "center",
        },
        {
          headerName: "变更分类",
          field: "alterTypes",
          width: 80,
          align: "center",
          cellRenderer: this.alterTypeCellRenderer,
        },
        {
          headerName: "发起时间",
          field: "alterDate",
          width: 100,
          align: "center",
        },
        {
          headerName: "变更原因",
          field: "alterReason",
          minWidth: 140,
          cellRenderer: 'alterReasonCellRender',
          //cellStyle: () =>  {return {textAlign:'left'}},
          tooltipValueGetter: (p) => p.value,
        },
        {
          headerName: "变更内容",
          field: "alterContent",
          minWidth:220,
          cellStyle: () =>  {return {textAlign:'left'}},
          cellRenderer: 'alterContentCellRender',
          tooltipValueGetter: params => {
            // 返回 null 或 undefined 则不显示 tooltip
            return null;
          },
          cellRendererParams: {
            onInfo: this.info,
            onSplit: this.spilt,
            onCombine: this.combine
          },
        },{
          headerName: "研究所",
          field: "alterDept",
          width: 140,
          align: "center",

        },
        {
          headerName: "变更负责人",
          field: "alterCharge",
          width: 100,
          align: "center",
        },
        {
          headerName: "审批状态",
          field: "pushStatus",
          width: 100,
          align: "center",
          cellRenderer: function (params) {
            return params.value == 2 ? '已审核' : '审核中'
          },
        },
        {
          headerName: "实施时间",
          field: "alterProcessDate",
          width: 100,
          align: "center",
        },
      ],
      sourceData: [],
      rowData: []
    }
  },
  methods: {
    getByClass(parent, cls) {
      if (parent.getElementsByClassName) {
        return Array.from(parent.getElementsByClassName(cls));
      } else {
        var res = [];
        var reg = new RegExp(' ' + cls + ' ', 'i')
        var ele = parent.getElementsByTagName('*');
        for (var i = 0; i < ele.length; i++) {
          if (reg.test(' ' + ele[i].className + ' ')) {
            res.push(ele[i]);
          }
        }
        return res;
      }
    },
    initMain() {
      let that = this
      that.$nextTick(() => {
        let items = that.getByClass(document, 'ant-layout-content')
        for (const e of items) {
          e.style.paddingLeft = 0
        }
      })
    },
    info(strArr) {
      const h = this.$createElement;
      let msgs = []
      for (let msg of strArr) {
        msgs.push(h('p',msg))
      }
      this.$info({
        title: '变更详情',
        content: h('div', {},msgs),
        onOk() {},
      });
    },
    spilt(str) {
      if (str == "{}") {
        return []
      }
      let bomChangeJson = JSON.parse(str)
      if (!bomChangeJson.changes) {
        return []
      }
      let arr = []
      if (bomChangeJson.changes != undefined) {
        for (const item of bomChangeJson.changes) {
          arr.push(
            `主物料:${item.mSapNumber}${item.flag}子物料:${item.sapNumber}-${item.partDescription}-使用量:${item.partUse}-变更前使用量:${item.prePartUse}`
          )
        }
      }
      return arr
    },
    combine(str) {
      if(null == str){
        return str
      }
      if(!str.startsWith("{")){
        return str
      }
      if (str == "{}") {
        return ""
      }
      let bomChangeJson = JSON.parse(str)
      if (!bomChangeJson.changes) {
        return ""
      }
      let strarr = ""
      if (bomChangeJson.changes != undefined) {
        let arr = bomChangeJson.changes.slice(0, bomChangeJson.changes.length > 3 ? 3 : bomChangeJson.changes.length)
        for (const item of arr) {
          strarr += `主物料:${item.mSapNumber}${item.flag}子物料:${item.sapNumber}-${item.partDescription}-使用量:${item.partUse}-变更前使用量:${item.prePartUse}\n`
        }
      }
      return strarr
    },

    tableFocus() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      this.$el.style.setProperty('--scroll-display', 'unset');
      this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      this.$el.style.setProperty('--scroll-display', 'none');
      this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
    },
    getDictName(code,key) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
      let name = dict.find(item=>item.code == key)?.name
      return name ?? '-'
    },
    alterTypeCellRenderer(params){
      return this.getDictName('alter_type',params.value+'')
    },
    handlePageChange(value){
      let { current, pageSize } = value
      this.pageNo = Number.isNaN(current) ? 1 : current
      this.pageSize = pageSize
      this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.rowData = this.sourceData.slice((this.pageNo - 1) * pageSize, this.pageNo * pageSize);
    },
    callFilter(){
      this.pageNo = 1
      this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.getAltersForProduct()
    },
    getAltersForProduct() {
      this.loading = true
      getAltersForProduct(this.queryParam)
        .then(res => {
          if (res.success) {
            this.tableTotal = res.data.length
            this.sourceData = res.data??[]

            setTimeout(() => {

              this.$nextTick(() => {
                this.pageNo = 1
                this.pageSize = 20
                this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(1,20)
                this.handlePageChange({current:1, pageSize:20})
              })

            }, 100);

          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    getAlterTypeOptions(){
      let items = this.getDict('alter_type')
      items.sort((a,b)=>a.sort-b.sort)

      let options = []
      for (const item of items) {
        let _item = {
          id: parseInt(item.code),
          label: item.name
        }
        options.push(_item)
      }
      this.alterTypeOptions.push(...options)
    },
    getDeptJiraOptionList() {
      this.deptsOptions = [{
        id: '22269',
        label: '储能所',
      },{
        id: '18711',
        label: '铁锂所',
      }, {
        id: '22487',
        label: '锰铁锂所',
      }];
    },
  },
  mounted() {
    this.initMain()
    this.getDeptJiraOptionList()
    this.getAlterTypeOptions()
    if(this.$route.query.deptId){
      this.queryParam.depts.push(this.$route.query.deptId)

    }
    this.getAltersForProduct()
  },
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';

/deep/.searchItem .label{
  width: initial;
}
// 面包屑
.ant-breadcrumb a {
  color: #40a9ff !important;
}
.ant-breadcrumb {
  font-size: 12px !important;
  margin: 0 12px 12px;
}
/deep/.ag-cell-value div.ellipsis {
  cursor: pointer;
  font-size: 12px;
  text-align: left !important;
  padding-left: 2px;
  word-break: break-all;
  line-height: 1.5em;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  background: initial;
  overflow: hidden;
  display: -webkit-inline-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
/deep/.ag-ltr .ag-cell {
  line-height: 1.5;
}
/deep/.ag-header-cell-label {
  text-overflow: clip;
  overflow: visible;
  white-space: normal;
}
/deep/.ag-cell {
  white-space: normal;
  word-wrap: break-word;
  height: auto;
  padding: 8px;
  min-height: 100%;
  display: flex;
  justify-content: center;
}
/deep/ .ag-cell-value,
/deep/ .ag-cell-value div{
  text-align: center !important;
}

/deep/ .ag-ltr .ag-header-select-all,
/deep/ .ag-ltr .ag-selection-checkbox{
  margin-right: 0;
}

/deep/ .ag-header-cell{
  display: flex;
  justify-content: center;
}

// 序号的高度
/deep/.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value){
  height: fit-content;
}
</style>
