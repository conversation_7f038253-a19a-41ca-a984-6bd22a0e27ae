package eve.sys.modular.product.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import eve.core.pojo.page.PageResult;
import eve.sys.modular.product.entity.ProductManager;
import eve.sys.modular.product.param.ProductProjectItem;
import eve.sys.modular.product.param.ProjectDetail;

public interface IProductManagerService extends IService<ProductManager>{
    List<ProductProjectItem> getAllProducts(String token);
    List<ProductProjectItem> getProducts(Boolean isVcyBoolean, Boolean isJMAreaBoolean);
    ProjectDetail getProduct(ProductProjectItem param);
    PageResult<ProductProjectItem> pageProjects(String token,ProductManager param);
    ProductProjectItem getProject(String token,ProductManager param);
    List<ProductProjectItem> getProducts(ProductManager param);
    
}
