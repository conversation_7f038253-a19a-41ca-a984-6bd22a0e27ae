package eve.sys.modular.bombill.bomaccountingdetail.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomaccountingdetail.params.BomAccountingDetailParam;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * BOM核算明细Service接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IBomAccountingDetailService extends IService<BomAccountingDetail> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<BomAccountingDetail> pageList(BomAccountingDetail param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return 列表结果
     */
    List<JSONObject> list(BomAccountingDetail param);

    /**
     * 新增
     *
     * @param param 新增参数
     * @return 是否成功
     */
    Boolean add(BomAccountingDetail param);

    /**
     * 删除
     *
     * @param param 删除参数
     * @return 是否成功
     */
    Boolean delete(BomAccountingDetail param);

    /**
     * 更新
     *
     * @param param 更新参数
     * @return 是否成功
     */
    Boolean update(BomAccountingDetail param);

    /**
     * 根据ID查询
     *
     * @param param 查询参数
     * @return 查询结果
     */
    BomAccountingDetail get(BomAccountingDetail param);

    void insertBatch(List<BomAccountingDetailParam> params,BomCostOverview bomCostOverview);

    /**
     * 从Excel导入BOM核算明细
     *
     * @param bomCostOverview BOM成本总览
     * @throws Exception 异常
     */
    void importfromExcel(BomCostOverview bomCostOverview) throws Exception;

    /**
     * 从BOM导入核算明细
     *
     * @param bomCostOverview BOM成本总览
     */
    void importfromBom(BomCostOverview bomCostOverview);

    void exportTemplate(HttpServletResponse response, BomAccountingDetail param);

    void updateBaseUse(BomAccountingDetail param);

    /**
     * 导出BOM核算明细Excel
     *
     * @param param 查询参数
     * @param response HTTP响应
     */
    void exportExcel(BomAccountingDetail param, HttpServletResponse response);

    /**
     * 获取核算结果
     *
     * @param param 查询参数
     * @return 核算结果
     */
    JSONObject getAccountingResult(BomAccountingDetail param);

    void syncBomAccountingDetail(BomAccountingDetail param);
}
