package eve.sys.jiraModular.qualityManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/24
 */
@Getter
@Setter
@TableName("quality_problem_test")
//@TableName("quality_problem")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityProblem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long issueId;

    private Date createDate;

    private Date updateDate;

    private Date dueDate;

    private String resolution;

    private String summary;

    private Long problemStatus;

    private String productId;

    private String productName;

    private String projectName;

    private String lampBasic;

    private String problemDescription;

    private String causeAnalysis;

    private String problemSolving;

    private String problemCode;

    private String projectStageName;

    private String projectStage;

    private String statusLampName;

    private String statusLamp;

    private String problemLevelName;

    private String problemLevel;

    private String problemCateName;

    private String problemCate;

    private String projectDepartmentName;

    private String projectDepartment;

    private String instituteName;

    private String institute;

    private String dlDepartment;//动力院-研究所

    private String dlDepartmentName;

    private String jmDepartment;//荆门院-研究所

    private String jmDepartmentName;

    private String findDate;

    private String planCompleteDate;

    private String actualCompleteDate;

    private String presenter;

    private String presenterName;

    private String responsiblePerson;

    private String responsiblePersonName;

    private String dqe;

    private String dqeName;

    private String rpm;

    private String rpmName;

    private String productManager;

    private String productManagerName;

    private String productTechMajordomo;

    private String productTechMajordomoName;

    private String headOfTheInstitute;

    private String headOfTheInstituteName;

    private String qualityVicePresident;

    private String qualityVicePresidentName;

    private String president;

    private String presidentName;

    private String issueKey;

    private String issueTypeName;

    private String issuestatusName;

    private String projectName1;

    private String priority;

    private String priorityName;

    private String reporterId;

    private String reporterName;

    private String creatorId;

    private String creatorName;

    private String assigneeId;

    private String assigneeName;

    @TableField(exist = false)
    private Long delayDay;

    @TableField(exist = false)
    private String keyWord;

    @TableField(exist = false)
    private List<String> statusLampList;

    @TableField(exist = false)
    private List<String> problemStatusList;

    @TableField(exist = false)
    private List<String> problemLevelList;

    @TableField(exist = false)
    private List<String> problemCateList;

    @TableField(exist = false)
    private List<String> projectDepartmentList;

    @TableField(exist = false)
    private List<String> instituteList;

    @TableField(exist = false)
    private List<String> dlDepartmentList;//动力院

    @TableField(exist = false)
    private List<String> jmDepartmentList;//荆门院

    @TableField(exist = false)
    private Long meetingFileId;
    @TableField(exist = false)
    private String meetingFileName;
    @TableField(exist = false)
    private String meetingFileUrl;
    @TableField(exist = false)
    private Long report8dFileId;
    @TableField(exist = false)
    private String report8dFileName;
    @TableField(exist = false)
    private String report8dFileUrl;
    @TableField(exist = false)
    private Long reportProblemFileId;
    @TableField(exist = false)
    private String reportProblemFileName;
    @TableField(exist = false)
    private String reportProblemFileUrl;
    @TableField(exist = false)
    private Long otherFileId;
    @TableField(exist = false)
    private String otherFileName;
    @TableField(exist = false)
    private String otherFileUrl;

    @TableField(exist = false)
    private Long parentDeptId;

}
