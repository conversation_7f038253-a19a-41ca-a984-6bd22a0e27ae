<template>
  <div class="product_width">
    <a-spin :spinning="loading">


      <!-- 表格 start -->
      <div>
        <a-table
          ref="table"
          :style="`height:${tableHeight}px;`"
          :rowKey="record => record.issueId + record.productCate"
          :columns="columns"
          :dataSource="loadData"
        >
					<span slot="productCate" slot-scope="text, record">
						{{ record.productOrProject == 1 ? record.productCateParent + (text != "" ? "->" + text : "") : "" }}
					</span>
          <span slot="A" slot-scope="text, record">
						<a @click="showDocs(record,2)">查看</a>
					</span>
          <span slot="B" slot-scope="text, record">
						<a @click="showDocs(record,3)">查看</a>
					</span>
          <span slot="C" slot-scope="text, record">
						<a @click="showDocs(record,4)">查看</a>
					</span>
        </a-table>
      </div>
      <!-- 表格 end -->
    </a-spin>
  </div>
</template>

<script>
import { getTreeProductsOfNotSplit } from "@/api/modular/system/jmChartManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import {  statusType } from "@/utils/enum.js"

export default {
  components: {
    Treeselect
  },
  data() {
    return {
      activeKey: "1",

      queryparam: {
        cates: [],
        states: [],
        depts:[],
        keyword: null,
      },
      loading: true,
      columns: [
        {
          title: "序号",
          width: 60,
          dataIndex: "no",
          align: "center",
          customRender: (text, record, index) => {
            if (record.productOrProject == 1) {
              return `${index + 1}`
            }
            return ""
          }
        },
        {
          title: "产品名称",
          align: "center",
          width: '20%', // 设置列宽为40%
          dataIndex: "productProjectName",
          scopedSlots: {
            customRender: "productProjectName"
          }
        },
        {
          title: "产品状态",
          align: "center",
          width: '20%', // 设置列宽为40%
          dataIndex: "productState",
          customRender: text => statusType[text]
        },
        /* {
          title: "产品经理",
          align: "center",
          dataIndex: "productManager"
        }, */
        {
          title: "A样",
          align: "center",
          dataIndex: "A",
          width: '20%', // 设置列宽为40%
          scopedSlots: {
            customRender: "A"
          }
        },
        {
          title: "B样",
          align: "center",
          dataIndex: "B",
          width: '20%', // 设置列宽为40%
          scopedSlots: {
            customRender: "B"
          }
        },
        {
          title: "C样",
          align: "center",
          dataIndex: "C",
          width: '20%', // 设置列宽为40%
          scopedSlots: {
            customRender: "C"
          }
        }
      ],
      loadData: [],
      totalData: [],
    }
  },
  props: {
    // 表格高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },
  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "80px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    showDocs(record,state) {
      this.$emit("showdetail", record,state)
    },
    onTabChange(key) {
      this.activeKey = key
    },
    change() {
      this.callFilter()
    },

    // 数据筛选
    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

      // 产品分类
      if (this.queryparam["cates"].length > 0) {
        filterData = filterData.filter(
          item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
        )
        if (this.queryparam["cates"].indexOf(2) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["depts"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
      }

      // 产品名称
      if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
        const temList = []
        this.searchParam.inputSearch.forEach(v => {
          if (v.keyword === "") return
          filterData.forEach(e => {
            if (e.productName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
              temList.push(e)
            }
          })
        })
        filterData = _.uniqWith(temList, _.isEqual)
      }

      if (this.queryparam.startDate != null) {
        filterData = filterData.filter(
          item =>
            Date.parse(item.initiationDate) >= this.queryparam.startDate &&
            Date.parse(item.initiationDate) < this.queryparam.endDate
        )
      }

      // 表格数据
      this.loadData = filterData
    },
    dateChange(date, dateString) {
      if (dateString[0] != null && dateString[0] != "") {
        this.queryparam.startDate = Date.parse(dateString[0])
      } else {
        this.queryparam.startDate = null
      }
      if (dateString[1] != null && dateString[1] != "") {
        this.queryparam.endDate = Date.parse(dateString[1])
      } else {
        this.queryparam.endDate = null
      }
      this.callFilter()
    },

    handleOk() {
      this.getTreeProducts()
    },
    getTreeProducts() {
      this.loading = true
      getTreeProductsOfNotSplit({})
        .then(res => {
          if (res.success) {
            for (let item of res.data) {
              delete item.children
            }
            this.totalData = JSON.parse(JSON.stringify(res.data))
            this.callFilter()
          } else {
            this.$message.error(res.message, 1)
          }
          this.loading = false
        })
        .catch(err => {
          this.loading = false
          this.$message.error("错误提示：" + err.message, 1)
        })
    }
  },
  created() {
    this.getTreeProducts()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  }
}
</script>

<style lang="less" scoped>
@import "./productoption.less";
/deep/.ant-tabs-nav-scroll {
  margin-left: 60px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
  margin-right: 20px;
}

:root {
  --height: 600px;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}

// th高度
/deep/.ant-table-thead > tr > th {
  padding: 5px;
}

/deep/.ant-table-tbody > tr > td {
  padding: 5px;
}
</style>
