package eve.sys.modular.product.param;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import eve.sys.modular.finance.statistics.entity.ProjectCost;
import eve.sys.modular.weekprocess.entity.WeekProcess;
import eve.sys.modular.weekprocess.entity.WeekProcessDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ProductProjectItem implements Serializable{

    private Long id;
    private Long issueId;
    private String issueKey;
    private Long cateId;//产品类别子级id
    private Long catepid;//产品类别父级id
    private Long productClassification;//产品分类

    private String parentDeptName; //-- 父级部门名称
    private String childDeptName; //-- 子级部门名称

    private String productProjectName;//产品项目名
    private String projectName;//项目名
    private Long productOrProject;//是否产品/项目

    private String customer;//客户
    private int fixedState;//定点状态

    private int mStatus;//产品阶段

    private String dept;//默认获取等于0的所属部门，没有获取子级部门名称
    private Long deptId;//pid 等于0的所属部门

    private String productCateParent;//父级类别的名称
    private String productCate;//子级类别的名称

    private Date stopTime;//暂停提出时间

    private int state;//产品状态
    private Long productState;//产品状态另外表达 1：正常，2：延期，3：停止
    private String stateTxt;//产品状态文本


    private String productLevel;//产品等级记文本

    private String productManager;//产品经理
    private String researchProjectManager;//RPM

    private String plannedFixedDate;///计划定点日期
    private String productPlannedDate;
    private String productActualDate;
    private String initiationDate;

    private Long productDistribute;//-- 产品分布
    private String productDistributeName;

    private String produceFeedback;//生产反馈
    private String sellFeedback;//销售反馈
    private String supplyFeedback;//供应反馈

    private String structureType; //-- 电池结构
    //private String scenario; //-- 应用场景

    private String productTechMajordomoName;//产品总监

    private String sellRepresent;//销售代表
    private String sellRepresentName;

    private Long vCylinderProductDepartment;//V圆柱产品部门
    private String vCylinderProductDepartmentName;

    /* PBI处理 */
    private String no;
    private WeekProcess weekProcess;
    private List<WeekProcessDetail> weekProcessDetail;
    private Integer wellState;
    private BigDecimal k0Reach;
    private BigDecimal m1Reach;
    private BigDecimal m2Reach;
    private BigDecimal m3Reach;
    private BigDecimal reach;
    private Long delayDays;
    private Boolean showPlan;

    private List<ProductCateOptionBean> productCateOptionBeans;
    private List<ProductCateOptionBean> departmentOptionList;
    private List<ProductStageItem> productStageItems;
    private List<ProductProjectItem> children;

    private List<Long> projectIds;
    private int ostate;//产品原始状态

    private String catePidKey;
    private String cateIdKey;

    private Long bomCount;
    private Long miCount;

    private Long isVcylinder;
    private Long isJMArea;
    private ProjectCost projectCost;

    private List<StageRange> stageRanges;

    private String statusTxt;

    private List<Integer> showFlag;

    private Integer stage;

    private BigDecimal budgetExe;


    private String lineId;      //产线ID

    private String lineName;    //产线名称

    private String werkNo;    //工厂Id

    /* PBI处理 end*/
}
