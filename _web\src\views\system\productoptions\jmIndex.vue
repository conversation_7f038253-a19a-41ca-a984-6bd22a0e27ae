<template>
	<div class="container">
		<!-- 面包屑 start -->
		<div>
			<a-breadcrumb class="breadcrumb" separator=">">
				<a-breadcrumb-item
					><a @click="gohome"><a-icon class="rollback-icon" type="rollback" />产品看板</a></a-breadcrumb-item
				>
				<a-breadcrumb-item><a @click="goprolist">产品矩阵图</a></a-breadcrumb-item>
				<a-breadcrumb-item
					><a v-if="activeKey == '2' && showdoc" @click="hidedoc">{{ tabsMenu[activeKey] }}</a
					><span v-else>{{ tabsMenu[activeKey] }}</span></a-breadcrumb-item
				>
				<a-breadcrumb-item v-if="activeKey == '2' && showdoc">{{ productName }}产品主要技术文档</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<!-- 面包屑 end -->

		<div class="table-page-search-wrapper">
			<a-form layout="inline">
				<a-row :gutter="24">
					<a-col :md="5" :sm="24">
						<a-form-item label="立项日期">
							<a-range-picker
								class="params"
								:placeholder="['开始日期', '结束日期']"
								size="small"
								@change="dateChange"
							/>
						</a-form-item>
					</a-col>
					<a-col :md="5" :sm="24">
						<a-form-item label="细分市场">
							<div class="filter-box">
								<treeselect
									class="params"
									v-model="searchParam.cates"
									:options="cateOptions"
									placeholder="请选择细分市场"
									:limit="1"
									:max-height="200"
									value-consists-of="BRANCH_PRIORITY"
									:multiple="true"
								/>
							</div>
						</a-form-item>
					</a-col>
					<a-col :md="5" :sm="24">
						<a-form-item label="产品状态">
							<div class="filter-box">
								<treeselect
									class="params"
									v-model="searchParam.states"
									:options="statesOptions"
									placeholder="请选择产品状态"
									:limit="1"
									:max-height="200"
									value-consists-of="BRANCH_PRIORITY"
									:multiple="true"
								/>
							</div>
						</a-form-item>
					</a-col>
					<a-col :md="5" :sm="24">
						<a-form-item label="产品部门">
							<div class="filter-box">
								<treeselect
									class="params"
									v-model="searchParam.depts"
									:options="deptsOptions"
									placeholder="请选择所属部门"
									:limit="1"
									:max-height="200"
									:multiple="true"
									value-consists-of="BRANCH_PRIORITY"
								>
								</treeselect>
							</div>
						</a-form-item>
					</a-col>
					<a-col :md="2" :sm="24" :style="{ float: 'right' }">
						<div class="table-page-search-submitButtons" :style="{marginBottom:'0'}">
							<a-icon style="margin-right: 10px;" :type="isShow ? 'up' : 'down'" @click="handleShowInputSearch" />
							<a-button size="small" type="primary" @click="query">查询</a-button>
							<a-button style="margin-left: 8px" v-if="hasPerm('docs:pm') && (activeKey == '1' || activeKey == '6' || activeKey == '0' || activeKey == '3')" size="small"  type="primary" @click="download" >导出</a-button>
							<a-button size="small" style="margin-left: 8px" @click="reset">重置</a-button>
						</div>
					</a-col>
				</a-row>
        <a-row :gutter="24" v-show="isShow">
          <a-col :md="5" :sm="24" v-if="activeKey == '4'">
            <a-form-item label="项目阶段">
              <div class="filter-box">
                <treeselect
                  class="params"
                  v-model="searchParam.stages"
                  :options="stagesOptions"
                  placeholder="请选择项目阶段"
                  :limit="1"
                  :max-height="200"
                  value-consists-of="BRANCH_PRIORITY"
                  :multiple="true"
                />
              </div>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24"  v-if="activeKey == '6'">
            <a-form-item label="选择周数">
              <a-week-picker style="margin: 0 auto" @change="handleSelectWeek" :value="dateValue"/>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item label="产品名称">
              <div class="input-wrap">
                <a-input
                  class="params"
                  v-for="(item,key) in searchParam.inputSearch"
                  v-model="item.keyword"
                  placeholder="请输入产品名称"
                  size="small"
                  :key="key"
                >
                  <a-icon slot="suffix" type="search" style="color: rgba(0,0,0,.45)" />
                </a-input>
                <a-icon
                  v-if="searchParam.inputSearch.length <= 4"
                  type="plus-circle"
                  @click="handleAdd"
                  style="color: rgba(0,0,0,.45);margin-left: 10px;"
                />
                <a-icon
                  v-if="searchParam.inputSearch.length > 1"
                  type="minus-circle"
                  @click="handleSubtract"
                  style="color: rgba(0,0,0,.45);margin-left: 10px;"
                />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
			</a-form>
		</div>

		<!-- 主标题 start -->
		<!-- <div class="head-title">{{ titleMenu[activeKey] }}</div> -->
		<!-- 主标题 end -->

		<!-- tabs start -->
    <div class="tabs-wrap">
      <a-tabs type="card" :activeKey="activeKey" @change="handleTabsActive" destroyInactiveTabPane>
        <a-tab-pane v-for="(item, index) in tabsMenu" :key="index" :tab="item">
        </a-tab-pane>

      </a-tabs>
      <div class="tab-wrap">
        <!--基本信息-->
        <baseInfo
          ref="infos"
          v-if="activeKey === 0"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!--产品参数信息-->
        <params
          ref="params"
          v-if="activeKey === 1"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!--产品里程碑-->
        <stageplan
          ref="stageplan"
          v-if="activeKey === 3"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!--产品周进展-->
        <process
          ref="process"
          v-if="activeKey === 6"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!--转阶段文档-->
        <stagedocs
          v-if="activeKey === 4"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!--问题管理-->
        <problems
          v-if="activeKey === 5"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
        />
        <!-- 列表文档-->
        <prodocs
          v-if="activeKey === 2 && !showdoc"
          :searchParam="searchParam"
          :tableHeight="tableHeight"
          :scrollHeigh="scrollHeigh"
          @showdetail="showdetail"
        />
        <!--详细数据文档-->
        <techdoc
          v-if="activeKey === 2 && showdoc"
          :tabHeight="tableHeight + 68"
          :issueId="issueId"
          :productState = "productState"
          :productName="productName"
          techStatus="2"
        />
      </div>

    </div>
		<!-- tabs end -->
	</div>
</template>

<script>
import baseInfo from "./jmBaseInfo"//荆门 基本信息
import process from "./jmProcess"//产品周进展
import params from "./jmParams"//荆门 产品参数信息
import technology from "./technology.vue"
import prodocs from "./jmProdocs"//荆门 主要技术文档
import techdoc from "./techdoc/index"//主要技术文档-查看
import stageplan from "./jmStageplan"//荆门 产品里程碑
import stagedocs from "./jmStagedocs"//荆门 转阶段文档
import problems from "./jmProblems"//荆门 问题管理
import { mixin } from "@/utils/mixin"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import moment from 'moment'
// 筛选框

import {getJiraOptionList} from "@/api/modular/system/jiraCustomTool";

import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import Stagedoc from '../projectdash/jmStagedoc.vue'//荆门 转阶段达成率

export default {
	mixins: [mixin],
	components: {
		baseInfo,//基本信息
		process,//产品周进展
		params,//产品参数信息
		technology,//---页面上似乎没使用到，暂时不变动-090000-20250606---
		prodocs,//主要技术文档
		techdoc,//主要技术文档-查看
		stageplan,//主要技术文档
		Treeselect,
		stagedocs,//转阶段文档
		problems,//问题管理
		Stagedoc//转阶段达成率
	},
	watch: {
		sidebarOpened(val) {
			this.collapsed = !val
		}
	},
  data() {
    return {
      productState:null,
      issueId: 0,
      showdoc: false,
      productName: "",
      dateValue:this.moment(new Date()),
      navTag: "-1",
      collapsed: false,
      width187: {
        width: "187px",
        textAlign: "right"
      },
      width40: {
        width: "40px",
        textAlign: "center"
      },
      // tabs
      activeKey: 0, // 当前激活 tab 面板的 key
      tabsMenu: ["基本信息", "产品参数信息", "主要技术文档", "产品里程碑", "转阶段文档", "问题管理", "产品周进展" ],
      // 表格高度
      // 40：layout 20 : 内边距 14：面包屑 32 主标题边距 27：标题 45：tabs块 68：筛选块 63：分页区
      tableHeight: document.documentElement.clientHeight - 40 - 20 - 14 - 20  - 45 - 68,
      scrollHeigh: document.documentElement.clientHeight - 40 - 20 - 14 - 20  - 45 - 68 - 63 - 20,

      // 筛选框
      searchParam: {
        weeks:[],
        dateString: [], // 立项日期
        cates: [], // 细分市场
        states: [], // 产品状态
        stages: [], //项目阶段
        depts: [], //产品部门
        inputSearch: [
          {
            keyword: ""
          }
        ]
      },
      isShow: true,
      cateOptions: [],
      statesOptions: [],
      stagesOptions: [],
      deptsOptions: [],
      isJMArea: null,
    }
  },
  created() {
    if (window.sessionStorage.getItem("tag")) {
      this.navTag = window.sessionStorage.getItem("tag")
    }
    this.collapsed = !this.sidebarOpened
    this.initMain()

    this.getJiraOptionList()
    this.getDeptJiraOptionList()
    this.getProductState()
    this.getProductStage()
    this.isJMArea = 1
  },
	methods: {
		moment,
		query() {
			let searchParam = {...this.searchParam}
			this.searchParam = searchParam
		},
    reset(){
      this.searchParam = {
        dateString: [], // 立项日期
        cates: [], // 细分市场
        states: [], // 产品状态
        stages: [], //项目阶段
        depts: [], //产品部门
        inputSearch: [
          {
            keyword: ""
          }
        ]
      }
    },
		getByClass(parent, cls) {
			if (parent.getElementsByClassName) {
				return Array.from(parent.getElementsByClassName(cls))
			} else {
				var res = []
				var reg = new RegExp(" " + cls + " ", "i")
				var ele = parent.getElementsByTagName("*")
				for (var i = 0; i < ele.length; i++) {
					if (reg.test(" " + ele[i].className + " ")) {
						res.push(ele[i])
					}
				}
				return res
			}
		},
    initMain() {
      let that = this
      that.$nextTick(() => {
        let items = that.getByClass(document, "ant-layout-content")
        for (const e of items) {
          e.style.paddingLeft = 0
        }
      })

      if (this.$route.query.deptId) {
        /* let cateIds = this.$route.query.cateId.split(',')
        for (const item of cateIds) {
          this.searchParam["cates"].push(parseInt(item))
        } */

        this.searchParam.depts.push( this.$route.query.deptId)

      }
      if (this.$route.query.keyword) {
        this.searchParam.inputSearch[0].keyword = this.$route.query.keyword
      }
    },
		hidedoc() {
			this.issueId = 0
			this.showdoc = false
		},
		showdetail(row,productState) {
			this.productName = row.productProjectName
			this.issueId = row.issueId
			this.showdoc = true
			this.productState = productState
		},
		gohome() {//返回首页产品看板
			this.$router.push({
				path: "/jm_product_chart"
			})
		},
		goprolist() {//返回产品矩阵图
			this.$router.push({
				path: "/jm_report_sum"
			})
		},
    showView(tg) {
      this.navTag = tg + ""
      window.sessionStorage.setItem("tag", tg + "")
    },
    // 切换tabs
    handleTabsActive(activeKey) {
      this.activeKey = activeKey
      sessionStorage.setItem("activeKey", activeKey)
    },

    handleSelectWeek(date,dateString){
      let param = [dateString,dateString]
      this.dateValue = date
      this.$set(this.searchParam, "weeks", param)
    },
    download(){
      if (this.activeKey == '1') {
        this.$refs.params.downloadParams()
        return
      }
      if (this.activeKey == '0') {
        this.$refs.infos.downloadBaseInfo()
        return
      }
      if (this.activeKey == '3') {
        this.$refs.stageplan.downloadStagePlan()
        return
      }
      this.$refs.process.weekExport()
    },
		// 筛选框
		dateChange(date, dateString) {
			this.$set(this.searchParam, "dateString", dateString)
		},
		handleShowInputSearch() {
			this.isShow = !this.isShow
		},
		handleAdd() {
			this.searchParam.inputSearch.push({ keyword: "" })
		},
		handleSubtract() {
			this.searchParam.inputSearch.pop()
		},
		getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
		getProductState(){
            let items = this.getDict('product_state_status')
            items.sort((a,b)=>a.sort-b.sort)
            let states = []
            for (const item of items) {
                let _item = {
                    id: parseInt(item.code),
                    label: item.name
                }
                states.push(_item)
            }
            this.statesOptions = states
        },
    getProductStage(){
      let items = this.getDict('product_stage_status')
      items.sort((a,b)=>a.sort-b.sort)
      let states = []
      for (const item of items) {
        let _item = {
          id: parseInt(item.code),
          label: item.name
        }
        states.push(_item)
      }
      this.stagesOptions = states
    },
    getJiraOptionList(){
      getJiraOptionList({fieldName:'jmProductCate'}).then(res => {
        if (res.success) {
          let cates = []
          let parentNodes = res.data.filter(o => o.parentoptionid == null)
          for (const item of parentNodes) {
            let _item = {
              id: parseInt(item.id),
              label: item.customvalue
            }
            let childNodes = res.data.filter(o => o.parentoptionid == item.id)
            if (childNodes && childNodes.length > 0) {
              _item.children = []
              for (const $item of childNodes) {
                _item.children.push({
                  id: parseInt($item.id),
                  label: $item.customvalue
                })
              }
            }
            cates.push(_item)
          }
          this.cateOptions = cates
        }
      })
    },

		getDeptJiraOptionList() {
/*            getJiraOptionList({ fieldName: 'department' }).then(res => {
                if (res.success) {
                    let depts = []
					let _depts = ["18846", "22492", "22487", "18711", "22101", "22269", "22105"]
                    let parentNodes = res.data.filter(o => o.parentoptionid == null)
                    for (const item of parentNodes) {
						if (_depts.indexOf(item.id) == -1) {
							continue
						}
                        let _item = {
                            id: (item.id),
                            label: item.customvalue
                        }
                        depts.push(_item)
                    }
					console.log(depts)
                    this.deptsOptions = depts
                }
            })*/
      this.deptsOptions = [{
        id: '22487',
        label: '锰铁锂电池研究所',
      }, {
        id: '18711',
        label: '铁锂电池研究一所',
      }, {
        id: '22269',
        label: '储能电池研究所',
      }];
        },
	},

	mounted() {
		if (!window.sessionStorage.getItem("tag")) {
			window.sessionStorage.setItem("tag", this.navTag)
		} else if (window.sessionStorage.getItem("tag")) {
			this.navTag = window.sessionStorage.getItem("tag")
		}

		if (sessionStorage.getItem("activeKey")) {
			this.activeKey = Number(sessionStorage.getItem("activeKey"))
		}
	},
	destroyed() {
		window.sessionStorage.removeItem("tag")
		sessionStorage.removeItem("activeKey")
	}
}
</script>
<style lang="less" scoped="">
@import './productoption.less';
/deep/.ant-layout-content {
	padding-left: 0 !important;
}
.wrap {
	background: #f0f2f5;
}
.layout-main {
	display: flex;
	flex-direction: row;
}
.wrap {
	flex: 1;
}
.slide {
	max-width: 200px;
	background-color: #fff;
	box-shadow: 2px 0px 8px 0px rgba(29, 35, 41, 5%);
	z-index: 100;
	height: 100vh;
}
.collapsed_bar {
	position: fixed;
	width: 20px;
	bottom: 0;
	/* top: 0; */
	cursor: pointer;
}
/deep/.ant-menu-light {
	border-right-color: transparent;
}
/deep/.ant-menu-inline-collapsed {
	width: 40px !important;
}
/deep/.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
/deep/.ant-menu-inline-collapsed > .ant-menu-item,
/deep/.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
/deep/.ant-menu-vertical .ant-menu-item,
/deep/.ant-menu-inline .ant-menu-item {
	margin-top: 0;
}

.ant-breadcrumb a {
	color: #40a9ff !important;
}
.ant-breadcrumb a:first-child {
	color: rgba(0, 0, 0, 0.65) !important;
}
.ant-breadcrumb {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.65) !important;
}
/deep/.ant-breadcrumb .anticon.anticon-home {
	font-size: 19px;
}
</style>
<style lang="less" scoped>
.ant-layout-sider-collapsed {
	flex: 0 0 40px !important;
	max-width: 40px !important;
	min-width: 40px !important;
	width: 40px !important;
}
.ant-menu-inline-collapsed > .ant-menu-item,
.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
.ant-menu-inline-collapsed
	> .ant-menu-item-group
	> .ant-menu-item-group-list
	> .ant-menu-submenu
	> .ant-menu-submenu-title,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
	padding: 0 12px !important;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title {
	font-size: 13px;
}


/* 主标题 */

.head-title {
	color: #333;
	padding: 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.head-title::before {
	width: 8px;
	background: #1890ff;
	margin-right: 8px;
	content: "\00a0"; /* 填充空格 */
}

/* tabs */
.tab-wrap {
	background-color: #fff;
	border-radius: 0 10px 10px 10px;
	margin-left: 1px;
}

/* tabs 组件 */
/deep/.ant-tabs-bar {
	margin: 0;
}

/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	border-radius: 10px 10px 0 0;
	font-size: 13px;
}

// tabs块级颜色
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
	background-color: rgba(232, 232, 232, 0.5);
	margin-right: 4px;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
	background-color: #fff;
}

.table-page-search-wrapper {
	background-color: #fff;
	padding: 10px;
	margin: 10px 0 0;
	border-radius: 10px;
}

.table-page-search-wrapper .ant-form-inline .ant-form-item {
	margin-bottom: 0;
}



.input-wrap {
	display: flex;
	align-items: center;
}
.input-wrap .params {
	height: 24px;
	width: 150px;
	margin-right: 10px;
}

// 筛选框高度
/deep/.ant-input-affix-wrapper .ant-input {
	height: 24px !important;
}
/deep/.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container{
	height: auto;
}
</style>
