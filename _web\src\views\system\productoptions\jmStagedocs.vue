<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<!-- 筛选区域 end -->

			<!-- 表格 start -->
			<div>
				<a-table
					ref="table"
					:rowKey="record => record.issueId + record.productChildCate"
					:columns="columns"
					:dataSource="loadData"
				>
				<span slot="k0reach" slot-scope="text,record">
						<span v-if="text == 0"><a  @click="toDetail(record,1)">--</a></span>
						<a v-else @click="toDetail(record,1)">{{(text *100).toString().indexOf(".") != -1 ? (text *100).toFixed(1)+'%' : (text *100)+'%'}}</a>
					</span>
					<span slot="m1reach" slot-scope="text,record">
						<span v-if="text == 0"><a  @click="toDetail(record,2)">--</a></span>
						<a v-else @click="toDetail(record,2)">{{(text *100).toString().indexOf(".") != -1 ? (text *100).toFixed(1)+'%' : (text *100)+'%'}}</a>
					</span>
					<span slot="m2reach" slot-scope="text,record">
						<span v-if="text == 0"><a  @click="toDetail(record,5)">--</a></span>
						<a v-else @click="toDetail(record,5)">{{(text *100).toString().indexOf(".") != -1 ? (text *100).toFixed(1)+'%' : (text *100)+'%'}}</a>
					</span>
					<span slot="m3reach" slot-scope="text,record">
						<span v-if="text == 0"><a  @click="toDetail(record,8)">--</a></span>
						<a v-else @click="toDetail(record,8)">{{(text *100).toString().indexOf(".") != -1 ? (text *100).toFixed(1)+'%' : (text *100)+'%'}}</a>
					</span>
				</a-table>
			</div>
			<docsModel ref="docsModel" />
			<!-- 表格 end -->
		</a-spin>
	</div>
</template>

<script>
import { getProjectStageDetailOfNotSplit } from "@/api/modular/system/jmChartManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import docsModel from "./modal/docsModel"


import _ from "lodash"

export default {
	components: {
		Treeselect,
		docsModel
	},
	data() {
		return {
			
			queryparam: {
				cates: [],
				states: [],
				depts: [],
				keyword: null,
			},
			loading: true,
			columns: [
        {
          title: "序号",
          width: 60,
          dataIndex: "no",
          align: "center",
          customRender: (text, record, index) => {
            return `${index + 1}`
          },
        },
        {
          title: "产品名称",
          dataIndex: "productProjectName",
          align: "center",
        },
        {
          title: "项目名称",
          dataIndex: "projectName",
          align: "center",
        },
        {
          title: "项目等级",
          dataIndex: "projectLevelName",
          align: "center",
        },
        {
          title: "项目阶段",
          dataIndex: "productStageName",
          align: "center",
        },
        {
          title: "K0",
          dataIndex: "k0Reach",
          align: "center",
          scopedSlots: {
            customRender: "k0reach"
          }
        },
        {
          title: "M1",
          dataIndex: "m1Reach",
          align: "center",
          scopedSlots: {
            customRender: "m1reach"
          }
        },
        {
          title: "M2",
          dataIndex: "m2Reach",
          align: "center",
          scopedSlots: {
            customRender: "m2reach"
          }
        },
        {
          title: "M3",
          dataIndex: "m3Reach",
          align: "center",
          scopedSlots: {
            customRender: "m3reach"
          }
        },
        /* {
    title: "PD",
    dataIndex: "productManagerName",
    align: "center",
  },
  {
    title: "RPM",
              align: "center",
              dataIndex: "productRPMName"
  },
          {
    title: "研究所",
              align: "center",
              dataIndex: "parentDeptName",
  } */
      ],
      loadData: [],
      totalData: [],
      cate: [],
      tablesScroll: {x: "100%", y: 500},
      tableScroll: 100
    }
  },
  props: {
    // 表格高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },

  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "80px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.stages = this.searchParam.stages
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getProjectStageDetail()
    //this.getwerklines()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  },
  methods: {
    toDetail(record,stage){
      this.$refs.docsModel.view(record.issueId,stage)
    },
    // 数据筛选
    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

			// 产品分类
			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
				)
				if (this.queryparam["cates"].indexOf(2) != -1) {
                    filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
                }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["stages"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["stages"].indexOf(parseInt(item.productStage)) > -1)
      }

			if (this.queryparam["depts"].length > 0) {
				console.log(this.queryparam["depts"])
				filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
			}

			// 产品名称
			if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
				const temList = []
				this.searchParam.inputSearch.forEach(v => {
					if (v.keyword === "") return
					filterData.forEach(e => {
						if (e.productProjectName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
							temList.push(e)
						}
					})
				})
				filterData = _.uniqWith(temList, _.isEqual)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}

			// 表格数据
			this.loadData = filterData
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}
			this.callFilter()
		},
			getProjectStageDetail() {
			this.loading = true
        getProjectStageDetailOfNotSplit({})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.callFilter()
					} else {
						this.$message.error("错误提示：" + res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
	}
}
</script>

<style lang="less" scoped>
@import "./productoption.less";
:root {
	--height: 200px;
}

/deep/.ant-table-body {
	height: var(--height) !important;
	overflow-y: scroll;
}
</style>
