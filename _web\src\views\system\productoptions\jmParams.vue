<template>
  <div class="product_width">
    <a-spin :spinning="loading">
      <!-- 筛选区域 start -->
      <!-- <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="1" :sm="24" :style="{flex:'1'}">
              <div class="table-page-search-submitButtons" :style="{margin:'5px 0',textAlign:'right'}">
                <a-button v-if="hasPerm('docs:pm')" size="small"  type="primary" @click="() => downloadParams()" >导出</a-button>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </div> -->
      <!-- 筛选区域 end -->

      <!-- 表格 start -->
      <div class="table-wrapper">
        <a-table
          ref="table"
          :rowKey="record => record.issueId + record.productChildCate"
          :columns="columns"
          :dataSource="loadData"
        >

          <template slot="productSplitName" slot-scope="text, record">
            <div v-for="(item,i) in record.productSplitName.split(';')" :key="i">
              {{ item }}
            </div>
          </template>

          <template v-slot:sizeTitle>
            <a-tooltip placement="topLeft">
              <template slot="title">
                宽、厚含蓝膜
              </template>
              <span>尺寸<a-icon class="ml4" type="question-circle" /></span>
            </a-tooltip>
          </template>

          <span slot="rollCore" slot-scope="text">
						<span v-if="text">{{ coreMap[text] }}</span>
						<span v-else>-</span>
					</span>

          <span slot="productType" slot-scope="text">
						<span v-if="text">{{ "param_product_type" | dictType(text) }}</span>
						<span v-else>-</span>
					</span>

          <div slot="pic" slot-scope="text, record" :style="{ textAlign: 'center' }">
            <!-- <a v-if="record.pic && record.pic.length > 0" target="_blank" :href="record.pic[0].url + '#navpanes=0'">查看</a> -->
            <div v-if="record.pic && record.pic.length > 0" class="a-file" @click="$refs.picshow.view(record.pic)">查看</div>
            <span v-else>-</span>
          </div>

          <a slot="docName" slot-scope="text,record" @click="previewDoc(record.docId)" :title="text">  {{ text || '-' }}</a>

          <span slot="clampText" slot-scope="text">
						<clamp v-if="text" :isCenter="true" :text="text.split('；').join('<br/>')" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :key="new Date()" />
                        <span v-else>-</span>
					</span>
        </a-table>
      </div>
      <!-- 表格 end -->
    </a-spin>
    <picshow ref="picshow" />
    <a-drawer :bodyStyle="{ height: '100%' }" width="70%" :closable="false" placement="right"  :visible="drawerVisible" @close="drawerVisible = false">
      <iframe v-if="pdfName.indexOf('pdf') !== -1" :src="pdfUrl" width="100%" height="100%"></iframe>
      <img v-else width="100%" height="100%"  :src="pdfUrl" alt="">
    </a-drawer>
    <a-drawer style="z-index: 1001;" :bodyStyle="{ height: '100%' }" width="70%" :closable="false" placement="right"  :visible="drawerVisible2" @close="drawerVisible2 = false">
      <iframe :src="pdfUrl" width="100%" height="100%"></iframe>
    </a-drawer>
  </div>
</template>

<script>
import {getProductParamsOfNotSplit, exportParmas} from "@/api/modular/system/jmChartManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import moment from 'moment'
import picshow from "../projects/modal/picshow"
import {clamp} from '@/components'
import Vue from "vue";
export default {
  components: {
    Treeselect,
    clamp,
    picshow
  },
  data() {
    return {
      pdfUrl:'',
      pdfName:'',
      drawerVisible:false,
      drawerVisible2:false,
      coreMap: {
        1: "卷绕",
        2: "叠片"
      },
      queryparam: {
        cates: [],
        states: [],
        depts:[],
        keyword: null,
      },
      loading: true,
      columns: [
        {
          title: "序号",
          width: 60,
          dataIndex: "no",
          align: "center",
          customRender: (text, record, index) => {
            if (record.productOrProject == 1 && !record.stage) {
              return `${index + 1}`
            }
            return ''
          },
        },
        {
          title: "细分市场",
          width: 150,
          align: "center",
          dataIndex: "productSplitName",
          scopedSlots: {
            customRender: 'productSplitName'
          }
        },
        {
          title: "产品名称",
          width: 150,
          align: "center",
          dataIndex: "productProjectName",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },
        {
          title: "化学体系",
          width: 100,
          align: "center",
          dataIndex: "chemicalSystem",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },
        {
          title: "E/D(Wh/kg)",
          width: 100,
          dataIndex: "density",
          align: "center",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },

        {
          title: "倍率(C)",
          width: 160,
          align: "center",
          dataIndex: "capacity",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },
        {
          title: "标称容量(Ah)",
          width: 95,
          align: "center",
          dataIndex: "ah",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },
        {
          title: "标称电压(V)",
          width: 95,
          align: "center",
          dataIndex: "voltage",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },

        {
          title: "ACR(mΩ)≤",
          width: 100,
          align: "center",
          dataIndex: "acr",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },

        {
          title: "DCR(mΩ)≤",
          width: 100,
          align: "center",
          dataIndex: "dcr",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },

        },

        {
          title: "循环(cycle)",
          children: [
            {
              title: "常规",
              width: 200,
              align: "center",
              dataIndex: "normal",
              scopedSlots: { customRender: 'clampText' }

            },
            {
              title: "快充",
              width: 200,
              align: "center",
              dataIndex: "rapid",
              scopedSlots: { customRender: 'clampText' }
            },
          ]
        },
        {
          scopedSlots: { title: 'sizeTitle' }, // 指定作用域插槽的名称
          children: [
            {
              title: "宽(mm)",
              width: 80,
              align: "center",
              dataIndex: "blueFilmWidth",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },

            },
            /* {
                title: "公差(mm)",
                width: 80,
                align: "center",
                dataIndex: "widthErrand",

            }, */
            {
              title: "厚(mm)",
              width: 80,
              align: "center",
              dataIndex: "blueFilmThickness",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },

            },
            /* {
                title: "公差(mm)",
                width: 80,
                align: "center",
                dataIndex: "thicknessErrand",

            }, */
            {
              title: "肩高(mm)",
              width: 80,
              align: "center",
              dataIndex: "nonePolarHeight",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },

            },
            /*  {
                 title: "公差(mm)",
                 width: 80,
                 align: "center",
                 dataIndex: "heightErrand",

             }, */
            {
              title: "总高(mm)",
              width: 80,
              align: "center",
              dataIndex: "totalHeight",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },
            },
            /*{
              title: "公差(mm)",
              width: 80,
              align: "center",
              dataIndex: "totalheightErrand",
            }, */
            {
              title: "直径(mm)",
              width: 80,
              align: "center",
              dataIndex: "diameter",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },

            },
            /* {
                title: "公差(mm)",
                width: 80,
                align: "center",
                dataIndex: "diameterErrand",

            }, */
          ],
        },
        {
          title: "重量(g)",
          width: 80,
          align: "center",
          dataIndex: "weight",
          customRender: (text, record, index) => {
            return text ? text : '-'
          },
        },
        {
          title: '工艺参数',
          children: [
            {
              title: "封装形式",
              width: 120,
              align: "center",
              dataIndex: "productType",
              scopedSlots: {
                customRender: "productType"
              },
            },
            {
              title: "卷芯工艺",
              width: 140,
              align: "center",
              dataIndex: "rollCore",
              scopedSlots: {
                customRender: "rollCore"
              },
            },
            {
              width: 110,
              title: "卷芯出极耳方式",
              align: "center",
              dataIndex: "earbud",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },

            },
            {
              width: 100,
              title: "电芯端子方式",
              align: "center",
              dataIndex: "terminal",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },
            },
            {
              width: 100,
              title: "芯包数量",
              align: "center",
              dataIndex: "bareWireCount",
              customRender: (text, record, index) => {
                return text ? text : '-'
              },
            },
          ]
        },
        {
          width: 100,
          title: "产品图片",
          dataIndex: "pic",
          align: "center",
          scopedSlots: {
            customRender: "pic"
          }
        },
        {
          width: 100,
          title: "产品规格书",
          dataIndex: "docName",
          align: "center",
          scopedSlots: {
            customRender: "docName"
          }
        }
      ],
      loadData: [],
      totalData: [],
    }
  },
  props: {
    // 表格高度
    tableHeight: {
      type: Number,
      default: 0
    },
    // 表格滚动高度
    scrollHeigh: {
      type: Number,
      default: 0
    },
    searchParam: {
      type: Object,
      default: {}
    },
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
  },
  watch: {
    loadData(newVal, oldVal) {
      if (this.loadData.length > 0) {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
      } else {
        // 动态修改--height的值
        document.documentElement.style.setProperty(`--height`, "60px")
      }
    },
    searchParam: {
      handler(newName, oldName) {
        this.queryparam.cates = this.searchParam.cates
        this.queryparam.states = this.searchParam.states
        this.queryparam.depts = this.searchParam.depts
        this.queryparam.inputSearch = this.searchParam.inputSearch
        // 立项日期筛选框
        this.dateChange("", this.searchParam.dateString)
        this.callFilter()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getProductParams()
    //this.callGetDepartmentCateTree()
    // 动态修改--height的值
    document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
  },
  methods: {
    moment,
    previewPdf(file){
      this.pdfUrl = file.url
      this.pdfName = file.name

      if(['.xbm','.tif','.pjp','.svgz','.jpg','.jpeg','.ico','.tiff','.gif','.svg','.jfif','.webp','.png','.bmp','.pjpeg','.avif','.pdf'].some(someItem => { return typeof file.name === 'string' && file.name.indexOf(someItem) !== -1})){
        this.drawerVisible = true
      }else{
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = file.url
        a.download =file.name
        a.click()
      }
    },
    previewDoc(fileId){
      if(fileId){
        this.pdfUrl = '/api/sysFileInfo/previewMonitor?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + fileId + '#toolbar=0'
        this.drawerVisible2 = true
      }
    },
    dateChange(date, dateString) {
      if (dateString[0] != null && dateString[0] != "") {
        this.queryparam.startDate = Date.parse(dateString[0])
      } else {
        this.queryparam.startDate = null
      }
      if (dateString[1] != null && dateString[1] != "") {
        this.queryparam.endDate = Date.parse(dateString[1])
      } else {
        this.queryparam.endDate = null
      }
      this.callFilter()
    },
    downloadParams(){
      let _params = {
        projectIds:this.loadData.map(item=>item.issueId)
      }
      exportParmas(_params).then(res => {
        const fileName = `产品参数.xlsx`;
        const _res = res.data;
        let blob = new Blob([_res]);
        let downloadElement = document.createElement("a");
        //创建下载的链接
        let href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        //下载后文件名
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        //点击下载
        downloadElement.click();
        //下载完成移除元素
        document.body.removeChild(downloadElement);
        //释放掉blob对象
        window.URL.revokeObjectURL(href);
      })
    },


    callFilter() {
      // 全部数据
      let filterData = JSON.parse(JSON.stringify(this.totalData))

      // 产品分类
      if (this.queryparam["cates"].length > 0) {
        filterData = filterData.filter(
          item => this.queryparam['cates'].some(el => item.cateIds.includes(el+''))
        )
        if (this.queryparam["cates"].indexOf(2) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
          filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
      }

      if (this.queryparam["states"].length > 0) {
        filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.productState)) > -1)
      }

      if (this.queryparam["depts"].length > 0) {
        console.log(this.queryparam["depts"])
        filterData = filterData.filter(item => this.queryparam["depts"].indexOf((item.parentDept)) > -1)
      }

      // 产品名称
      if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
        const temList = []
        this.searchParam.inputSearch.forEach(v => {
          if (v.keyword === "") return
          filterData.forEach(e => {
            if (e.productProjectName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
              temList.push(e)
            }
          })
        })
        filterData = _.uniqWith(temList, _.isEqual)
      }

      if (this.queryparam.startDate != null) {
        filterData = filterData.filter(
          item =>
            Date.parse(item.initiationDate) >= this.queryparam.startDate &&
            Date.parse(item.initiationDate) < this.queryparam.endDate
        )
      }

      // 表格数据
      this.loadData = filterData
    },

    getProductParams() {
      this.loading = true
      getProductParamsOfNotSplit({})
        .then(res => {
          console.log(res);
          if (res.success) {
            this.totalData = JSON.parse(JSON.stringify(res.data))
            this.callFilter()
          } else {
            this.$message.error(res.message, 1)
          }

        }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "./productoption.less";

:root {
  --height: 600px;
}

.tips {
  color: #1890ff;
  margin-left: 4px;
  cursor: pointer;
}

/deep/.ant-table-body {
  height: var(--height) !important;
  overflow-y: scroll;
}
/deep/ .ant-table-thead > tr > th{
  border: 1px solid #cccccc4f;
  padding: 2px;
}

/* 固定列 */
/deep/ .ant-table tr td {
  background: #fff;
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(1),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(1){
  position: sticky;
  left: 0;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(2),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(2){
  position: sticky;
  left: 45px;
  z-index: 10;
}
/deep/ .table-wrapper .ant-table-thead tr:nth-child(1) th:nth-child(3),
/deep/ .table-wrapper .ant-table-tbody tr td:nth-child(3){
  position: sticky;
  left: calc(45px + 150px);
  z-index: 10;
}
/deep/.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 4px;
}

.ml4 {
  margin-left: 4px;
}
</style>
