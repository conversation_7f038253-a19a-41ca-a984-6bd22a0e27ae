package eve.sys.modular.product.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import com.qcloud.cos.utils.DateUtils;
import eve.sys.modular.product.param.ProjectDetail;
import eve.sys.modular.weekprocess.entity.WeekProcess;
import eve.sys.modular.weekprocess.entity.WeekProcessDetail;
import eve.sys.modular.weekprocess.service.IWeekProcessDetailService;
import eve.sys.modular.weekprocess.service.impl.WeekProcessDetailServiceImpl;
import net.bytebuddy.implementation.bytecode.Throw;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.param.ProductCateOptionBean;
import eve.sys.modular.product.param.ProductProjectItem;
import eve.sys.modular.product.param.request.ProductQueryParams;
import eve.sys.modular.product.param.response.ProductParams;
import eve.sys.modular.product.param.response.VproductParams;
import eve.sys.modular.product.param.response.WeekProcessParam;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.productparam.entity.SysProductParam;
import eve.sys.modular.productparam.service.ISysProductParamService;
import eve.sys.modular.techdoc.entity.TechDoc;
import eve.sys.modular.techdoc.service.ITechDocService;
//import eve.sys.modular.weekprocess.entity.WeekProcess;
import eve.sys.modular.weekprocess.service.IWeekProcessService;
import org.springframework.util.ObjectUtils;

@Service
public class ProductDashService {

    //    @Resource
//    private IWeekProcessService WeekProcessService;
    @Resource
    private IWeekProcessDetailService iWeekProcessDetailService;

    @Resource
    private ISysProductParamService productParamService;

    @Resource
    private IProductManagerService productManagerService;

    @Resource
    private ISysBomService sysBomService;

    @Resource
    private ITechDocService docService;


    public List<ProductProjectItem> getProducts(Long isVcylinder, Long isJMArea) {

        List<ProductProjectItem> projectItems = new ArrayList<>();

        List<ProductProjectItem> productProjectItems = new ArrayList<>();

        if (null != isVcylinder && isVcylinder.equals(1L)) {
            productProjectItems = productManagerService.getProducts(true, false);
        } else if (null != isJMArea && isJMArea.equals(1L)) {
            productProjectItems = productManagerService.getProducts(false, true);
        } else {
            productProjectItems = productManagerService.getProducts(false, false);
        }

        Map<Integer,String> stateTxtMap = new HashMap<Integer,String>(){{
            put(1, "立项讨论");
            put(2,"A样");
            put(3,"B样");
            put(4,"C样");
            put(5,"D样");
            put(6,"SOP");
            put(7,"暂停开发");
            put(8,"停产");
        }};

        for (ProductProjectItem e : productProjectItems) {
            for (ProductCateOptionBean _e : e.getProductCateOptionBeans()) {
                String[] split = _e.getValue().split("->");
                Long deptId = e.getDepartmentOptionList().stream().filter($e->$e.getPid().equals(0L)).map(ProductCateOptionBean::getId).findFirst().orElse(0L);
                ProductProjectItem _item = ProductProjectItem.builder()
                        .issueId(e.getIssueId())
                        .catepid(_e.getPid())
                        .deptId(deptId)
                        .departmentOptionList(e.getDepartmentOptionList())
                        .cateId(_e.getId())
                        .productClassification(e.getProductClassification())
                        .initiationDate(e.getInitiationDate())
                        .issueKey(e.getIssueKey())
                        .productProjectName(e.getProductProjectName())
                        .projectName(e.getProjectName())
                        .productOrProject(e.getProductOrProject())
                        .productManager(e.getProductManager())
                        .customer(e.getCustomer())
                        .fixedState(e.getFixedState())
                        .plannedFixedDate(e.getPlannedFixedDate())
                        .mStatus(e.getMStatus())
                        .ostate(e.getState())
                        .state(e.getState())
                        .stateTxt(stateTxtMap.getOrDefault(e.getState(), ""))
                        .productState(e.getProductState())
                        .productLevel(e.getProductLevel())
                        .researchProjectManager(e.getResearchProjectManager())
                        .showPlan(false)
                        .productTechMajordomoName(e.getProductTechMajordomoName())
                        .productCateOptionBeans(e.getProductCateOptionBeans())
                        .productStageItems(e.getProductStageItems())
                        .cateIdKey(_e.getId() + "")
                        .catePidKey(_e.getPid().equals(0L) ? _e.getId() + "" : _e.getPid() + "")
                        .productDistribute(e.getProductDistribute())
                        .vCylinderProductDepartment(e.getVCylinderProductDepartment())
                        .vCylinderProductDepartmentName(e.getVCylinderProductDepartmentName())
                        .build();

                _item = split.length > 1 ? _item.toBuilder().productCateParent(split[0]).productCate(split[1]).build()
                : _item.toBuilder().productCateParent(split[0]).productCate("").build();

                projectItems.add(_item);
            }
        }

        return projectItems;
    }



    public List<ProductProjectItem> getInfos(ProductQueryParams param) {

        List<ProductProjectItem> projectItems = getProducts(param.getIsVcylinder(), param.getIsJMArea());

        if (projectItems.size() == 0) {
            return Collections.emptyList();
        }

        List<ProductProjectItem> projects = projectItems.stream()
        .filter(e->e.getProductOrProject().equals(1L))
        .sorted(Comparator.comparing(ProductProjectItem::getProductCateParent).thenComparing(ProductProjectItem::getProductProjectName)).collect(Collectors.toList());

        List<Long> issueIds = projects.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        LambdaQueryWrapper<SysBom> bomLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bomLambdaQueryWrapper.in(SysBom::getBomIssueId, issueIds).in(SysBom::getBomType, Arrays.asList(0, 2,3));
        List<SysBom> boms = null == issueIds || issueIds.size() == 0 ? new ArrayList<>() : sysBomService.list(bomLambdaQueryWrapper);

        List<Long> bomIds = boms.stream().map(SysBom::getId).collect(Collectors.toList());
        LambdaQueryWrapper<TechDoc> docLambdaQueryWrapper = new LambdaQueryWrapper<>();
        docLambdaQueryWrapper.in(TechDoc::getBomId, bomIds).eq(TechDoc::getTechType, 0).eq(TechDoc::getTechStatus, 2);
        List<TechDoc> docs = null == bomIds || bomIds.size() == 0 ? new ArrayList<>() : docService.list(docLambdaQueryWrapper);

        LambdaQueryWrapper<WeekProcessDetail> weekQuery = new LambdaQueryWrapper<>();
        weekQuery.in(WeekProcessDetail::getIssueId, issueIds)
                .orderByAsc(WeekProcessDetail::getDimensions);
        List<WeekProcessDetail> weekProcesses = iWeekProcessDetailService.list(weekQuery);

        int weekTime = 0;
        if(ObjectUtils.isEmpty(param.getWeekTime())){
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(new Date());
            int weekNum = calendar.get(Calendar.WEEK_OF_YEAR);
            weekTime = ObjectUtils.isEmpty(param.getWeekTime()) ? weekNum : param.getWeekTime();
        }else {
            weekTime = param.getWeekTime();
        }

        for (ProductProjectItem item : projects) {
            List<ProductProjectItem> childrens = projectItems.stream().filter(e -> !e.getProductOrProject().equals(1L) && e.getProductProjectName().equals(item.getProductProjectName())).collect(Collectors.toList());

            List<Long> _bomIds = boms.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId())).map(SysBom::getId).collect(Collectors.toList());
            item.setBomCount(boms.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId())).count());
            item.setMiCount(docs.stream().filter(e -> _bomIds.indexOf(e.getBomId()) > -1).count());

            item.setWeekProcessDetail(getNewWp(weekProcesses, weekTime, item.getIssueId()));

            item.setChildren(childrens.size() > 0 ? childrens : null);
        }
        return projects;
    }

    public List<ProductProjectItem> getProcess(ProductQueryParams param){
        List<ProductProjectItem> projectItemss = this.getInfos(param);

        List<ProductProjectItem> productProjectItems = new ArrayList<>();

        for (ProductProjectItem e : projectItemss) {
            ProductProjectItem item = new ProductProjectItem();
            item.setIssueId(e.getIssueId());
            item.setChildren(e.getChildren());
            item.setProductOrProject(e.getProductOrProject());
            item.setProductProjectName(e.getProductProjectName());
            item.setProjectName(e.getProjectName());
            item.setCateId(e.getCateId());
            item.setProductClassification(e.getProductClassification());
            item.setCatepid(e.getCatepid());
            item.setProductLevel(e.getProductLevel());
            item.setMStatus(e.getMStatus());
            item.setCustomer(e.getCustomer());
            item.setInitiationDate(e.getInitiationDate());
            item.setProductCate(e.getProductCate());
            item.setProductCateParent(e.getProductCateParent());
            item.setState(e.getState());
            item.setWeekProcess(e.getWeekProcess());
            item.setDeptId(e.getDeptId());
            item.setProductDistribute(e.getProductDistribute());
            if(e.getWeekProcessDetail().size() > 0){
                item.setWeekProcessDetail(e.getWeekProcessDetail());
            }
            productProjectItems.add(item);
        }
        return productProjectItems;
    }


    public List<ProductProjectItem> getProcess2(ProductQueryParams param){
        List<ProductProjectItem> projectItemss = this.getInfos2(param);

        List<ProductProjectItem> productProjectItems = new ArrayList<>();

        for (ProductProjectItem e : projectItemss) {
            ProductProjectItem item = new ProductProjectItem();
            item.setIssueId(e.getIssueId());
            item.setChildren(e.getChildren());
            item.setProductOrProject(e.getProductOrProject());
            item.setProductProjectName(e.getProductProjectName());
            item.setProjectName(e.getProjectName());
            item.setCateId(e.getCateId());
            item.setProductClassification(e.getProductClassification());
            item.setCatepid(e.getCatepid());
            item.setProductLevel(e.getProductLevel());
            item.setMStatus(e.getMStatus());
            item.setCustomer(e.getCustomer());
            item.setInitiationDate(e.getInitiationDate());
            item.setProductCate(e.getProductCate());
            item.setProductCateParent(e.getProductCateParent());
            item.setState(e.getState());
            item.setWeekProcess(e.getWeekProcess());
            item.setDeptId(e.getDeptId());
            item.setProductDistribute(e.getProductDistribute());
            item.setProductState(e.getProductState());
            item.setWeekProcessDetail(e.getWeekProcessDetail());
            productProjectItems.add(item);
        }
        return productProjectItems;
    }

    /**
     * 修改成周范围查询
     */
    public List<ProductProjectItem> getInfos2(ProductQueryParams param) {

        List<ProductProjectItem> projectItems = getProducts(param.getIsVcylinder(), param.getIsJMArea());

        if (projectItems.size() == 0) {
            return Collections.emptyList();
        }

        List<ProductProjectItem> projects = projectItems.stream()
                .filter(e->e.getProductOrProject().equals(1L))
                .sorted(Comparator.comparing(ProductProjectItem::getProductCateParent).thenComparing(ProductProjectItem::getProductProjectName)).collect(Collectors.toList());

        List<Long> issueIds = projects.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        LambdaQueryWrapper<SysBom> bomLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bomLambdaQueryWrapper.in(SysBom::getBomIssueId, issueIds).in(SysBom::getBomType, Arrays.asList(0, 2,3));
        List<SysBom> boms = null == issueIds || issueIds.size() == 0 ? new ArrayList<>() : sysBomService.list(bomLambdaQueryWrapper);

        List<Long> bomIds = boms.stream().map(SysBom::getId).collect(Collectors.toList());
        LambdaQueryWrapper<TechDoc> docLambdaQueryWrapper = new LambdaQueryWrapper<>();
        docLambdaQueryWrapper.in(TechDoc::getBomId, bomIds).eq(TechDoc::getTechType, 0).eq(TechDoc::getTechStatus, 2);
        List<TechDoc> docs = null == bomIds || bomIds.size() == 0 ? new ArrayList<>() : docService.list(docLambdaQueryWrapper);


        List<Integer> issueIdList = issueIds.stream().map(Long::intValue).collect(Collectors.toList());
        List<WeekProcessDetail> weekProcesses = iWeekProcessDetailService.queryByWeek(param.getWeekTimeLists(),issueIdList,0);

        for (ProductProjectItem item : projects) {
            List<ProductProjectItem> childrens = projectItems.stream().filter(e -> !e.getProductOrProject().equals(1L) && e.getProductProjectName().equals(item.getProductProjectName())).collect(Collectors.toList());

            List<Long> _bomIds = boms.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId())).map(SysBom::getId).collect(Collectors.toList());
            item.setBomCount(boms.stream().filter(e -> e.getBomIssueId().equals(item.getIssueId())).count());
            item.setMiCount(docs.stream().filter(e -> _bomIds.indexOf(e.getBomId()) > -1).count());
            List<WeekProcessDetail> details = weekProcesses.stream().filter(e -> item.getIssueId().equals(e.getIssueId())).collect(Collectors.toList());
            item.setWeekProcessDetail(details);

            item.setChildren(childrens.size() > 0 ? childrens : null);
        }
        return projects;
    }

    public JSONArray getProductParams( ProductQueryParams param) {
        List<ProductProjectItem> projectItems = this.getInfos(param);
        List<Long> issueIds = projectItems.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        List<SysProductParam> productParams = productParamService.getParamsByIssueIds(issueIds);

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {{
            put(1, 2L);
            put(2, 2L);
            put(3, 3L);
            put(4, 4L);

            put(5, 4L);
            put(6, 4L);
            put(7, 4L);
            put(8, 4L);
        }};

        JSONArray jsonArray = new JSONArray();

        for (ProductProjectItem e : projectItems) {

            Optional<SysProductParam> optionProductParam = productParams.stream().filter(_e->e.getIssueId().equals(_e.getIssueId()) && _e.getStage().equals(stateMap.getOrDefault(e.getOstate(), 0L))).findFirst();
            JSONObject obj = optionProductParam.isPresent() ? (JSONObject)JSONObject.parse(optionProductParam.get().getParams()) : new JSONObject();
            obj.put("issueId", e.getIssueId());
            obj.put("productOrProject",e.getProductOrProject());
            obj.put("productProjectName",e.getProductProjectName());
            obj.put("projectName", e.getProjectName());
            obj.put("cateId", e.getCateId());
            obj.put("productClassification", e.getProductClassification());
            obj.put("productDistribute", e.getProductDistribute());
            obj.put("catepid",e.getCatepid());
            obj.put("productLevel",e.getProductLevel());
            obj.put("mStatus",e.getMStatus());
            obj.put("deptId", e.getDeptId());
            obj.put("customer",e.getCustomer());
            obj.put("initiationDate",e.getInitiationDate());
            obj.put("productCate",e.getProductCate());
            obj.put("productCateParent",e.getProductCateParent());
            obj.put("state",e.getState());

            jsonArray.add(obj);
        }

        return jsonArray;
    }


    public JSONArray getProductDocs(ProductQueryParams param) {
        List<ProductProjectItem> projectItems = this.getInfos( param);
        List<Long> issueIds = projectItems.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        List<SysProductParam> productParams = productParamService.getParamsByIssueIds(issueIds);

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {{
            put(1, 2L);
            put(2, 2L);
            put(3, 3L);
            put(4, 4L);

            put(5, 4L);
            put(6, 4L);
            put(7, 4L);
            put(8, 4L);
        }};

        JSONArray jsonArray = new JSONArray();

        for (ProductProjectItem e : projectItems) {

           
            String params = productParams.stream()
            .filter(_e-> null != _e.getStage())
            .filter(_e->e.getIssueId().equals(_e.getIssueId()))
            .filter(_e->_e.getStage().equals(stateMap.getOrDefault(e.getOstate(), 0L)))
            .findFirst()
            .map(SysProductParam::getParams)
            .orElse("");
            
            
            if (!params.isEmpty() && '[' ==  params.charAt(0)) {
                StringBuilder sb = new StringBuilder(params);
                sb.deleteCharAt(0);
                sb.deleteCharAt(sb.length() - 1);
                params = sb.toString();
            }
            
            JSONObject obj = !params.isEmpty() ? (JSONObject)JSONObject.parse(params) : new JSONObject();
            
            obj.put("issueId", e.getIssueId());
            obj.put("productOrProject",e.getProductOrProject());
            obj.put("productProjectName",e.getProductProjectName());
            obj.put("projectName", e.getProjectName());
            obj.put("cateId", e.getCateId());
            obj.put("productClassification", e.getProductClassification());
            obj.put("catepid",e.getCatepid());
            obj.put("productLevel",e.getProductLevel());
            obj.put("mStatus",e.getMStatus());
            obj.put("customer",e.getCustomer());
            obj.put("initiationDate",e.getInitiationDate());
            obj.put("productCate",e.getProductCate());
            obj.put("productManager",e.getProductManager());
            obj.put("productCateParent",e.getProductCateParent());
            obj.put("productDistribute",e.getProductDistribute());
            obj.put("state",e.getState());
            obj.put("deptId", e.getDeptId());
            obj.put("stateTxt",e.getStateTxt());
            jsonArray.add(obj);
        }
        return jsonArray;

    }

    public void exportParmas(HttpServletResponse response, ProductQueryParams param){

        List<ProductProjectItem> projectItems = this.getInfos( param);
        if (!ObjectUtils.isEmpty(param)) {

            if(!ObjectUtils.isEmpty(param.getProductClassification())){
                projectItems = projectItems
                .stream()
                .filter(e->param.getProductClassification().indexOf(e.getProductClassification()) > -1)
                .collect(Collectors.toList());
            }


            if (!ObjectUtils.isEmpty(param.getCates())) {
                projectItems = projectItems
                .stream()
                .filter(
                        e -> param.getCates().indexOf(e.getCateId()) > -1
                                || param.getCates().indexOf(e.getCatepid()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getDepts())) {
                projectItems = projectItems
                .stream()
                .filter(e->param.getDepts().indexOf(e.getDeptId()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getStartDate())) {
                projectItems = projectItems
                .stream()
                .filter(
                    e -> e.getInitiationDate().compareTo(param.getStartDate()) >= 0
                    && e.getInitiationDate().compareTo(param.getEndDate()) <= 0)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getProjectId())) {
                projectItems = projectItems
                .stream()
                .filter(e -> e.getIssueId().equals(param.getProjectId()))
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getKeyword())) {
                projectItems = projectItems
                .stream()
                .filter(
                    e-> e.getProjectName().toLowerCase().contains(param.getKeyword().trim().toLowerCase())
                )
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getStates())) {
                projectItems = projectItems
                .stream()
                .filter(e->param.getStates().indexOf(e.getState()) > -1)
                .collect(Collectors.toList());
            }

        }

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {{
            put(1, 2L);
            put(2, 2L);
            put(3, 3L);
            put(4, 4L);

            put(5, 4L);
            put(6, 4L);
            put(7, 4L);
            put(8, 4L);
        }};

        List<Long> issueIds = projectItems.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        List<SysProductParam> productParams = productParamService.getParamsByIssueIds(issueIds);
        List<ProductParams> params = new ArrayList<>();

        for (ProductProjectItem e : projectItems) {

            Optional<SysProductParam> optionProductParam = productParams
            .stream()
            .filter(
                    _e -> e.getIssueId().equals(_e.getIssueId())
                            && _e.getStage().equals(stateMap.getOrDefault(e.getOstate(), 0L))
            )
            .findFirst();

            JSONObject obj = optionProductParam.isPresent() ? (JSONObject)JSONObject.parse(optionProductParam.get().getParams()) : new JSONObject();

            params.add(
                ProductParams.builder()
                .productProjectName(e.getProductProjectName())
                /* .acr((String)obj.getOrDefault("acr", ""))
                .ah((String)obj.getOrDefault("ah", ""))
                .bareWireCount((String)obj.getOrDefault("bareWireCount", ""))
                .blueFilmThickness((String)obj.getOrDefault("blueFilmThickness", ""))
                .blueFilmWidth((String)obj.getOrDefault("blueFilmWidth", ""))
                .capacity((String)obj.getOrDefault("capacity", ""))
                .dcr((String)obj.getOrDefault("dcr", ""))
                .density((String)obj.getOrDefault("density", ""))
                .diameter((String)obj.getOrDefault("diameter", ""))
                .earbud((String)obj.getOrDefault("earbud", ""))
                .internalStructure((String)obj.getOrDefault("internalStructure", ""))
                .loop((String)obj.getOrDefault("loop", ""))
                .noneBlueFilmThickness((String)obj.getOrDefault("noneBlueFilmThickness", ""))
                .noneBlueFilmWidth((String)obj.getOrDefault("noneBlueFilmWidth", ""))
                .nonePolarHeight((String)obj.getOrDefault("nonePolarHeight", ""))
                .performance((String)obj.getOrDefault("performance", ""))
                .polarHeight((String)obj.getOrDefault("polarHeight", ""))
                .productType((Integer)obj.getOrDefault("productType", 0))
                .rollCore((Integer)obj.getOrDefault("rollCore", 0))
                .terminal((String)obj.getOrDefault("terminal", ""))
                .voltage((String)obj.getOrDefault("voltage", ""))
                .weight((String)obj.getOrDefault("weight", "")) */
                .build()
            );

        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
            .encode("产品参数", "UTF-8")
            .replaceAll("\\+", "%20");
            response
            .setHeader(
                    "Content-disposition",
                    "attachment;filename*=utf-8''" + fileName + ".xlsx"
            );
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
            .write(response.getOutputStream(), ProductParams.class)
            .sheet("产品参数")
            .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportVparmas(HttpServletResponse response, ProductQueryParams param){

        List<ProductProjectItem> projectItems = this.getInfos(param);
        if (!ObjectUtils.isEmpty(param)) {

            if(!ObjectUtils.isEmpty(param.getProductClassification())){
                projectItems = projectItems
                .stream()
                .filter(e->param.getProductClassification().indexOf(e.getProductClassification()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getCates())) {
                projectItems = projectItems
                .stream()
                .filter(
                        e -> param.getCates().indexOf(e.getCateId()) > -1
                                || param.getCates().indexOf(e.getCatepid()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getDepts())) {
                projectItems = projectItems
                .stream()
                .filter(e->param.getDepts().indexOf(e.getDeptId()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getStartDate())) {
                projectItems = projectItems
                .stream()
                .filter(
                    e -> e.getInitiationDate().compareTo(param.getStartDate()) >= 0
                    && e.getInitiationDate().compareTo(param.getEndDate()) <= 0)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getProjectId())) {
                projectItems = projectItems
                .stream()
                .filter(e -> e.getIssueId().equals(param.getProjectId()))
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getKeyword())) {
                projectItems = projectItems
                .stream()
                .filter(
                    e-> e.getProjectName().toLowerCase().contains(param.getKeyword().trim().toLowerCase())
                )
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getStates())) {
                projectItems = projectItems
                .stream()
                .filter(e->param.getStates().indexOf(e.getState()) > -1)
                .collect(Collectors.toList());
            }

            if (!ObjectUtils.isEmpty(param.getProductDistribute())) {
                projectItems = projectItems
                .stream()
                .filter(e->param.getProductDistribute().indexOf(e.getProductDistribute()) > -1)
                .collect(Collectors.toList());
            }

        }

        // A样 B样 C样
        Map<Integer, Long> stateMap = new HashMap<Integer, Long>() {{
            put(1, 2L);
            put(2, 2L);
            put(3, 3L);
            put(4, 4L);

            put(5, 4L);
            put(6, 4L);
            put(7, 4L);
            put(8, 4L);
        }};

        List<Long> issueIds = projectItems.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        List<SysProductParam> productParams = productParamService.getParamsByIssueIds(issueIds);
        List<VproductParams> params = new ArrayList<>();

        for (ProductProjectItem e : projectItems) {

            Optional<SysProductParam> optionProductParam = productParams
            .stream()
            .filter(
                    _e -> e.getIssueId().equals(_e.getIssueId())
                            && _e.getStage().equals(stateMap.getOrDefault(e.getOstate(), 0L))
            )
            .findFirst();

            JSONObject obj = optionProductParam.isPresent() ? (JSONObject)JSONObject.parse(optionProductParam.get().getParams()) : new JSONObject();

            BigDecimal density = null;

            if (null != obj.get("energy") && null != obj.get("weight")) {
                BigDecimal energy = BigDecimal.valueOf(Double.valueOf(obj.getString("energy").replaceAll("[^0-9.]", "")));
                BigDecimal weight = BigDecimal.valueOf(Double.valueOf(obj.getString("weight").replaceAll("[^0-9.]", "")));
                BigDecimal temp = weight.multiply(BigDecimal.valueOf(0.001));
                density = energy.divide(temp, 1, RoundingMode.HALF_UP);
            }
            params.add(
                VproductParams.builder()
                .productProjectName(e.getProductProjectName())
                .size((String)obj.getOrDefault("diameter", "")+"*"+(String)obj.getOrDefault("height", ""))
                .weight((String)obj.getOrDefault("weight", ""))
                .polarEar((Integer)obj.getOrDefault("polarEar", 0))
                .ah((String)obj.getOrDefault("ah", ""))
                .voltage((String)obj.getOrDefault("voltage", ""))
                .energy((String)obj.getOrDefault("energy", ""))
                .density(null != density ? density.toString(): "")
                .standardChargingCurrent((String)obj.getOrDefault("standardChargingCurrent", ""))
                .fastChargingCurrent((String)obj.getOrDefault("fastChargingCurrent", ""))
                .standardDischargeCurrent((String)obj.getOrDefault("standardDischargeCurrent", ""))
                .maxContinuousDischargeCurrent((String)obj.getOrDefault("maxContinuousDischargeCurrent", ""))
                .acr((String)obj.getOrDefault("acr", ""))
                .dcr((String)obj.getOrDefault("dcr", ""))
                .loop((String)obj.getOrDefault("loop", ""))
                .build()
            );

        }

        if (!ObjectUtils.isEmpty(param) && !ObjectUtils.isEmpty(param.getEars())) {

            params = params
                .stream()
                .filter(e->param.getEars().indexOf(e.getPolarEar()) > -1)
                .collect(Collectors.toList());
         }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
            .encode("产品参数", "UTF-8")
            .replaceAll("\\+", "%20");
            response
            .setHeader(
                    "Content-disposition",
                    "attachment;filename*=utf-8''" + fileName + ".xlsx"
            );
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
            .write(response.getOutputStream(), VproductParams.class)
            .sheet("产品参数")
            .doWrite(params);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportProcess(HttpServletResponse response, ProductQueryParams param) throws IOException {
        //组装issueIds
        List<Integer> issueIds = param.getIssueIds().stream()
                .map(Long::intValue)
                .collect(Collectors.toList());


        //组装projectDetailList
        List<ProjectDetail> projectDetailList = new ArrayList<>();
        List<ProductProjectItem> projectItemss = this.getInfos2(param);
        for (ProductProjectItem pp:projectItemss){
            ProjectDetail detail = new ProjectDetail();
            projectDetailList.add(detail);

            detail.setIssueId(pp.getIssueId());
            detail.setCustomer(pp.getCustomer());
            detail.setProductRPMName(pp.getResearchProjectManager());
            detail.setProductManagerName(pp.getProductManager());
            detail.setProductProjectName(pp.getProductProjectName());
            detail.setProductStageItems(pp.getProductStageItems());
        }

        iWeekProcessDetailService.export(param.getWeekTimeLists(),issueIds,projectDetailList,response);
    }

    //获取周数最新的WeekProcessDetail数据
    public List<WeekProcessDetail> getNewWp(List<WeekProcessDetail> details, Integer weekTime, Long issueIds) {
        List<WeekProcessDetail> result = new ArrayList<>();

        if(ObjectUtils.isEmpty(details)){
            return result;
        }

        List<WeekProcessDetail> collect = details.stream().filter(_e -> issueIds.equals(_e.getIssueId()))
                .filter(_e -> _e.getWeekTime().equals(weekTime))
                .collect(Collectors.toList());
        Map<String, List<WeekProcessDetail>> listMap = collect.stream().collect(Collectors.groupingBy(WeekProcessDetail::getDimensions));
        for(Map.Entry<String, List<WeekProcessDetail>> ent:listMap.entrySet()){
            List<WeekProcessDetail> entValue = ent.getValue();
            Optional<WeekProcessDetail> first = entValue.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).findFirst();
            result.add(first.get());
        }
        return result;
    }

}
