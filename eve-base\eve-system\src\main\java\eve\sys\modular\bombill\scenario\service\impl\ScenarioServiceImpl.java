package eve.sys.modular.bombill.scenario.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;
import eve.sys.modular.bombill.bomcostoverview.service.IBomCostOverviewService;
import eve.sys.modular.bombill.chemicalelementprice.entity.ChemicalElementPrice;
import eve.sys.modular.bombill.chemicalelementprice.service.IChemicalElementPriceService;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import eve.sys.modular.bombill.chemicalelement.service.IChemicalElementService;
import eve.sys.modular.bombill.chemicalelement.entity.ChemicalElement;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import eve.sys.modular.bombill.scenario.mapper.ScenarioMapper;
import eve.sys.modular.bombill.scenario.service.IScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyStore.Entry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.core.util.StrUtil;
import java.util.HashMap;


import javax.annotation.Resource;

/**
 * 场景Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
public class ScenarioServiceImpl extends ServiceImpl<ScenarioMapper, Scenario> implements IScenarioService {

    @Override
    public PageResult<Scenario> pageList(Scenario param) {
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Scenario::getCreateTime);
        
        Page<Scenario> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<Scenario> pageResult = this.page(page, queryWrapper);
        
        return new PageResult<>(pageResult);
    }

    @Override
    public List<Long> getScenarioIdsByBomCostOverviewId(Scenario param) {
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Scenario::getScenarioId);
        queryWrapper.eq(Scenario::getBomCostOverviewId, param.getBomCostOverviewId());
        queryWrapper.orderByAsc(Scenario::getScenarioId);
        return this.list(queryWrapper).stream().map(Scenario::getScenarioId).distinct().collect(Collectors.toList());
    }
    

    @Override
    public List<Scenario> list(Scenario param) {
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotNull(param.getBomCostOverviewId())) {
            queryWrapper.eq(Scenario::getBomCostOverviewId, param.getBomCostOverviewId());
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByAsc(Scenario::getScenarioId);
        
        return this.list(queryWrapper);
    }

    @Override
    public Boolean add(Scenario param) {
        return this.save(param);
    }

    @Override
    public Boolean delete(Scenario param) {

        List<Long> getScenarioIds = getScenarioIdsByBomCostOverviewId(param);
        if (2 > getScenarioIds.size()) {
            throw new ServiceException(500, "至少保留一个场景");
        }

        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Scenario::getBomCostOverviewId, param.getBomCostOverviewId());
        queryWrapper.eq(Scenario::getScenarioId, param.getScenarioId());
        chemicalElementPriceService.delete(ChemicalElementPrice.builder().bomCostOverviewId(param.getBomCostOverviewId()).scenarioId(param.getScenarioId()).build());
        return this.remove(queryWrapper);
        /* if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.removeById(param.getId()); */
    }

    @Override
    public Boolean update(Scenario param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        Scenario updateEntity = Scenario.builder()
            .id(param.getId())
            .bomCostOverviewId(param.getBomCostOverviewId())
            .bomAccountingDetailId(param.getBomAccountingDetailId())
            .untaxedUnitPrice(param.getUntaxedUnitPrice())
            .positiveMaterialAccountingId(param.getPositiveMaterialAccountingId())
            .amountCnyEa(param.getAmountCnyEa())
            .amountCnyWh(param.getAmountCnyWh())
            .proportion(param.getProportion())
            .build();
        return this.updateById(updateEntity);
    }

    @Override
    public Scenario get(Scenario param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }
    @Resource
    private IChemicalElementPriceService chemicalElementPriceService;

    @Resource
    private IPositiveMaterialAccountingService positiveMaterialAccountingService;

    @Resource
    private IChemicalElementService chemicalElementService;
    /*
     * 复制添加上一个场景
     */
    @Override
    public void copyInsertBatch(Scenario param){

        List<Long> getScenarioIds = getScenarioIdsByBomCostOverviewId(param);

        int size = getScenarioIds.size();
        Long previousScenarioId = getScenarioIds.get(size-1);

        List<Scenario> scenarios = this.list(
            Wrappers.lambdaQuery(Scenario.class)
            .eq(Scenario::getScenarioId, previousScenarioId)
            .eq(Scenario::getBomCostOverviewId, param.getBomCostOverviewId())
        );

        scenarios.forEach(e->{
            e.setId(null);
            e.setScenarioId(e.getScenarioId()+1L);
        });

        this.saveBatch(scenarios);

        // 复制上一个场景的化学元素价格到新场景
        chemicalElementPriceService.copyFromPreviousScenario(previousScenarioId, scenarios.get(0));
    }

    @Override
    public void syncScenarios(List<BomAccountingDetail> bomAccountingDetails,List<MaterialPrice> materialPrices,BomCostOverview bomCostOverview){
        if (ObjectUtil.isEmpty(bomAccountingDetails)) {
            return;
        }
        List<Scenario> scenios = this.list(Wrappers.lambdaQuery(Scenario.class).eq(Scenario::getBomCostOverviewId, bomCostOverview.getId()));

        Map<Long, List<Scenario>> scenariosMap = scenios.stream().collect(Collectors.groupingBy(Scenario::getScenarioId));
    
        for (Map.Entry<Long, List<Scenario>> scenarioMap : scenariosMap.entrySet()) {

            for (Scenario scenario : scenarioMap.getValue()) {

                BomAccountingDetail e = bomAccountingDetails.stream().filter(d->d.getId().equals(scenario.getBomAccountingDetailId())).findFirst().orElse(null);
                
                if (null == e) {
                    continue;
                }

                Optional<MaterialPrice> m = materialPrices.stream().filter(mp->mp.getPartNumber().equals(e.getPartNumber())).findFirst();

                BigDecimal untaxedUnitPrice = e.getDisableEditUnitPrice() ? BigDecimal.ZERO : m.map(MaterialPrice::getAccountingPrice).orElse(new BigDecimal(0));

                scenario.setUntaxedUnitPrice(untaxedUnitPrice);
                scenario.setAmountCnyEa(untaxedUnitPrice.multiply(e.getBaseUse()).divide(new BigDecimal(1000),3, RoundingMode.HALF_UP));
                scenario.setAmountCnyWh(bomCostOverview.getRatedEnergy().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : untaxedUnitPrice.multiply(e.getBaseUse()).divide(new BigDecimal(1000)).divide(bomCostOverview.getRatedEnergy(), 3, RoundingMode.HALF_UP));

            }

            BigDecimal sum = scenarioMap.getValue().stream()
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            scenarioMap.getValue().forEach(e->{
                e.setProportion(sum.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getAmountCnyWh().divide(sum, 3, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP));
            });
        }

        this.updateBatchById(scenios);
    }
    /* 
     * 批量插入
     */
    @Override
    public void insertBatch(List<BomAccountingDetail> bomAccountingDetails,List<MaterialPrice> materialPrices,BomCostOverview bomCostOverview){
        
        if (ObjectUtil.isEmpty(bomAccountingDetails)) {
            return;
        }
        
        List<Scenario> scenios = new ArrayList<>();
        bomAccountingDetails.forEach(e->{

            Optional<MaterialPrice> m = materialPrices.stream().filter(mp->mp.getPartNumber().equals(e.getPartNumber())).findFirst();
            
            BigDecimal untaxedUnitPrice = e.getDisableEditUnitPrice() ? BigDecimal.ZERO : m.map(MaterialPrice::getAccountingPrice).orElse(new BigDecimal(0));

            scenios.add(
                Scenario
                .builder()
                .bomCostOverviewId(e.getBomCostOverviewId())
                .scenarioId(1L)
                .bomAccountingDetailId(e.getId())
                .baseUse(e.getBaseUse())
                /* 正极材料核算表ID */
                .positiveMaterialAccountingId(e.getPositiveMaterialAccountingId())
                .untaxedUnitPrice(untaxedUnitPrice)
                .amountCnyEa(untaxedUnitPrice.multiply(e.getBaseUse()).divide(new BigDecimal(1000),3, RoundingMode.HALF_UP))
                .amountCnyWh(bomCostOverview.getRatedEnergy().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : untaxedUnitPrice.multiply(e.getBaseUse()).divide(new BigDecimal(1000)).divide(bomCostOverview.getRatedEnergy(), 3, RoundingMode.HALF_UP))
                .build()
            );
        });

        BigDecimal sum = scenios.stream()
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        scenios.forEach(e->{
            e.setProportion(sum.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getAmountCnyWh().divide(sum, 3, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP));
        });

        this.saveBatch(scenios);
        insertChemicalElementPricesFromScenarios(scenios);
    }
    
    @Resource
    private IBomCostOverviewService bomCostOverviewService;

    /* 修改物料用量触发修改场景使用量 */
    public void batchUpdateBaseUse(Scenario param){
        
        List<Scenario> scenarios = this.list(Wrappers.lambdaQuery(Scenario.class).eq(Scenario::getBomAccountingDetailId, param.getBomAccountingDetailId()));
        scenarios.forEach(e->{
            e.setBaseUse(null == param.getBaseUse() ? e.getBaseUse() : param.getBaseUse());
        });
        param.setBomCostOverviewId(scenarios.get(0).getBomCostOverviewId());
        param.setScenarios(scenarios);
        this.batchUpdate(param);

    }
    /* 修改场景的未税单价触发修改金额&占比 */
    public void batchUpdate(Scenario param){

        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        
        if (null != param.getBomCostOverviewId()) {
            queryWrapper.eq(Scenario::getBomCostOverviewId, param.getBomCostOverviewId());
        }
        if (null != param.getScenarioId()) {
            queryWrapper.eq(Scenario::getScenarioId, param.getScenarioId());
        }

        List<Scenario> scenarios = this.list(queryWrapper);

        BomCostOverview bomCostOverview = bomCostOverviewService.getById(param.getBomCostOverviewId());

        if (!param.getScenarios().isEmpty()) {

            for (Scenario e : param.getScenarios()) {

                scenarios.stream().filter(s -> s.getId().equals(e.getId())).forEach(s->{
                    s.setPositiveMaterialAccountingId(e.getPositiveMaterialAccountingId());
                    s.setBaseUse(null == e.getBaseUse() ? s.getBaseUse() : e.getBaseUse());
                    s.setUntaxedUnitPrice(e.getUntaxedUnitPrice());
                    s.setAmountCnyEa(s.getUntaxedUnitPrice().multiply(s.getBaseUse()).divide(new BigDecimal(1000),3, RoundingMode.HALF_UP));
                    s.setAmountCnyWh(bomCostOverview.getRatedEnergy().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : s.getUntaxedUnitPrice().multiply(s.getBaseUse()).divide(new BigDecimal(1000)).divide(bomCostOverview.getRatedEnergy(), 3, RoundingMode.HALF_UP));
                });
            
            }
        }

        Map<Long, List<Scenario>> scenariosMap = scenarios.stream().collect(Collectors.groupingBy(Scenario::getScenarioId));

        for (Map.Entry<Long, List<Scenario>> entry : scenariosMap.entrySet()) {
            BigDecimal sum = entry.getValue().stream()
                    .map(Scenario::getAmountCnyWh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            entry.getValue().forEach(e->{
                e.setProportion(sum.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getAmountCnyWh().divide(sum, 3, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP));
            });
        }
        this.updateBatchById(scenarios);
    }

    /**
     * 从场景列表中获取化学体系编号，并从正极核算表获取有用量的元素，创建化学元素价格记录
     * @param scenarios 场景列表
     */
    private void insertChemicalElementPricesFromScenarios(List<Scenario> scenarios) {
        if (scenarios == null || scenarios.isEmpty()) {
            return;
        }

        // 1. 从scenarios获取正极材料核算表ID
        Set<Long> positiveMaterialAccountingIds = scenarios.stream()
                .map(Scenario::getPositiveMaterialAccountingId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (positiveMaterialAccountingIds.isEmpty()) {
            return;
        }

        // 2. 从正极核算表根据ID批量获取数据
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getId, positiveMaterialAccountingIds)
        );

        // 转换为JSONObject格式以保持原有逻辑
        List<JSONObject> accountingDataList = accountingList.stream()
            .map(accounting -> {
                JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(accounting));
                if (StrUtil.isNotBlank(accounting.getChemicalManagementElementsJson())) {
                    JSONObject elementsObj = JSONObject.parseObject(accounting.getChemicalManagementElementsJson());
                    for (Map.Entry<String, Object> entry : elementsObj.entrySet()) {
                        obj.put(entry.getKey(), entry.getValue());
                    }
                }
                return obj;
            })
            .collect(Collectors.toList());

        // 3. 解析化学元素用量，只获取有填写用量的元素，并过滤重复值
        Set<String> uniqueElementKeys = new HashSet<>();
        List<ChemicalElementPrice> chemicalElementPrices = new ArrayList<>();

        Scenario firstScenario = scenarios.get(0);

        // 4. 预先收集所有需要查询的元素名称和核算类型
        Set<String> elementKeys = new HashSet<>();
        for (JSONObject accounting : accountingDataList) {
            String elementsJson = accounting.getString("chemicalManagementElementsJson");
            JSONObject elementsData = new JSONObject();

            if (StrUtil.isNotBlank(elementsJson)) {
                try {
                    elementsData = JSONObject.parseObject(elementsJson);
                } catch (Exception e) {
                    for (String key : accounting.keySet()) {
                        if (key.contains("_") && !key.equals("chemicalManagementElementsJson")) {
                            Object value = accounting.get(key);
                            if (value != null) {
                                elementsData.put(key, value);
                            }
                        }
                    }
                }
            } else {
                for (String key : accounting.keySet()) {
                    if (key.contains("_") && !key.equals("chemicalManagementElementsJson")) {
                        Object value = accounting.get(key);
                        if (value != null) {
                            elementsData.put(key, value);
                        }
                    }
                }
            }

            // 收集有用量的元素键
            for (Map.Entry<String, Object> entry : elementsData.entrySet()) {
                String elementKey = entry.getKey();
                Object usageValue = entry.getValue();

                if (usageValue != null && !usageValue.toString().trim().isEmpty()
                    && !usageValue.toString().equals("0")
                    && !usageValue.toString().equals("0.0")) {

                    String[] parts = elementKey.split("_");
                    if (parts.length == 2) {
                        elementKeys.add(elementKey);
                    }
                }
            }
        }

        // 5. 批量查询所有化学元素
        Map<String, ChemicalElement> elementMap = batchFindChemicalElements(elementKeys);

        // 6. 使用预查询的元素信息创建化学元素价格记录
        for (String elementKey : elementKeys) {
            if (!uniqueElementKeys.contains(elementKey)) {
                uniqueElementKeys.add(elementKey);

                ChemicalElement element = elementMap.get(elementKey);
                if (element != null) {
                    String[] parts = elementKey.split("_");
                    String accountingType = parts[0];

                    // 创建化学元素价格记录
                    ChemicalElementPrice price = new ChemicalElementPrice();
                    price.setBomCostOverviewId(firstScenario.getBomCostOverviewId());
                    price.setScenarioId(firstScenario.getScenarioId());
                    price.setChemicalElementId(element.getId());
                    price.setPositiveElectrodeAccountingType(Integer.valueOf(accountingType));
                    price.setCnyPrice(BigDecimal.ZERO);
                    price.setForeignPrice(BigDecimal.ZERO);
                    price.setCurrencyType("USD");
                    price.setUnit(element.getUnit());

                    chemicalElementPrices.add(price);
                }
            }
        }

        // 5. 批量保存化学元素价格记录
        if (!chemicalElementPrices.isEmpty()) {
            chemicalElementPriceService.saveBatch(chemicalElementPrices);
        }
    }

    /**
     * 批量查找化学元素
     * @param elementKeys 元素键集合，格式为 accountingType_elementName
     * @return 元素键到化学元素对象的映射
     */
    private Map<String, ChemicalElement> batchFindChemicalElements(Set<String> elementKeys) {
        Map<String, ChemicalElement> resultMap = new HashMap<>();

        if (elementKeys.isEmpty()) {
            return resultMap;
        }

        // 收集所有需要查询的元素名称和核算类型
        Set<String> elementNames = new HashSet<>();
        Set<String> accountingTypes = new HashSet<>();

        for (String elementKey : elementKeys) {
            String[] parts = elementKey.split("_");
            if (parts.length == 2) {
                accountingTypes.add(parts[0]);
                elementNames.add(parts[1]);
            }
        }

        // 批量查询所有相关的化学元素
        List<ChemicalElement> allElements = chemicalElementService.list(new ChemicalElement());

        // 过滤出匹配的元素并建立映射
        for (ChemicalElement element : allElements) {
            if (elementNames.contains(element.getElementName()) &&
                accountingTypes.contains(element.getAccountingType())) {

                String elementKey = element.getAccountingType() + "_" + element.getElementName();
                if (elementKeys.contains(elementKey)) {
                    resultMap.put(elementKey, element);
                }
            }
        }

        return resultMap;
    }
}
