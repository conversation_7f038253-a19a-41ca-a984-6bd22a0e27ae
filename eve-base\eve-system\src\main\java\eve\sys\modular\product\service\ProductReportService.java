package eve.sys.modular.product.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import eve.core.context.login.LoginContextHolder;
import eve.sys.jiraModular.customTool.service.ICustomfieldoptionService;
import eve.sys.modular.bomhistory.service.ISysBomHistoryService;
import eve.sys.modular.feedback.service.IFeedbackService;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.param.ProductCateBean;
import eve.sys.modular.product.param.ProductCateOptionBean;
import eve.sys.modular.product.param.ProductProjectItem;
import eve.sys.modular.product.param.StageRisk;
import eve.sys.modular.product.param.TreeSelectParam;
import eve.sys.modular.product.param.request.CateParams;
import eve.sys.modular.product.param.request.DocParams;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.techdoc.service.ITechDocService;
import eve.sys.modular.techhistory.service.ITechHistoryService;
import eve.sys.modular.topic.param.Cate;
import eve.sys.modular.user.service.SysUserService;
import eve.sys.modular.weekprocess.service.IWeekProcessDetailService;

@Service
public class ProductReportService {

    @Resource
    private ITechDocService docService;

    @Resource
    private ISysBomHistoryService bomHistoryService;

    @Resource
    private ITechHistoryService techHistoryService;

    @Resource
    private IFeedbackService feedbackService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private ProductJiraService productJiraService;

    @Resource
    private ICustomfieldoptionService customfieldoptionService;

    @Resource
    private IWeekProcessDetailService iWeekProcessDetailService;

    public List<ProductCateBean> getCates(Long isJMArea) {
        List<ProductCateBean> nodes = new ArrayList<ProductCateBean>();

        List<Cate> cateBeans;
        if (null != isJMArea && isJMArea.equals(1L)) {
            cateBeans = customfieldoptionService.cateList("jmProductCate");
        } else {
            cateBeans = customfieldoptionService.cateList("productCate");
        }

        List<ProductCateBean> productCateBeans = new ArrayList<>();

        productCateBeans.add(ProductCateBean.builder()
        .id(2L)
        .pid(0L)
        .field("2")
        .key("2")
        .title("类型")
        .build());

        productCateBeans.add(ProductCateBean.builder()
            .id(0L)
            .pid(2L)
            .field("0")
            .key("0")
            .title("分类")
            .build());

        productCateBeans.add(ProductCateBean.builder()
            .id(1L)
            .pid(2L)
            .field("1")
            .key("1")
            .title("状态")
            .build());

        for (Cate e : cateBeans) {
            productCateBeans.add(ProductCateBean.builder()
            .id(e.getId())
            .pid(e.getPid())
            .field(e.getId()+"")
            .key(e.getId()+"")
            .title(e.getValue())
            .build());
        }
        nodes.addAll(productCateBeans);

        nodes.forEach(e -> e.setWidth("110px"));

        return nodes;
    }

    public List<ProductCateBean> getClafications(){
        List<ProductCateBean> productCateBeans = new ArrayList<ProductCateBean>(){{
            add(ProductCateBean.builder()
                .id(0L)
                .pid(1L)
                .field("0")
                .key("0")
                .title("类别")
                .build());
            add(ProductCateBean.builder()
                .id(1L)
                .pid(1L)
                .field("1")
                .key("1")
                .title("预研产品")
                .build());
            add(ProductCateBean.builder()
                .id(2L)
                .pid(1L)
                .field("2")
                .key("2")
                .title("A|B新产品")
                .build());
            add(ProductCateBean.builder()
                .id(3L)
                .pid(1L)
                .field("3")
                .key("3")
                .title("试产新产品")
                .build());
            add(ProductCateBean.builder()
                .id(4L)
                .pid(1L)
                .field("4")
                .key("4")
                .title("量产品")
                .build());
            add(ProductCateBean.builder()
                .id(6L)
                .pid(1L)
                .field("6")
                .key("6")
                .title("停止")
                .build());
            add(ProductCateBean.builder()
                .id(5L)
                .pid(1L)
                .field("5")
                .key("5")
                .title("其他")
                .build());
        }};
        return productCateBeans;
    }
    
    public List<TreeSelectParam> getCatesTree(CateParams param) {
        List<ProductCateBean> cateBeans = this.getCates(param.getIsJMArea());
        cateBeans = cateBeans.stream().filter(e -> !e.getId().equals(1L) && !e.getId().equals(2L))
                .collect(Collectors.toList());
        List<TreeSelectParam> treeSelectParams = new ArrayList<>();

        for (ProductCateBean e : cateBeans) {
            treeSelectParams.add(TreeSelectParam.builder()
                    .key(e.getId() + "")
                    .value(e.getId() + "")
                    .pid(e.getPid().equals(null) || e.getPid().equals(0L) ? "" : e.getPid() + "")
                    .title(e.getTitle())
                    .children(new ArrayList<>())
                    .build());
        }

        
        treeSelectParams =  Utils.buildCateTree(treeSelectParams);
        if (null != param && null != param.getIsVcylinder() && param.getIsVcylinder().equals(1L)) {
            treeSelectParams = treeSelectParams.stream().filter(e->e.getKey().equals("20047")).collect(Collectors.toList());
        }else{
            treeSelectParams = treeSelectParams.stream().filter(e->!e.getKey().equals("20047")).collect(Collectors.toList());
        }
        return treeSelectParams;
    }

    @Resource
    private IProductManagerService productManagerService;

    public List<ProductProjectItem> getProducts(Long isVcylinder, Long isJMArea) {

        List<ProductProjectItem> projectItems = new ArrayList<>();

        List<ProductProjectItem> productProjectItems = new ArrayList<>();

        if (null != isVcylinder && isVcylinder.equals(1L)) {
            productProjectItems = productManagerService.getProducts(true, false);
        } else if (null != isJMArea && isJMArea.equals(1L)) {
            productProjectItems = productManagerService.getProducts(false, true);
        } else {
            productProjectItems = productManagerService.getProducts(false, false);
        }
        
        
        for (ProductProjectItem e : productProjectItems) {
            for (ProductCateOptionBean _e : e.getProductCateOptionBeans()) {
                String[] split = _e.getValue().split("->");
                ProductProjectItem _item = ProductProjectItem.builder()
                        .issueId(e.getIssueId())
                        .catepid(_e.getPid())
                        .departmentOptionList(e.getDepartmentOptionList())
                        .cateId(_e.getId())
                        .productClassification(e.getProductClassification())
                        .initiationDate(e.getInitiationDate())
                        .issueKey(e.getIssueKey())
                        .productProjectName(e.getProductProjectName())
                        .projectName(e.getProjectName())
                        .productOrProject(e.getProductOrProject())
                        .productManager(e.getProductManager())
                        .customer(e.getCustomer())
                        .fixedState(e.getFixedState())
                        .plannedFixedDate(e.getPlannedFixedDate())
                        .mStatus(e.getMStatus())
                        .state(e.getState())
                        .productCateOptionBeans(e.getProductCateOptionBeans())
                        .productLevel(e.getProductLevel())
                        .researchProjectManager(e.getResearchProjectManager())
                        .showPlan(false)
                        .productStageItems(e.getProductStageItems())
                        .productDistribute(e.getProductDistribute())
                        .productDistributeName(e.getProductDistributeName())
                        .cateIdKey(_e.getId() + "")
                        .catePidKey(_e.getPid().equals(0L) ? _e.getId() + "" : _e.getPid() + "")
                        .build();

                _item = split.length > 1 ? _item.toBuilder().productCateParent(split[0]).productCate(split[1]).build()
                : _item.toBuilder().productCateParent(split[0]).productCate("").build();
                        
                projectItems.add(_item);
            }
        }

        return projectItems;
    }

    public JSONObject getProductVals(CateParams param){
        JSONObject resp = new JSONObject();

        List<ProductProjectItem> productProjectItems = getProducts(1L,0L);

        if (productProjectItems.size() == 0) {
            return resp;
        }
        
        // 过滤同个类型的数据
        List<ProductProjectItem> products = productProjectItems
        .stream()
        .filter(e -> e.getProductOrProject().equals(1L))
        .collect(Collectors.toList());

        JSONObject chatDatas = new JSONObject();
        for (int i = 0; i < 6; i++) {
            final int j = i;
            long count = products
            .stream()
            .filter(e -> e.getProductClassification() == j + 1)
            .count();
            chatDatas.put(j + "", count);
        }

        chatDatas.put("z", products.stream().count());

        if (null != param) {
            // 产品分类
            if (null != param.getProductClassification() && param.getProductClassification().size() > 0) {
                products = products
                .stream()
                .filter(
                    e -> param.getProductClassification().indexOf(Long.valueOf(e.getProductClassification())) > -1
                )
                .collect(Collectors.toList());
            }

            // 产品分布
            if (null != param.getProductDistribute() && param.getProductDistribute().size() > 0) {
                
                products = products
                .stream()
                .filter(
                        e -> param
                        .getProductDistribute()
                        .indexOf(Long.valueOf(e.getProductDistribute())) > -1)
                        .collect(Collectors.toList()
                );
            }

            //产品等级
            if (null != param.getProductLevel() && param.getProductLevel().size() > 0) {
                
                products = products
                .stream()
                .filter(
                        e -> param
                        .getProductLevel()
                        .indexOf(e.getProductLevel()) > -1)
                        .collect(Collectors.toList()
                );
            }

            // 日期过滤
            if (null != param.getStartDate()) {

                products = products
                .stream()
                .filter(
                    e -> e.getInitiationDate().compareTo(param.getStartDate()) >= 0
                    && e.getInitiationDate().compareTo(param.getEndDate()) <= 0
                )
                .collect(Collectors.toList());
            }

            // 关键字查询
            if (null != param.getKeyword()) {
                products = products
                .stream()
                .filter(
                    e -> e.getProductProjectName().toLowerCase().contains(param.getKeyword().toLowerCase())
                )
                .collect(Collectors.toList());
            }

            if (null != param.getDepts() && param.getDepts().size() > 0) {

                products = products
                .stream()
                .filter(
                    e -> null != e.getDepartmentOptionList() 
                    && e.getDepartmentOptionList()
                    .stream()
                    .filter(_e -> param.getDepts().indexOf(Long.valueOf(_e.getId())) > -1)
                    .findFirst()
                    .isPresent()
                )
                .collect(Collectors.toList());
            }
        }

        products.forEach(_e -> {
            _e.setProjectIds(new ArrayList<>());
            _e.getProjectIds().add(_e.getIssueId());
            productProjectItems
            .stream()
            .filter(
                i -> (i.getProductProjectName() + i.getCateIdKey()).equals(_e.getProductProjectName() + _e.getCateIdKey()) 
                && i.getProductOrProject().equals(2L)
            ).forEach(
                _i -> {_e.getProjectIds().add(_i.getIssueId());}
            );
        });

        // 改为按产品类型排序
        products = products
        .stream()
        .sorted(
            Comparator.comparing(ProductProjectItem::getProductDistribute)
            .thenComparing(ProductProjectItem::getProductProjectName)).collect(Collectors.toList()
        );

        JSONArray arr = new JSONArray();
        
        int size = 0;

        for (int n = 1; n < 4; n++) {
            int m = n;
            Map<Long, List<ProductProjectItem>> mapProduct = products.stream()
            .filter(e->e.getProductDistribute().equals(Long.valueOf(m+"")))
            .collect(Collectors.groupingBy(node -> node.getProductClassification()));;

            List<ProductProjectItem> maxSizeProducts = mapProduct.values().stream()
                    .max((a, b) -> a.size() > b.size() ? 1 : -1)
                    .orElse(new ArrayList<>());
            size += maxSizeProducts.size();

            /* 把map的多个list转为 {列1:val,列2:val}格式数据 */
            for (int i = 0, j = maxSizeProducts.size(); i < j; i++) {

                JSONObject jObject = new JSONObject();

                for (Long key : mapProduct.keySet()) {
                    List<ProductProjectItem> tempProjectItems = mapProduct.get(key);
                    if (i < tempProjectItems.size()) {
                        jObject.put(key+"",tempProjectItems.get(i).getProductProjectName());
                        jObject.put(key+"_id", tempProjectItems.get(i).getIssueId());
                        jObject.put(key+"_key", tempProjectItems.get(i).getCateIdKey());
                        jObject.put(key+"_pid",tempProjectItems.get(i).getCatePidKey());
                        jObject.put(key+"_productClassification", tempProjectItems.get(i).getProductClassification());
                        jObject.put(key+"_productDistribute", tempProjectItems.get(i).getProductDistribute());
                        jObject.put(key + "_isAllow", 1);
                        jObject.put(key + "_projectIds", tempProjectItems.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                    }else{
                        jObject.put(key+"", "");
                        jObject.put(key+"_id", "");
                        jObject.put(key+"_key", "");
                        jObject.put(key+"_pid","");
                        jObject.put(key+"_productClassification", -1);
                        jObject.put(key+"_productDistribute", 0);
                        jObject.put(key + "_isAllow", 0);
                        jObject.put(key + "_projectIds", "");
                    }
                }
                jObject.put("0",m == 1 ? "功率型" : (m == 2 ? "能量型" : "混动型"));
                arr.add(jObject);
            }
            
        }

        Map<Long, List<ProductProjectItem>> mapProduct = products.stream()
            .filter(e->e.getProductDistribute().equals(0L))
            .collect(Collectors.groupingBy(node -> node.getProductClassification()));;

        List<ProductProjectItem> maxSizeProducts = mapProduct.values().stream()
                .max((a, b) -> a.size() > b.size() ? 1 : -1)
                .orElse(new ArrayList<>());
        size += maxSizeProducts.size();

        /* 把map的多个list转为 {列1:val,列2:val}格式数据 */
        for (int i = 0, j = maxSizeProducts.size(); i < j; i++) {

            JSONObject jObject = new JSONObject();

            for (Long key : mapProduct.keySet()) {
                List<ProductProjectItem> tempProjectItems = mapProduct.get(key);
                if (i < tempProjectItems.size()) {
                    jObject.put(key+"",tempProjectItems.get(i).getProductProjectName());
                    jObject.put(key+"_id", tempProjectItems.get(i).getIssueId());
                    jObject.put(key+"_key", tempProjectItems.get(i).getCateIdKey());
                    jObject.put(key+"_pid",tempProjectItems.get(i).getCatePidKey());
                    jObject.put(key+"_productClassification", tempProjectItems.get(i).getProductClassification());
                    jObject.put(key + "_isAllow", 1);
                    jObject.put(key + "_projectIds", tempProjectItems.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                }else{
                    jObject.put(key+"", "");
                    jObject.put(key+"_id", "");
                    jObject.put(key+"_key", "");
                    jObject.put(key+"_pid","");
                    jObject.put(key+"_productClassification", -1);
                    jObject.put(key + "_isAllow", 0);
                    jObject.put(key + "_projectIds", "");
                }
            }
            arr.add(jObject);
        }
        
        JSONObject jObject = new JSONObject();
        jObject.put("0", "总计");
        for (int i = 1; i < 7; i++) {
            final int j = i;
            jObject.put(i+"",products.stream().filter(e->e.getProductClassification().equals(Long.valueOf(j+""))).count());
        }
        arr.add(jObject);

        List<ProductCateBean> cateBeans = this.getClafications();
        cateBeans.forEach(
            e -> { e.setPid(e.getPid().equals(1L) ? 0L : e.getPid()); }
        );

        
        List<String> titles = new ArrayList<String>(3){{
            add("产品分布");
            add("产品分类");
            add("产品等级");
        }};
        List<String> distribute = new ArrayList<String>(3){{
            add("功率型");
            add("能量型");
            add("混动型");
        }};
        List<String> classes = new ArrayList<String>(6){{
            add("预研产品");
            add("A|B新产品");
            add("试产新产品");
            add("量产品");
            add("其他");
            add("停止");
        }};
        List<String> levels = new ArrayList<String>(4){{
            add("S");
            add("A");
            add("B");
            add("C");
        }};
        //["#ed7d31", "#5b9bd5", "#4472c4", "#f6d530", "#a5a5a5"],
        List<String> colors = new ArrayList<String>(6){{
            add("#ed7d31");
            add("#7cbbf3");
            add("#5b9bd5");
            add("#4472c4");
            add("#f6d530");
            add("#a5a5a5");
        }};
        //BigDecimal totalBigDecimal = BigDecimal.valueOf(products.size());
        BigDecimal hundredBigDecimal = BigDecimal.valueOf(100);
        JSONArray chatBarDatas = new JSONArray();
        for (int i = 0; i < 3; i++) {

            JSONObject object = new JSONObject();
            object.put("title", titles.get(i));

            if (i == 0) {

                Map<Long,Long> countByDistribute = products
                .stream()
                .collect(Collectors.groupingBy(e -> e.getProductDistribute(), Collectors.counting())); 
                
                Long max = countByDistribute.values().stream()
                .max(Long::compareTo).orElse(0L);

                BigDecimal maxBigDecimal = BigDecimal.valueOf(max.equals(0L) ? max : max + 1L);

                JSONArray barArray = new JSONArray();

                for (int j = 0; j < 3; j++) {
                    final int n = j+1;
                    BigDecimal countDecimal = BigDecimal.valueOf(
                        products.stream().filter(e->e.getProductDistribute().equals(Long.valueOf(n+""))).count()
                    );
                    JSONObject barJsonObject = new JSONObject();
                    barJsonObject.put("key", distribute.get(j));
                    barJsonObject.put("val",countDecimal);
                    barJsonObject.put("ratio", 
                        maxBigDecimal.compareTo(BigDecimal.ZERO) == 0 
                        ? 0 
                        : countDecimal.divide(maxBigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(hundredBigDecimal)
                    );
                    barJsonObject.put("color",colors.get(n));
                    barArray.add(barJsonObject);
                }
                object.put("bars", barArray);
                chatBarDatas.add(object);
            }

            if (i == 1) {
                Map<Long,Long> countByClassification = products
                .stream()
                .collect(Collectors.groupingBy(e -> e.getProductClassification(), Collectors.counting())); 
                
                Long max = countByClassification.values().stream()
                .max(Long::compareTo).orElse(0L);

                BigDecimal maxBigDecimal = BigDecimal.valueOf(max.equals(0L) ? max : max + 1L);

                JSONArray barArray = new JSONArray();

                for (int j = 0; j < 6; j++) {
                    final int n = j+1;
                    BigDecimal countDecimal = BigDecimal.valueOf(
                        products.stream().filter(e->e.getProductClassification().equals(Long.valueOf(n+""))).count()
                    );
                    JSONObject barJsonObject = new JSONObject();
                    barJsonObject.put("key", classes.get(j));
                    barJsonObject.put("val",countDecimal);
                    barJsonObject.put("ratio", 
                        maxBigDecimal.compareTo(BigDecimal.ZERO) == 0 
                        ? 0 
                        : countDecimal.divide(maxBigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(hundredBigDecimal)
                    );
                    barJsonObject.put("color",colors.get(j));
                    barArray.add(barJsonObject);
                }
                object.put("bars", barArray);
                chatBarDatas.add(object);
            }

            if (i == 2) {

                Map<String,Long> countByLevels = products
                .stream()
                .collect(Collectors.groupingBy(e -> e.getProductLevel(), Collectors.counting())); 
                
                Long max = countByLevels.values().stream()
                .max(Long::compareTo).orElse(0L);

                BigDecimal maxBigDecimal = BigDecimal.valueOf(max.equals(0L) ? max : max + 1L);

                JSONArray barArray = new JSONArray();
                for (int j = 0; j < 4; j++) {
                    final int n = j;
                    BigDecimal countDecimal = BigDecimal.valueOf(
                        products.stream().filter(e->e.getProductLevel().equals(levels.get(n))).count()
                    );
                    JSONObject barJsonObject = new JSONObject();
                    barJsonObject.put("key", levels.get(j));
                    barJsonObject.put("val",countDecimal);
                    barJsonObject.put("ratio", 
                        maxBigDecimal.compareTo(BigDecimal.ZERO) == 0 
                        ? 0 
                        : countDecimal.divide(maxBigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(hundredBigDecimal)
                    );
                    barJsonObject.put("color",colors.get(j));
                    barArray.add(barJsonObject);
                }
                object.put("bars", barArray);
                chatBarDatas.add(object);
            }
        }
        resp.put("chatbardatas", chatBarDatas);
        resp.put("tablecolumns", Utils.buildTree(cateBeans));
        resp.put("tabledatas", arr);
        resp.put("rows", size+1);
        resp.put("columns", cateBeans.size());
        resp.put("chartdatas", chatDatas);
        return resp;
    }
    /* public JSONObject getProductVal(CateParams param) {

        JSONObject resp = new JSONObject();

        List<ProductProjectItem> productProjectItems = getProducts(0L);

        if (productProjectItems.size() == 0) {
            return resp;
        }

        // 过滤同个类型的数据
        List<ProductProjectItem> products = productProjectItems
        .stream()
        .filter(e -> e.getProductOrProject().equals(1L))
        .collect(Collectors.toList());


        Map<Long,Long> countMap = products.stream().collect(Collectors.groupingBy(ProductProjectItem::getCatepid,Collectors.counting()));
        Map<Long,Long> countChild = products.stream().collect(Collectors.groupingBy(ProductProjectItem::getCateId,Collectors.counting()));
        countMap.putAll(countChild);
        

        if (null != param) {
            // 产品分类
            if (null != param.getProductClassification() && param.getProductClassification().size() > 0) {
                
                products = products
                .stream()
                .filter(
                        e -> param
                        .getProductClassification()
                        .indexOf(Long.valueOf(e.getProductClassification())) > -1)
                        .collect(Collectors.toList()
                );
            }

            // 产品分布
            if (null != param.getProductDistribute() && param.getProductDistribute().size() > 0) {
                
                products = products
                .stream()
                .filter(
                        e -> param
                        .getProductDistribute()
                        .indexOf(Long.valueOf(e.getProductDistribute())) > -1)
                        .collect(Collectors.toList()
                );
            }

            //产品等级
            if (null != param.getProductLevel() && param.getProductLevel().size() > 0) {
                
                products = products
                .stream()
                .filter(
                        e -> param
                        .getProductLevel()
                        .indexOf(e.getProductLevel()) > -1)
                        .collect(Collectors.toList()
                );
            }

            // 日期过滤
            if (null != param.getStartDate()) {

                products = products
                .stream()
                .filter(
                    e -> e.getInitiationDate().compareTo(param.getStartDate()) >= 0
                    && e.getInitiationDate().compareTo(param.getEndDate()) <= 0
                )
                .collect(Collectors.toList());
            }

            // 关键字查询
            if (null != param.getKeyword()) {
                products = products
                .stream()
                .filter(
                    e -> e.getProductProjectName().toLowerCase().contains(param.getKeyword().toLowerCase())
                )
                .collect(Collectors.toList());
            }

            //部门筛选
            if (null != param.getDepts() && param.getDepts().size() > 0) {

                products = products
                .stream()
                .filter(
                    e -> null != e.getDepartmentOptionList() 
                    && e.getDepartmentOptionList()
                    .stream()
                    .filter(_e -> param.getDepts().indexOf(Long.valueOf(_e.getId())) > -1)
                    .findFirst()
                    .isPresent()
                )
                .collect(Collectors.toList());
            }
        }

        List<Long> issueIds = products.stream().map(ProductProjectItem::getIssueId).collect(Collectors.toList());
        List<WeekProcessDetail> weekProcesses = new ArrayList<>();
        if (issueIds.size() > 0) {
            weekProcesses = iWeekProcessDetailService.list(Wrappers.<WeekProcessDetail>lambdaQuery().in(WeekProcessDetail::getIssueId, issueIds));
        }
        
        //设置健康状态
        for (ProductProjectItem _e : products) {
            
            int state = weekProcesses.stream()
            .filter(item->null != item.getProductState())
            .filter(item->item.getIssueId().equals(_e.getIssueId()))
            .sorted((o1, o2) -> o2.getWeekDate().compareTo(o1.getWeekDate()))
            .map(item->Integer.valueOf(item.getProductState())).findFirst().orElse(-1);

            _e.setWellState(state);
            _e.setProjectIds(new ArrayList<>());
            _e.getProjectIds().add(_e.getIssueId());
            productProjectItems
            .stream()
            .filter(
                i -> (i.getProductProjectName() + i.getCateIdKey()).equals( _e.getProductProjectName() + _e.getCateIdKey()) 
            )
            .filter(i->i.getProductOrProject().equals(2L))
            .forEach(
                _i -> {_e.getProjectIds().add(_i.getIssueId());
            });
        }

        if (null != param) {
            if (null != param.getWealthState()) {
                products = products.stream().filter(e->e.getWellState().equals(param.getWealthState().intValue())).collect(Collectors.toList());
            }
        }

        JSONObject chatDatas = new JSONObject();
        for (int i = 0; i < 4; i++) {
            final int j = i;
            long count = products
            .stream()
            .filter(e -> e.getWellState() == j)
            .count();
            chatDatas.put(j + "", count);
        }

        chatDatas.put("z", products.stream().count());

        // 按产品分类排序
        products = products
        .stream()
        .sorted(
            Comparator
            .comparing(ProductProjectItem::getProductClassification)
            .thenComparing(ProductProjectItem::getProductProjectName))
            .collect(Collectors.toList()
        );

        JSONArray arr = new JSONArray();

        List<Integer> classifications = new ArrayList<Integer>(){{
            add(7);
            add(1);
            add(2);
            add(3);
            add(4);
        }};

        int size = 0;

        for (Integer classificate : classifications) {

            if (classificate != 2 && classificate != 3) {
                List<ProductProjectItem> _productItems = products.stream().filter(e->e.getProductClassification().intValue() == classificate).collect(Collectors.toList());
                Map<String,List<ProductProjectItem>> mapProducts = Utils.mapProductProjectItem(_productItems);
                List<ProductProjectItem> maxSizeProducts = mapProducts.values().stream().max((a, b) -> a.size() > b.size() ? 1 : -1).orElse(new ArrayList<>());
                size += maxSizeProducts.size();
                for (int i = 0, j = maxSizeProducts.size(); i < j; i++) {
                    JSONObject jObject = new JSONObject();
                    for (String key : mapProducts.keySet()) {
                        List<ProductProjectItem> tempProjectItems = mapProducts.get(key);
                        if (i < tempProjectItems.size()) {
                            jObject.put(key,tempProjectItems.get(i).getProductProjectName());
                            jObject.put(key+"_id", tempProjectItems.get(i).getIssueId());
                            jObject.put(key+"_key", tempProjectItems.get(i).getCateIdKey());
                            jObject.put(key+"_pid",tempProjectItems.get(i).getCatePidKey());
                            jObject.put(key+"_productClassification", tempProjectItems.get(i).getProductClassification());
                            jObject.put(key + "_isAllow", 1);
                            jObject.put(key+"_wellState", tempProjectItems.get(i).getWellState());
                            jObject.put(key + "_projectIds", tempProjectItems.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                        
                        }else{
                            jObject.put(key,"");
                            
                        }
                    }
                    jObject.put("0",classificate == 7 ? "立项讨论" : (  classificate == 1 ? "预研产品" : "量产品"));
                    jObject.put("0_productClassification", classificate);
                    jObject.put("1",classificate == 7 ? "立项讨论" : (  classificate == 1 ? "预研产品" : "量产品"));
                    jObject.put("col",2);
                    jObject.put(classificate+"_count",_productItems.size());
                    arr.add(jObject);
                }
            }
            
            if (classificate == 2) {
                List<ProductProjectItem> _productItems = products.stream().filter(e->Arrays.asList(2,3).indexOf(e.getState()) != -1).filter(e->e.getProductClassification().intValue() == classificate).collect(Collectors.toList());
                for (Integer t : Arrays.asList(2,3)) {
                    List<ProductProjectItem> _items = _productItems.stream().filter(e->e.getState() == t).collect(Collectors.toList());
                    Map<String,List<ProductProjectItem>> mapProducts = Utils.mapProductProjectItem(_items);
                    List<ProductProjectItem> maxSizeProducts = mapProducts.values().stream().max((a, b) -> a.size() > b.size() ? 1 : -1).orElse(new ArrayList<>());
                    size += maxSizeProducts.size();
                    for (int i = 0, j = maxSizeProducts.size(); i < j; i++) {
                        JSONObject jObject = new JSONObject();
                        for (String key : mapProducts.keySet()) {
                            List<ProductProjectItem> tempProjectItems = mapProducts.get(key);
                            if (i < tempProjectItems.size()) {
                                jObject.put(key,tempProjectItems.get(i).getProductProjectName());
                                jObject.put(key+"_id", tempProjectItems.get(i).getIssueId());
                                jObject.put(key+"_key", tempProjectItems.get(i).getCateIdKey());
                                jObject.put(key+"_pid",tempProjectItems.get(i).getCatePidKey());
                                jObject.put(key+"_state",tempProjectItems.get(i).getState());
                                jObject.put(key+"_productClassification", tempProjectItems.get(i).getProductClassification());
                                jObject.put(key + "_isAllow", 1);
                                jObject.put(key+"_wellState", tempProjectItems.get(i).getWellState());
                                jObject.put(key + "_projectIds", tempProjectItems.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                            
                            }else{
                                jObject.put(key,"");
                                
                            }
                        }
                        jObject.put("0","A|B新产品");
                        jObject.put("1",t == 2 ? "A样" : "B样");
                        jObject.put("1_state", t);
                        jObject.put("0_productClassification", classificate);
                        jObject.put(t+"_state_count",_items.size());
                        jObject.put("col",1);
                        jObject.put(classificate+"_count",_productItems.size());
                        arr.add(jObject);
                    } 
                }
            }

            if (classificate == 3) {
                List<ProductProjectItem> _productItems = products.stream().filter(e->Arrays.asList(4,5).indexOf(e.getState()) != -1).filter(e->e.getProductClassification().intValue() == classificate).sorted(Comparator.comparing(ProductProjectItem::getState)).collect(Collectors.toList());
                for (Integer t : Arrays.asList(4,5)) {
                    List<ProductProjectItem> _items = _productItems.stream().filter(e->e.getState() == t).collect(Collectors.toList());
                    Map<String,List<ProductProjectItem>> mapProducts = Utils.mapProductProjectItem(_items);
                    List<ProductProjectItem> maxSizeProducts = mapProducts.values().stream().max((a, b) -> a.size() > b.size() ? 1 : -1).orElse(new ArrayList<>());
                    size += maxSizeProducts.size();
                    for (int i = 0, j = maxSizeProducts.size(); i < j; i++) {
                        JSONObject jObject = new JSONObject();
                        for (String key : mapProducts.keySet()) {
                            List<ProductProjectItem> tempProjectItems = mapProducts.get(key);
                            if (i < tempProjectItems.size()) {
                                jObject.put(key,tempProjectItems.get(i).getProductProjectName());
                                jObject.put(key+"_id", tempProjectItems.get(i).getIssueId());
                                jObject.put(key+"_key", tempProjectItems.get(i).getCateIdKey());
                                jObject.put(key+"_pid",tempProjectItems.get(i).getCatePidKey());
                                jObject.put(key+"_state",tempProjectItems.get(i).getState());
                                jObject.put(key+"_productClassification", tempProjectItems.get(i).getProductClassification());
                                jObject.put(key + "_isAllow", 1);
                                jObject.put(key+"_wellState", tempProjectItems.get(i).getWellState());
                                jObject.put(key + "_projectIds", tempProjectItems.get(i).getProjectIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                            
                            }else{
                                jObject.put(key,"");
                                
                            }
                        }
                        jObject.put("0","试产新产品");
                        jObject.put("0_productClassification", classificate);
                        jObject.put("1",t == 4 ? "C样" : "D样");
                        jObject.put("1_state", t);
                        jObject.put(t+"_state_count",_items.size());
                        jObject.put("col",1);
                        jObject.put(classificate+"_count",_productItems.size());
                        arr.add(jObject);
                    } 
                }
            }

        }

        List<ProductCateBean> cateBeans = this.getCates();
        cateBeans.forEach(
            e -> { e.setPid(e.getPid().equals(1L)? 0L:e.getPid());}
        );
                
        List<ProductCateBean> ProductCateBeans = Utils.getChilds(cateBeans);
        List<ProductCateBean> cateTree = Utils.buildTree(cateBeans);
        resp.put("tablecolumns", cateTree.stream().filter(e->!e.getId().equals(20047L)).collect(Collectors.toList()));
        resp.put("tabledatas", arr);
        resp.put("rows", size);
        resp.put("columns", ProductCateBeans.size());
        resp.put("chartdatas", chatDatas);
        resp.put("cateCount",countMap);
        return resp;
    } */

    /* public List<ProductCateBean> getCateTree(String cateName) {
        List<ProductCateBean> nodes = new ArrayList<>();

        nodes.add(new ProductCateBean(104L, 0L, "ee", "ee", "基本信息", "left", null, null));
        nodes.add(new ProductCateBean(100L, 0L, "aa", "aa", "产品信息", null, null, null));
        nodes.add(new ProductCateBean(101L, 0L, "bb", "bb", "产品开发进度", null, null, null));
        nodes.add(new ProductCateBean(110L, 0L, "ff", "ff", "开发进展", null, null, null));
        nodes.add(new ProductCateBean(102L, 0L, "cc", "cc", "产能规划", null, null, null));
        nodes.add(new ProductCateBean(103L, 0L, "dd", "dd", "信息反馈", null, null, null));

        nodes.add(new ProductCateBean(2L, 104L, "productName", "productName", "产品", null, "50px", null));
        nodes.add(new ProductCateBean(3L, 104L, "productCateName", "productCateName", "类别", null, "50px", null));
        nodes.add(new ProductCateBean(4L, 104L, "no", "no", "序号", null, "50px", null));
        nodes.add(
                new ProductCateBean(5L, 104L, "productProjectName", "productProjectName", "产品名称", null, "80px", null));
        nodes.add(new ProductCateBean(1L, 100L, "productLevel", "productLevel", "项目等级", null, "80px", null));
        nodes.add(new ProductCateBean(8L, 100L, "projectName", "projectName", "项目名称", null, "80px", null));
        nodes.add(new ProductCateBean(7L, 100L, "customer", "customer", "客户", null, "80px", null));
        nodes.add(new ProductCateBean(6L, 100L, "productManager", "productManager", "产品经理", null, "80px", null));

        nodes.add(new ProductCateBean(105L, 100L, "researchProjectManager", "researchProjectManager", "RPM", null,
                "80px", null));

        nodes.add(new ProductCateBean(9L, 100L, "fixedState", "fixedState", "定点状态", null, "80px", null));
        nodes.add(new ProductCateBean(10L, 100L, "dischargeRate", "dischargeRate", "额定容量放电倍率(C)", null, "80px", null));
        nodes.add(new ProductCateBean(10L, 100L, "ah", "ah", "电芯容量(Ah)", null, "80px", null));
        nodes.add(new ProductCateBean(107L, 100L, "voltage", "voltage", "电压(V)", null, "80px", null));
        nodes.add(new ProductCateBean(11L, 100L, "size", "size", "尺寸(mm)", null, "120px", null));

        nodes.add(new ProductCateBean(12L, 101L, "a", "a", "项目启动", null, "80px", null));
        nodes.add(new ProductCateBean(38L, 101L, "b", "b", "项目规划", null, "80px", null));
        nodes.add(new ProductCateBean(13L, 101L, "c", "c", "A样", null, "", null));
        nodes.add(new ProductCateBean(14L, 101L, "d", "d", "B样", null, "", null));
        nodes.add(new ProductCateBean(15L, 101L, "e", "e", "C样", null, "", null));
        nodes.add(new ProductCateBean(16L, 101L, "f", "f", "D样", null, "", null));
        nodes.add(new ProductCateBean(39L, 101L, "g", "g", "SOP", null, "", null));
        nodes.add(new ProductCateBean(106L, 110L, "processStr", "processStr", "产品开发进展", null, "400px", null));

        nodes.add(new ProductCateBean(17L, 102L, "dept", "dept", "事业部", null, "80px", null));
        nodes.add(new ProductCateBean(18L, 102L, "factory", "factory", "工厂代码", null, "80px", null));

        nodes.add(new ProductCateBean(20L, 102L, "productLine", "productLine", "产线", null, "80px", null));
        nodes.add(new ProductCateBean(21L, 102L, "productCap", "productCap", "设计产能(GWh)", null, "80px", null));

        nodes.add(new ProductCateBean(25L, 100L, "performance", "performance", "性能表达", null, "200px", null));

        nodes.add(new ProductCateBean(26L, 103L, "produceFeedback", "produceFeedback", "制造反馈", null, "100px", null));
        nodes.add(new ProductCateBean(27L, 103L, "sellFeedback", "sellFeedback", "销售反馈", null, "100px", null));
        nodes.add(new ProductCateBean(28L, 103L, "supplyFeedback", "supplyFeedback", "供应链反馈", null, "100px", null));

        nodes.add(new ProductCateBean(29L, 12L, "ko", "ko", "K0 立项评审", null, "80px", null));
        nodes.add(new ProductCateBean(30L, 38L, "m1", "m1", "M1 项目规划", null, "80px", null));
        nodes.add(new ProductCateBean(31L, 13L, "m2", "m2", "M2 A样冻结", null, "80px", null));
        nodes.add(new ProductCateBean(37L, 13L, "m2t", "m2t", "M2 转阶段", null, "80px", null));
        nodes.add(new ProductCateBean(32L, 14L, "m3", "m3", "M3 B样冻结", null, "80px", null));
        nodes.add(new ProductCateBean(33L, 14L, "m3t", "m3t", "M3 转阶段", null, "80px", null));
        nodes.add(new ProductCateBean(34L, 15L, "m4", "m4", "M4 C样冻结", null, "80px", null));
        nodes.add(new ProductCateBean(35L, 16L, "m5", "m5", "M5 PPAP", null, "80px", null));
        nodes.add(new ProductCateBean(36L, 39L, "m6", "m6", "M6 SOP", null, "80px", null));

        return nodes;
    } */

    /* public Map<String, Long> getStageTroublcCountByStatusLamp(String token, DocParams docParams) {
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", "0");
                put("issueType", "1");
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", docParams);
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.StageApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }

        List<StageProblem> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), StageProblem.class);

        Map<String, Long> map = new HashMap<>();

        map.put("statusRedLampCount", stageProblems.stream().filter(e -> e.getStatusLamp().equals(1L)).count());
        map.put("statusYellowLampCount", stageProblems.stream().filter(e -> e.getStatusLamp().equals(2L)).count());

        return map;
    } */

    /* public List<StageProblem> getDashStateTroubles(String token, DocParams docParams) {
        String productStates = null;
        if (null != docParams) {
            productStates = docParams.getProductStates();
            docParams.setProductStates(null);
        }
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", docParams.getIssueId() + "");
                put("issueType", "1");
                put("productState", docParams.getState());
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", docParams);
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.StageApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }

        List<StageProblem> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), StageProblem.class);

        if (null != productStates) {
            List<Long> longList = Arrays.stream(productStates.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            stageProblems = stageProblems.stream().filter(e -> longList.indexOf(e.getProductState()) > -1)
                    .collect(Collectors.toList());
        }

        if (null != docParams.getProductProjectName()) {
            stageProblems = stageProblems.stream().filter(e -> null != e.getProductProjectName() && e.getProductProjectName().toLowerCase()
                    .contains(docParams.getProductProjectName().toLowerCase())).collect(Collectors.toList());
        }

        int isCurWeek = Optional.ofNullable(docParams).map(DocParams::getIsCurWeek).orElse(0);

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(1);
        final int nowWeek = calendar.get(Calendar.WEEK_OF_YEAR) - 1;
        
        if (isCurWeek == 1) {
            stageProblems = stageProblems.stream().filter(e->{
                Calendar _calendar = Calendar.getInstance();
                _calendar.setFirstDayOfWeek(1);
                _calendar.setTime(e.getFindDate());
                return _calendar.get(Calendar.WEEK_OF_YEAR) == nowWeek;
            }).collect(Collectors.toList());
        }

        return stageProblems;
    } */

    /* public void exportProblems(HttpServletResponse response,String token, DocParams docParams){
        String productStates = null;
        if (null != docParams) {
            productStates = docParams.getProductStates();
            docParams.setProductStates(null);
        }
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", docParams.getIssueId() + "");
                put("issueType", "1");
                put("productStage", docParams.getStage());
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", docParams);
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.StageApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return ;
        }

        List<StageProblemParams> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), StageProblemParams.class);

        if (null != productStates) {
            List<Long> longList = Arrays.stream(productStates.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            stageProblems = stageProblems.stream().filter(e -> longList.indexOf(Long.valueOf(e.getProductState()+"")) > -1)
                    .collect(Collectors.toList());
        }

        if (null != docParams.getProductProjectName()) {
            stageProblems = stageProblems.stream().filter(e -> e.getProductProjectName().toLowerCase()
                    .contains(docParams.getProductProjectName().toLowerCase())).collect(Collectors.toList());
        }

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder
            .encode("问题升级管理", "UTF-8")
            .replaceAll("\\+", "%20");
            response
            .setHeader(
                "Content-disposition", 
                "attachment;filename*=utf-8''" + fileName + ".xlsx"
            );
            // 如果不用模板的方式导出的话，是doWrite
            EasyExcel
            .write(response.getOutputStream(), StageProblemParams.class)
            .sheet("问题升级管理")
            .doWrite(stageProblems);

        } catch (Exception e) {
            e.printStackTrace();
        }
    } */

    /* public List<StageProblem> getStageTroubles(String token, DocParams docParams) {

        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", docParams.getIssueId() + "");
                put("issueType", "1");
                put("productStage", docParams.getStage());
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", docParams);
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.StageApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }
        List<StageProblem> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), StageProblem.class);

        return null != docParams.getStage()
                ? stageProblems.stream().filter(e -> docParams.getStage().equals(e.getProductStage().intValue()))
                        .collect(Collectors.toList())
                : stageProblems;
    } */

    /* public boolean addOrUpdateStageProblem(String token, StageProblem stageProblem) {
        stageProblem.setPresenter(LoginContextHolder.me().getSysLoginUserAccount());
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", stageProblem.getIssueId() + "");
                put("issueType", "1");
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", stageProblem);
            }
        };
        JSONObject resp = Utils.doPostAndToken(JiraApiParams.StageAddOrUpdate, token, params);

        return resp.getBoolean("result");
    } */

    /* public boolean delStageProblem(String token, StageProblem stageProblem) {
        stageProblem.setPresenter(LoginContextHolder.me().getSysLoginUserAccount());
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", stageProblem.getIssueId() + "");
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
            }
        };
        JSONObject resp = Utils.doPostAndToken(JiraApiParams.StageDel, token, params);

        return resp.getBoolean("result");
    } */

    /* public boolean transitionStage(String token, StageProblem stageProblem) {

        Map<String, String> params = new HashMap<String, String>(3) {
            {
                put("issueId", stageProblem.getIssueId() + "");
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("transitionId", stageProblem.getTransitionId());
            }
        };
        JSONObject resp = Utils.doGet(JiraApiParams.StageTransition, token, params);
        return resp.getBoolean("result");
    } */

    /* public List<ReviewParam> getReviews(String token, DocParams docParams) {
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", docParams.getIssueId() + "");
                put("productStage", docParams.getStage());
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.ReviewApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }
        List<ReviewParam> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), ReviewParam.class);

        return stageProblems;
    } */

    public List<StageRisk> getStageRisk(String token, DocParams docParams) {
        Map<String, Object> params = new HashMap<String, Object>(3) {
            {
                put("issueId", docParams.getIssueId() + "");
                put("issueType", "2");
                put("productStage", docParams.getStage());
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
            }
        };
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.StageApi, token, params);
        if (!jiraResp.getBoolean("result")) {
            return null;
        }
        List<StageRisk> stageProblems = JSONObject.parseArray(jiraResp.getString("value"), StageRisk.class);

        return null != docParams.getStage()
                ? stageProblems.stream().filter(e -> docParams.getStage().equals(e.getProductStage().intValue()))
                        .collect(Collectors.toList())
                : stageProblems;
    }

    public boolean addOrUpdateStageRisk(String token, StageRisk stageRisk) {
        Map<String, Object> params = new HashMap<String, Object>(4) {
            {
                put("issueId", stageRisk.getIssueId() + "");
                put("issueType", "2");
                put("userName", LoginContextHolder.me().getSysLoginUserAccount());
                put("map", stageRisk);
            }
        };
        JSONObject resp = Utils.doPostAndToken(JiraApiParams.StageAddOrUpdate, token, params);

        return resp.getBoolean("result");
    }
}
