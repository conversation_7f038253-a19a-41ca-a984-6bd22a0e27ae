package eve.sys.modular.product.controller;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.jiraModular.productManager.entity.ProductManagerProblemIssue;
import eve.sys.modular.product.entity.ProductManager;
import eve.sys.modular.product.param.request.ChartParam;
import eve.sys.modular.product.service.JMChartService;
import eve.sys.modular.product.service.VcyChartService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Optional;

@RestController
@RequestMapping("/product/jm/chart")
public class JMChartController {

    @Resource
    private JMChartService jmChartService;

    @Resource
    private VcyChartService vcyChartService;

    @PostMapping("/getProductStatics")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductStatics() throws Exception {
        return new SuccessResponseData(jmChartService.getProductStatics());
    }


    @PostMapping("/getCountByProductClass")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountByProductClass() {
        return new SuccessResponseData(jmChartService.getCountByProductClass());
    }

    @PostMapping("/getAll")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getAlls() {
        return new SuccessResponseData(jmChartService.getAlls());
    }

    @PostMapping("/getAllOfNotSplit")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getAllOfNotSplit() {
        return new SuccessResponseData(jmChartService.getAllOfNotSplit());
    }
    

    @PostMapping("/getTreeProducts")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getTreeProducts(@RequestBody String entity) {
        return new SuccessResponseData(jmChartService.getTreeProducts());
    }

    /**
     * 不区分细分市场
     */
    @PostMapping("/getTreeProductsOfNotSplit")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getTreeProductsOfNotSplit(@RequestBody String entity) {
        return new SuccessResponseData(jmChartService.getTreeProductsOfNotSplit());
    }
    

    @PostMapping("/getCountByProductLevel")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountByProductLevel() {
        return new SuccessResponseData(jmChartService.getCountByProductLevel());
    }
    
    @PostMapping("/getCountByProjectLevel")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountByProjectLevel() {
        return new SuccessResponseData(jmChartService.getCountByProjectLevel());
    }

    @PostMapping("/getCountByProductCate")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountByProductCate() {
        return new SuccessResponseData(jmChartService.getCountByProductCate());
    }

    @PostMapping("/getCountProcessForProject")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountProcessForProject(@RequestBody ChartParam param) {
        if (!Optional.ofNullable(param).map(ChartParam::getUnderDev).isPresent()) {
            param = ChartParam.builder().underDev(1).build();
        }
        return new SuccessResponseData(jmChartService.getCountProcessForProject(param));
    }

    @PostMapping("/getCountAlterForProduct")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getCountAlterForProduct(@RequestBody ChartParam param) {
        if (!Optional.ofNullable(param).map(ChartParam::getProductState).isPresent()) {
            param = ChartParam.builder().productState(1).build();
        }
        return new SuccessResponseData(jmChartService.getCountAlterForProduct(param));
    }

    @PostMapping("/monitor")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData monitor() throws ParseException {
        return new SuccessResponseData(jmChartService.monitor());
    }

    @PostMapping("/getStageCount")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getStageCount(){
        return new SuccessResponseData(jmChartService.getStageCount());
    }

    @PostMapping("/moniterDetails")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData moniterDetails(@RequestBody ProductManagerProblemIssue params) {
        return new SuccessResponseData(jmChartService.moniterDetails(params));
    }


    @PostMapping("/moniterDetailsOfNotSplit")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData moniterDetailsOfNotSplit(@RequestBody ProductManagerProblemIssue params) {
        return new SuccessResponseData(jmChartService.moniterDetailsOfNotSplit(params));
    }

    @PostMapping("/moniterDetails/export")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportMoniterDetails(HttpServletResponse response,@RequestBody ProductManagerProblemIssue param) {
        jmChartService.exportMoniterDetails(response,param);
    }

    @PostMapping("/getProcessForProject")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProcessForProject(@RequestBody ChartParam param) {
        return new SuccessResponseData(jmChartService.getProcessForProject(param));
    }

    @PostMapping("/getProductAlignTable")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductAlignTable(@RequestBody ProductManager param) {
        return new SuccessResponseData(jmChartService.getProductAlignTable(param));
    }
    
    @PostMapping("/getAltersForProduct")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getAltersForProduct(@RequestBody ChartParam params) throws Exception {
        return new SuccessResponseData(jmChartService.getAltersForProduct(params));
    }
    
    @GetMapping("/getStageDocsDetail")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getStageDocsDetail() throws Exception {
        return new SuccessResponseData(jmChartService.getStageDocsDetail());
    }

    @PostMapping("/getStageDocsDetailOfNotSplit")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getStageDocsDetailOfNotSplit(@RequestBody ProductManager param) throws Exception {
        return new SuccessResponseData(jmChartService.getStageDocsDetailOfNotSplit(param));
    }


    @PostMapping("/getProductParams")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductParams(@RequestBody ProductManager param) throws Exception {
        return new SuccessResponseData(jmChartService.getProductParams(param));
    }


    @PostMapping("/getProductParamsOfNotSplit")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductParamsOfNotSplit(@RequestBody ProductManager param) throws Exception {
        return new SuccessResponseData(jmChartService.getProductParamsOfNotSplit(param));
    }

    

    @PostMapping("/getProductParamsOfNotSplitByFileTrans")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductParamsOfNotSplitByFileTrans(@RequestBody ProductManager param) throws Exception {
        return new SuccessResponseData(jmChartService.getProductParamsOfNotSplitByFileTrans(param));
    }

    @PostMapping("/getProductParamsOfTest")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getProductParamsOfTest(@RequestBody ProductManager param) throws Exception {
        return new SuccessResponseData(jmChartService.getProductParamsOfTest(param));
    }


    @PostMapping("/exportParmas")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportParams(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportParmas(response,param);
    }


    @PostMapping("/exportMarketParmas")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportMarketParmas(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportMarketParmas(response,param);
    }


    @PostMapping("/exportSpecsParmas")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportSpecsParmas(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportSpecsParmas(response,param);
    }


    @PostMapping("/exportFactoryParmas")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportFactoryParmas(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportFactoryParmas(response,param);
    }


    @PostMapping("/exportBaseInfo")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportBaseInfo(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportBaseInfo(response,param);
    }

    @PostMapping("/exportStagePlan")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportStagePlan(HttpServletResponse response,@RequestBody ProductManager param) {
        jmChartService.exportStagePlan(response,param);
    }


    @PostMapping("/v/getCountByProductClass")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountByProductClass() {
        return new SuccessResponseData(vcyChartService.getCountByProductClass());
    }

    @PostMapping("/v/getAll")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvAlls() {
        return new SuccessResponseData(vcyChartService.getAlls());
    }

    @PostMapping("/v/getCountByProductLevel")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountByProductLevel() {
        return new SuccessResponseData(vcyChartService.getCountByProductLevel());
    }
    
    @PostMapping("/v/getCountByProjectLevel")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountByProjectLevel() {
        return new SuccessResponseData(vcyChartService.getCountByProjectLevel());
    }

    @PostMapping("/v/getCountByProductCate")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountByProductCate() {
        return new SuccessResponseData(vcyChartService.getCountByProductCate());
    }

    @PostMapping("/v/getCountProcessForProject")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountProcessForProject(@RequestBody ChartParam param) {
        if (!Optional.ofNullable(param).map(ChartParam::getUnderDev).isPresent()) {
            param = ChartParam.builder().underDev(1).build();
        }
        return new SuccessResponseData(vcyChartService.getCountProcessForProject(param));
    }

    @PostMapping("/v/getCountAlterForProduct")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvCountAlterForProduct(@RequestBody ChartParam param) {
        if (!Optional.ofNullable(param).map(ChartParam::getProductState).isPresent()) {
            param = ChartParam.builder().productState(1).build();
        }
        return new SuccessResponseData(vcyChartService.getCountAlterForProduct(param));
    }

    @PostMapping("/v/monitor")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData vmonitor() throws ParseException {
        return new SuccessResponseData(vcyChartService.monitor());
    }

    @PostMapping("/v/moniterDetails")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData vMoniterDetails(@RequestBody ProductManagerProblemIssue params) {
        return new SuccessResponseData(vcyChartService.moniterDetails(params));
    }

    @PostMapping("/v/getStageCount")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvStageCount(){
        return new SuccessResponseData(vcyChartService.getStageCount());
    }

    @PostMapping("/v/moniterDetails/export")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public void exportvMoniterDetails(HttpServletResponse response,@RequestBody ProductManagerProblemIssue param) {
        vcyChartService.exportMoniterDetails(response,param);
    }

    @PostMapping("/v/getProcessForProject")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvProcessForProject(@RequestBody ChartParam param) {
        return new SuccessResponseData(vcyChartService.getProcessForProject(param));
    }

    @GetMapping("/v/getStageDocsDetail")
    @BusinessLog(opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getvStageDocsDetail() throws Exception {
        return new SuccessResponseData(vcyChartService.getStageDocsDetail());
    }
}
