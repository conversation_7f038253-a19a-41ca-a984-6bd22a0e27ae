package eve.sys.modular.bombill.bomaccountingdetail.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

/**
 * BOM核算明细实体类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("BOM_ACCOUNTING_DETAIL")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class BomAccountingDetail extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM成本总览表ID
     */
    private Long bomCostOverviewId;

    /**
     * 物料代码
     */
    private String partNumber;

    /**
     * 物料名称
     */
    private String partName;

    /**
     * 物料规格
     */
    private String partDescription;


    /**
     * 物料描述
     */
    private String partDesc;

    /**
     * 物料单位
     */
    private String partUnit;

    /**
     * 理论用量
     */
    private BigDecimal baseUse;

    private BigDecimal processingFee;

    /**
     * 结构件标志
     */
    private Integer structureFlag;

    /**
     * 物料分类
     */
    private Integer materialType;

    /**
     * 正极材料核算表ID（替代原chemicalSystemCode）
     */
    private Long positiveMaterialAccountingId;

    /**
     * 化学体系编号（用于显示，从PositiveMaterialAccounting关联查询）
     */
    @TableField(exist = false)
    private String chemicalSystemCode;

    @TableField(exist = false)
    private Boolean disableEditUnitPrice;

    /**
     * 化学体系名称（用于显示，从PositiveMaterialAccounting关联查询）
     */
    @TableField(exist = false)
    private String chemicalSystem;

    @TableField(exist = false)
    private Long oldId;

    private String remark;
}
