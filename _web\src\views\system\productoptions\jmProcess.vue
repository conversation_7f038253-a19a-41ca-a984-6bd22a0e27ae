<template>
	<div class="product_width">
		<a-spin :spinning="loading">
			<!-- 筛选区域 start -->
			<!-- <div class="table-page-search-wrapper">
				<a-form layout="inline">
					<a-row :gutter="48">
						<a-col :md="4" :sm="24" :style="{flex:'1',display:'flex',justifyContent:'flex-end'}">
							<a-form-item label="时间" :style="{margin:'0'}">
                <a-range-picker style="margin: 0 auto" @change="handleSelectWeek"
                                format="YYYY-wo"
                                :value="dateValue"
                                :placeholder="['周范围开始', '周范围结束']" >
                  <a-icon slot="suffixIcon" type="schedule" theme="twoTone" two-tone-color="#d9d9d9"/>
                </a-range-picker>
                <a-button v-if="hasPerm('docs:pm')" size="small" style="margin-left: 20px;" type="primary" @click="weekExport" >导出</a-button>
              </a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</div> -->
			<!-- 筛选区域 end -->

			<!-- 表格 start -->
			<div class="table-wrapper">
				<a-table
					ref="table"
          bordered
          :pagination="false"
          size="small"
					:columns="columns"
					:dataSource="loadData"
          :row-key="record =>record.id"
				>
          <div slot="dimensions" slot-scope="text,record">
            <span v-if="record.weekProcessDetail">{{dimensionsOptions.find(item => item.code === text).name}}</span>
            <span v-else></span>
          </div>

          <span slot="progressDesc" slot-scope="text">
               <clamp :expend="true" :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :isOneLine='true'  :key="new Date()" />
          </span>

          <span slot="nextPlan" slot-scope="text">
               <clamp :expend="true" :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :isOneLine='true'  :key="new Date()" />
          </span>

          <span slot="unionName" slot-scope="text">
               <clamp :expend="true" :text="text" :sourceText="text?text.split(/[(\r\n)\r\n]+/):['-']" :isCenter="true" :isOneLine='true'  :key="new Date()" />
          </span>
				</a-table>
			</div>
			<!-- 表格 end -->
		</a-spin>
	</div>
</template>

<script>
import { dashboardProcess2, processExport } from "@/api/modular/system/dashboardManage"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

import { getWeek } from "@/utils/formatData"

import moment from 'moment'
import {clamp} from '@/components'
import Vue from "vue";
import { DICT_TYPE_TREE_DATA } from "@/store/mutation-types";

export default {
	components: {
		Treeselect,
		clamp
	},

	data() {
		return {
      //dateValue:[this.moment(new Date(new Date().getTime() - (7 * 24 * 60 * 60 * 1000))),this.moment(new Date())],
      dimensionsOptions: [],
      weekTime: [getWeek(new Date())-1,getWeek(new Date())-1],
      typeOptions: [
				{
					id: 1,
					label: "预研产品"
				},
				{
					id: 2,
					label: "A|B新产品"
				},
				{
					id: 3,
					label: "试产新产品"
				},
				{
					id: 4,
					label: "量产品"
				},
				{
					id: 5,
					label: "其他"
				},
				{
					id: 6,
					label: "停止"
				}
			],
      queryparam: {
				cates: [],
				states: [],
				depts:[],
				keyword: null,
			},
      title: `CW${getWeek(new Date(new Date().getTime() - (7 * 24 * 60 * 60 * 1000)))} 周进展`,
      loading: true,
			loadData: [],
			totalData: [],
		}
	},
	props: {
		// 表格高度
		tableHeight: {
			type: Number,
			default: 0
		},
		// 表格滚动高度
		scrollHeigh: {
			type: Number,
			default: 0
		},
		searchParam: {
			type: Object,
			default: {}
		},
    // 荆门地区标识
    isJMArea: {
      type: Number,
      default: 1
    },
	},
	watch: {
		loadData(newVal, oldVal) {
			if (this.loadData.length > 0) {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
			} else {
				// 动态修改--height的值
				document.documentElement.style.setProperty(`--height`, "80px")
			}
		},
		searchParam: {
			handler(newName, oldName) {
				this.queryparam.cates = this.searchParam.cates
				this.queryparam.states = this.searchParam.states
				this.queryparam.depts = this.searchParam.depts
				this.queryparam.inputSearch = this.searchParam.inputSearch
				// 立项日期筛选框
				this.dateChange("", this.searchParam.dateString)
        this.handleSelectWeek("", this.searchParam.weeks)
        //this.weekExport()
				this.callFilter()
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		moment,
    weekExport(){
      const result = this.loadData.flatMap(e=>e.issueId);
      let param = {
        "weekTimeLists": this.weekTime,
        "issueIds": result
      }
      processExport(param).then(res => {
        this.spinning = false
        const fileName = `CW周进展.xlsx`;

        if(!res) return
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) // 构造一个blob对象来处理数据，并设置文件类型

        if (window.navigator.msSaveOrOpenBlob) { //兼容IE10
          navigator.msSaveBlob(blob, fileName)
        } else {
          const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
          const a = document.createElement('a') //创建a标签
          a.style.display = 'none'
          a.href = href // 指定下载链接
          a.download = fileName //指定下载文件名
          a.click() //触发下载
          URL.revokeObjectURL(a.href) //释放URL对象
        }
      })
    },
		change() {
			this.callFilter()
		},
		// 数据筛选
		callFilter() {
      if(this.searchParam.inputSearch.length === 1 && !this.searchParam.inputSearch[0]){
        this.loadData = [];
      }
			let filterData = JSON.parse(JSON.stringify(this.totalData))
			
			if (this.queryparam["cates"].length > 0) {
				filterData = filterData.filter(
					item =>
						this.queryparam["cates"].indexOf(parseInt(item.cateId)) > -1 ||
						this.queryparam["cates"].indexOf(parseInt(item.catepid)) > -1
				)
        if (this.queryparam["cates"].indexOf(2) != -1) {
            filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() == 'g')
        }

        if (this.queryparam["cates"].indexOf(3) != -1) {
            filterData = filterData.filter(item=>item.productProjectName.substring(0,1).toLowerCase() != 'g')
        }
			}
			if (this.queryparam["states"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["states"].indexOf(parseInt(item.state)) > -1)
			}
			if (this.queryparam["depts"].length > 0) {
				filterData = filterData.filter(item => this.queryparam["depts"].indexOf(parseInt(item.deptId)) > -1)
			}

			// 产品名称
			if (this.queryparam["inputSearch"][0].keyword !== '' && this.queryparam["inputSearch"].length > 0) {
				const temList = []
				this.searchParam.inputSearch.forEach(v => {
					if (v.keyword === "") return
					filterData.forEach(e => {
                if (e.productProjectName.toLowerCase().indexOf(v.keyword.toLowerCase()) !== -1) {
                  temList.push(e)
                }
					})
				})
				filterData = _.uniqWith(temList, _.isEqual)
			}

			if (this.queryparam.startDate != null) {
				filterData = filterData.filter(
					item =>
						Date.parse(item.initiationDate) >= this.queryparam.startDate &&
						Date.parse(item.initiationDate) < this.queryparam.endDate
				)
			}
      let newArr = [];
      if (filterData && filterData.length > 0) {
        newArr = filterData.flatMap(item => {
          if (item.weekProcessDetail && item.weekProcessDetail.length > 0) {
            return item.weekProcessDetail.map(e => ({
              ...e,
              ...item,
              'id': this.generateRowKey()
            }));
          } else {
            return []; // 处理 weekProcessDetail 为空的情况，返回一个空数组
          }
        });
      }

      newArr.sort((a, b) => {
        // 先按 issueId 排序
        if (a.issueId < b.issueId) {
          return -1;
        } else if (a.issueId > b.issueId) {
          return 1;
        } else {
          //年数倒序
          if(this.year(a) < this.year(b)){
            return 0
          }
          if(this.year(a) > this.year(b)){
            return -1
          }
          //周数倒序
          if (a.weekTime === b.weekTime) {
            //再按项目维度排序
            return a.dimensions < b.dimensions ? -1 : 0;
          }
          return a.weekTime > b.weekTime ? -1 : 0
        }
      });

      //去除childen
      newArr.forEach(e=>e.children=null)
      if(newArr.length){
        this.loadData = newArr
      }else {
        this.loadData = []
      }
		},
		dateChange(date, dateString) {
			if (dateString[0] != null && dateString[0] != "") {
				this.queryparam.startDate = Date.parse(dateString[0])
			} else {
				this.queryparam.startDate = null
			}
			if (dateString[1] != null && dateString[1] != "") {
				this.queryparam.endDate = Date.parse(dateString[1])
			} else {
				this.queryparam.endDate = null
			}

			this.callFilter()
		},
		// 选择周
		handleSelectWeek(date, dateString) {
      this.weekTime[0] = dateString[0]
      this.weekTime[1] = dateString[1]
      //this.dateValue = date
      if(dateString[0] == '' && dateString[1] == ''){
      }else {
        this.callDashboardProcess(this.weekTime, this.isJMArea)
        if(this.weekTime[0]){
          this.title = `CW${this.weekTime[0].replace("周",'').substr(5)} 周进展`
        }
      }
    },
		callDashboardProcess(weekTime, isJMArea) {
			this.loading = true
      dashboardProcess2({ 'weekTimeLists':weekTime, 'isJMArea': isJMArea})
				.then(res => {
					if (res.success) {
						this.totalData = JSON.parse(JSON.stringify(res.data))
						this.callFilter()
					} else {
						this.$message.error(res.message, 1)
					}
					this.loading = false
				})
				.catch(err => {
					this.loading = false
					this.$message.error("错误提示：" + err.message, 1)
				})
		},
    generateRowKey(record){
      // 或者使用随机生成的值作为 key
      return Math.random().toString(36).substr(2, 10);
    },
		// 点击周进展
		handleShowEvolve(value) {
			this.$success({
				title: `${value}`,
				okText: "关闭"
			})
		},
    //返回年份
    year(obj){
      return obj.weekDate.toString().slice(0,4);
    },
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : [];
    },
	},
	created() {
		// this.callDashboardProcess(this.weekTime)
    this.dimensionsOptions = this.getDict('weekProcess_dimensions');
		// 动态修改--height的值
		document.documentElement.style.setProperty(`--height`, `${this.scrollHeigh}px`)
	},
  computed:{
    columns(){
      return [
        {
          title: '序号',
          align: 'center',
          width: 80,
          customRender: (text, record, index) => index + 1
        },
        {
          title: '周数',
          dataIndex: 'weekTime',
          key: 'weekTime',
          width: 80,
          align: 'center',
          customRender: (text, record, index) => {
            // 找到当前周数在数据源中的所有索引
            const indexes = this.loadData.reduce((acc, item, idx) => {
              if (item.weekTime === text && this.year(item) === this.year(record)) {
                acc.push(idx);
              }
              return acc;
            }, []);
            const itemIdx = indexes.indexOf(index);

            // 查找相邻子数组
            let start = itemIdx;
            while (start > 0 && indexes[start] === indexes[start - 1] + 1) {
              start--;
            }

            let end = itemIdx;
            while (end < indexes.length - 1 && indexes[end] === indexes[end + 1] - 1) {
              end++;
            }
            let div =  indexes.slice(start, end + 1);

            // 如果当前记录是该周数的第一个记录，则返回周数并设置行合并的行数
            if (div.length > 0 && div[0] === index) {
              return {
                children: `CW${text}`,
                attrs: {
                  rowSpan: div.length,
                },
              };
            } else {
              return {
                children: null,
                attrs: {
                  colSpan: 0,
                },
              };
            }
          }
        },
        {
          title: "产品名称",
          align: "center",
          width: 100,
          dataIndex: "productProjectName",
          scopedSlots: {
            customRender: "productProjectName"
          },
          customRender: (text, record, index) => {
            // 检查前一个记录是否与当前记录相同，如果相同则不渲染姓名
            if (index > 0 && record.productProjectName === this.loadData[index - 1].productProjectName) {
              return {
                children: null,
                attrs: {
                  colSpan: 0,
                },
              };
            } else {
              // 计算当前姓名在数据源中的出现次数
              const count = this.loadData.filter(item => item.productProjectName === text).length;
              // 返回姓名并设置行合并的行数
              return {
                children: text,
                attrs: {
                  rowSpan: count,
                },
              };
            }
          },
        },
        {
          title: "项目名称",
          width: 100,
          align: "center",
          dataIndex: "projectName",
          scopedSlots: {
            customRender: "projectName"
          },
          customRender: (text, record, index) => {
            const wet = record.projectName;
            // 检查前一个记录是否与当前记录相同，如果相同则不渲染姓名
            if (index > 0 && record.projectName === this.loadData[index - 1].projectName) {
              return {
                children: null,
                attrs: {
                  colSpan: 0,
                },
              };
            } else {
              // 计算当前姓名在数据源中的出现次数
              const count = this.loadData.filter(item => item.projectName === wet).length;
              // 返回姓名并设置行合并的行数
              return {
                children: text,
                attrs: {
                  rowSpan: count,
                },
              };
            }
          },
        },
        {
          title: "客户",
          width: 100,
          align: "center",
          dataIndex: "customer",
          customRender: (text, record, index) => {
            const wet = record.projectName;
            // 检查前一个记录是否与当前记录相同，如果相同则不渲染姓名
            if (index > 0 && record.projectName === this.loadData[index - 1].projectName) {
              return {
                children: null,
                attrs: {
                  colSpan: 0,
                },
              };
            } else {
              // 计算当前姓名在数据源中的出现次数
              const count = this.loadData.filter(item => item.projectName === wet).length;
              // 返回姓名并设置行合并的行数
              return {
                children: text?text:'-',
                attrs: {
                  rowSpan: count,
                },
              };
            }
          },
        },
        {
          title: "项目阶段",
          width: 120,
          align: "center",
          dataIndex: "state",
          customRender: (text, record, index) => {
            const arr = this.getDict("product_state_status");
            let find = arr.find(e=>e.code == text);
            const op = find.name;
            const wet = record.projectName;
            // 检查前一个记录是否与当前记录相同，如果相同则不渲染姓名
            if (index > 0 && record.projectName === this.loadData[index - 1].projectName) {
              return {
                children: null,
                attrs: {
                  colSpan: 0,
                },
              };
            } else {
              // 计算当前姓名在数据源中的出现次数
              const count = this.loadData.filter(item => item.projectName === wet).length;
              // 返回姓名并设置行合并的行数
              return {
                children: op,
                attrs: {
                  rowSpan: count,
                },
              };
            }
          },
        },
        {
          title: this.title,
          align: "center",
          children: [
            {
              title: "进展状态",
              align: "center",
              dataIndex: "dimensionsState",
              width: 80,
              scopedSlots: {
                customRender: "dimensionsState"
              },
              customRender: (text, record, index) => {
                let classColor = '';
                switch (text) {
                  case 0:
                    classColor = 'green';
                    break
                  case 1:
                    classColor = 'red';
                    break
                  case 2:
                    classColor = 'yellow';
                    break
                  default:
                }
                const obj = {
                  children: (
                    <div class={`dimensionsState ${classColor}`}></div>
                  ),
                  attrs: {}
                }
                return obj
              },
            },
            {
              title: "项目维度",
              align: "center",
              dataIndex: "dimensions",
              width: 120,
              scopedSlots: {
                customRender: "dimensions"
              }
            },
            {
              title: "进展",
              align: "center",
              width: 200,
              dataIndex: "progressDesc",
              scopedSlots: {
                customRender: "progressDesc"
              }
            },
            {
              title: "下一步计划",
              align: "center",
              width: 200,
              dataIndex: "nextPlan",
              scopedSlots: {
                customRender: "nextPlan"
              }
            },
            {
              title: "责任人",
              align: "center",
              width: 120,
              dataIndex: "unionName",
              scopedSlots: { customRender: 'unionName' }
            }
          ]
        },
      ]
    }
  }
}
</script>

<style lang="less" scoped>
@import "./productoption.less";

/deep/.ant-table-thead > tr > th {
	border-top: 1px solid #dfdbdb;
}
/deep/.ant-table-bordered .ant-table-thead > tr > th{
  border-right: 1px solid #dfdbdb;
}
/deep/ .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th{
  padding: 5px;
}
.dimensionsState {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 3px;
  margin-left: 3px;
}

.dimensionsState.green {
  background: #91cc75;
}

.dimensionsState.yellow {
  background: #efeb73;
}

.dimensionsState.red {
  background: #f54747;
}

.dimensionsState.grey {
  background: #999797;
}

/deep/.ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

</style>
