package eve.sys.modular.bombill.bomcostoverview.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import eve.core.pojo.base.entity.BaseEntity;
import eve.sys.modular.bombill.bomcostoverview.params.PositiveType2PartNo;
import lombok.*;

/**
 * BOM成本总览表实体类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("BOM_COST_OVERVIEW")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class BomCostOverview extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 核算代码
     */
    private String accountingCode;

    /**
     * 核算日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountingDate;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 正极体系
     */
    //private String positiveElectrodeSystem;

    /**
     * 额定容量
     */
    private BigDecimal ratedCapacity;

    /**
     * 额定电压
     */
    private BigDecimal ratedVoltage;

    /**
     * 额定能量
     */
    private BigDecimal ratedEnergy;

    /**
     * 用途说明
     */
    private String usageDescription;

    /**
     * BOM文件编号
     */
    private String bomFileNumber;

    /**
     * BOM文件ID
     */
    private Long bomFileId;

    /**
     * BOM文件名称
     */
    private String bomFileName;

    /**
     * 需求人工号
     */
    private String requesterJobNumber;

    /**
     * 需求人
     */
    private String requesterName;

    /**
     * 研究所
     */
    private String dept;

    /**
     * 产品状态
     */
    private Integer productStatus;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 核算类型
     */
    private Integer accountingType;

    /**
     * 产品ID
     */
    private Long issueId;

    /**
     * BOM ID
     */
    private Long bomId;

    @TableField(exist = false)
    @JsonProperty("aNodeItems")
    private List<PositiveType2PartNo> aNodeItems;


    @TableField(exist = false)
    private List<String> depts;

    @TableField(exist = false)
    private List<String> positiveElectrodeSystems;

    @TableField(exist = false)
    private List<Integer> productStatuses;

    /**
     * 正极体系（从场景表获取化学体系编号，分号分隔）
     */
    private String positiveElectrodeSystem;


    private Integer seq;

    /**
     * 部门长工号
     */
    private String majordomo;

    private String majordomoName;

    private Long costIssueId;

    private String issueKey;
}
