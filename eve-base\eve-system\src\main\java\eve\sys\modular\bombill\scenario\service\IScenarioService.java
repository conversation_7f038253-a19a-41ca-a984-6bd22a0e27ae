package eve.sys.modular.bombill.scenario.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.scenario.entity.Scenario;

import java.util.List;

/**
 * 场景Service接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IScenarioService extends IService<Scenario> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<Scenario> pageList(Scenario param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return 列表结果
     */
    List<Scenario> list(Scenario param);

    /**
     * 新增
     *
     * @param param 新增参数
     * @return 是否成功
     */
    Boolean add(Scenario param);

    /**
     * 删除
     *
     * @param param 删除参数
     * @return 是否成功
     */
    Boolean delete(Scenario param);

    /**
     * 更新
     *
     * @param param 更新参数
     * @return 是否成功
     */
    Boolean update(Scenario param);

    /**
     * 根据ID查询
     *
     * @param param 查询参数
     * @return 查询结果
     */
    Scenario get(Scenario param);

    void copyInsertBatch(Scenario param);
    List<Long> getScenarioIdsByBomCostOverviewId(Scenario param);
    void insertBatch(List<BomAccountingDetail> bomAccountingDetails,List<MaterialPrice> materialPrices,BomCostOverview bomCostOverview);
    void syncScenarios(List<BomAccountingDetail> bomAccountingDetails,List<MaterialPrice> materialPrices,BomCostOverview bomCostOverview);
    void batchUpdateBaseUse(Scenario param);
    void batchUpdate(Scenario param);
}
