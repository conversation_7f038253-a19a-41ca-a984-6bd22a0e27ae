<template>
  <a-modal
    title="BOM成本对比"
    :visible="visible"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <tableIndex
        :pageLevel="3"
        :pageTitleShow="false"
        :paginationShow="false"
        :loading="loading"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
      >
        <!-- 搜索框 -->
        <template #search>
          <pbiSearchContainer>
            <pbiSearchItem label="基准组" :span="6">
              <a-select
                v-model="baselineScenario"
                placeholder="请选择基准场景"
                style="width: 100%;"
                size="small"
                @change="handleBaselineChange"
              >
                <a-select-option 
                  v-for="item in scenarioOptions" 
                  :key="item.value" 
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </pbiSearchItem>
            
            <pbiSearchItem type="btn" :span="18">
              <div class="main-btn">
                <a-button type="primary" size="small" @click="handleCalculate" :loading="calculating">
                  计算
                </a-button>
                <a-button
                  type="default"
                  size="small"
                  :loading="exporting"
                  @click="handleExport"
                  :disabled="!compareData.length"
                  style="margin-left: 8px;"
                >
                  导出对比结果
                </a-button>
                <span v-if="calculating" style="color: #1890ff; font-size: 12px; margin-left: 8px;">
                  <a-icon type="loading" /> 正在计算对比数据...
                </span>
              </div>
            </pbiSearchItem>
          </pbiSearchContainer>
        </template>
        
        <!-- 表格 -->
        <template #table>
          <!-- 两个表格并排显示 -->
          <div style="display: flex; gap: 16px; margin-top: 8px; height: 100%;">
            <!-- 左侧：BOM成本对比表格 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 8px; font-weight: 500; color: #1890ff;">BOM成本对比</div>
              <ag-grid-vue
                ref="agGrid"
                :style="`height: ${tableHeight - 160}px`"
                :tooltipShowDelay="0"
                class="table ag-theme-balham"
                :columnDefs="columnDefs"
                :rowData="compareData"
                :gridOptions="gridOptions"
                :defaultColDef="defaultColDef"
                :getRowId="getRowId"
              >
              </ag-grid-vue>
            </div>

            <!-- 右侧：主要材料价格对比输出情况 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 8px; font-weight: 500; color: #1890ff;">主要材料价格对比输出情况</div>
              <ag-grid-vue
                ref="materialGrid"
                :style="`height: ${tableHeight}px`"
                :tooltipShowDelay="0"
                class="table ag-theme-balham"
                :columnDefs="materialColumnDefs"
                :rowData="materialCompareData"
                :gridOptions="materialGridOptions"
                :defaultColDef="defaultColDef"
              >
              </ag-grid-vue>
            </div>
          </div>
        </template>
      </tableIndex>
    </div>
  </a-modal>
</template>

<script>

import { bomCostOverviewCompare, bomCostOverviewCalculateMaterialCosts, bomCostOverviewExportCompare } from '@/api/modular/system/bomCostOverviewManage'
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
export default {
  name: 'BomCostCompareModal',
  components: {
    chemicalPriceRenderer: {
      template: `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; width: 100%;">
          <div class="chemical-price-content">
            <div v-if="chemicalPrices && chemicalPrices.length > 0">
              <div v-for="(price, index) in chemicalPrices" :key="index" class="price-item">
                {{ price.elementName }}: {{ formatPrice(price.cnyPrice) }}
              </div>
            </div>
            <div v-else>
              暂无价格数据
            </div>
          </div>
        </div>
      `,
      data() {
        return {
          chemicalPrices: []
        }
      },
      mounted() {
        // 在组件挂载后获取数据
        this.chemicalPrices = this.params.chemicalPrices || []
        console.log('chemicalPriceRenderer mounted')
        console.log('params:', this.params)
        console.log('chemicalPrices:', this.chemicalPrices)
      },
      methods: {
        formatPrice(price) {
          return price ? price.toLocaleString() : 0
        }
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedScenarios: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      calculating: false,
      exporting: false,
      baselineScenario: null,
      isInitialized: false, // 添加初始化标志
      compareData: [],
      chemicalPrices: {},
      tableHeight: 480,

      // 材料对比表格相关数据
      materialColumnDefs: [],
      materialCompareData: [],
      materialGridOptions: {
        suppressRowClickSelection: true,
        suppressCellFocus: true,
        enableRangeSelection: false,
        suppressRowHoverHighlight: true,
        suppressRowTransform: true,
        onGridReady: (params) => {
          return params.api.sizeColumnsToFit();
        }
      },

      // 材料类型字典
      materialTypeDict: {
        /* 1: '石墨',
        2: '硅',
        3: '铝箔',
        4: '隔膜',
        5: '铜箔',
        7: '电解液',
        8: '其他材料' */
      },
      
      // AG Grid 配置
      gridOptions: {
        suppressRowClickSelection: true,
        suppressCellFocus: true,
        enableRangeSelection: false,
        suppressRowHoverHighlight: true,
        suppressRowTransform: true, // 启用rowSpan支持
        onGridReady: (params) => {
          return params.api.sizeColumnsToFit();
        }
      },
      
      defaultColDef: {
        flex: 1,
        minWidth: 120,
        filter: false,
        floatingFilter: false,
        editable: false,
        tooltipValueGetter: this.tooltipValueGetter
      },



      columnDefs: []
    }
  },
  computed: {
    scenarioOptions() {
      return this.selectedScenarios.map(item => ({
        value: `${item.bomCostOverviewId}_${item.id}`,
        label: `${item.productName}-场景${item.id}`
      }))
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.isInitialized = false // 重置初始化标志
        this.initData()
      } else {
        this.isInitialized = false // 关闭时重置标志
      }
    },
    selectedScenarios: {
      handler() {
        if (this.visible && !this.isInitialized) {
          this.initData()
        }
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载时如果模态框已经显示且有选中场景，则初始化数据
    if (this.visible && this.selectedScenarios.length > 0 && !this.isInitialized) {
      this.initData()
    }
  },
  methods: {
    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    // 初始化数据
    async initData() {
      if (this.selectedScenarios.length > 0 && !this.isInitialized) {
        this.isInitialized = true // 设置初始化标志

        const firstScenario = this.selectedScenarios[0]
        this.baselineScenario = `${firstScenario.bomCostOverviewId}_${firstScenario.id}`
        this.buildColumnDefs()
        this.buildMaterialColumnDefs()

        // 自动执行计算
        await this.autoCalculate()
      }
    },
    
    // 构建列定义
    buildColumnDefs() {
      const columns = [
        {
          headerName: '产品',
          field: 'compareContent',
          pinned: 'left',
          width: 150,
          cellRenderer: params => params.value || '-'
        }
      ]
      
      // 动态添加场景列，基准组排在第一列
      const sortedScenarios = this.getSortedScenarios()
      sortedScenarios.forEach((scenario) => {
        columns.push({
          headerName: scenario.productName + '-' + scenario.scenarioName,
          field: `scenario_${scenario.uid}`,
          width: 160,
          cellRenderer: params => {
            const value = params.value
            const rowType = params.data.type

            // 根据行类型显示不同内容
            if (rowType === 'productName') {
              return value || '-'
            } else if (rowType === 'percentage') {
              return value || '-'
            } else if (value !== null && value !== undefined && value !== '-') {
              // 数值类型，保留4位小数
              return typeof value === 'number' ? value.toFixed(3) : value
            }
            return '-'
          }
        })
      })
      
      // 添加化学元素价格列
      if(this.hasPerm('bomCostOverview:getMetalDispay')){
        columns.push({
          headerName: '金属前提',
          field: 'chemicalPrices',
          width: 180,
          cellClass: 'merged-chemical-price-cell',
          rowSpan: (params) => {
            // 第一行跨越所有行，其他行返回0被合并
            if (params.node.rowIndex === 0) {
              let totalRows = 0;
              params.api.forEachNode(node => {
                if (node.data) totalRows++;
              });
              return totalRows;
            } else {
              return 0;
            }
          },
          cellRenderer: 'chemicalPriceRenderer',
          cellRendererParams: {
            chemicalPrices: this.chemicalPrices
          },
          tooltipValueGetter: params => {
            const prices = this.chemicalPrices
            if (prices && Array.isArray(prices)) {
              const priceText = prices.map(item => {
                const formattedPrice = item.cnyPrice ? item.cnyPrice.toLocaleString() : 0
                return `${item.elementName}: ${formattedPrice}`
              }).join('\n')
              return priceText
            }
            return ''
          }
        })
      }
      
      this.columnDefs = columns
    },

    // 构建材料对比表格列定义
    buildMaterialColumnDefs() {
      const columns = [
        {
          headerName: '项目',
          field: 'materialType',
          width: 120,
          pinned: 'left',
          cellRenderer: params => {
            if (params.data.isTotal) {
              return '<strong>合计(CNY/Wh)</strong>'
            }
            return params.value || '-'
          }
        }
      ]

      // 动态添加场景列
      this.selectedScenarios.forEach((scenario) => {
        columns.push({
          headerName: `${scenario.productName}-场景${scenario.id}`,
          children: [
            {
              headerName: '未税单价(CNY)',
              field: `scenario_${scenario.uid}_cny`,
              width: 120,
              cellRenderer: params => {
                // 如果是电芯能量行，跨列显示
                if (params.data && params.data.materialTypeId === 'energy') {
                  // 只在第一个子列显示数据，第二个子列隐藏
                  const value = params.value
                  if (value !== null && value !== undefined && value !== '') {
                    return `${typeof value === 'number' ? value.toFixed(2) : value} Wh`
                  }
                  return '-'
                }

                const value = params.value
                if (value !== null && value !== undefined && value !== '-') {
                  return typeof value === 'number' ? value.toFixed(4) : value
                }
                return '-'
              },
              colSpan: params => {
                // 如果是电芯能量行，跨两列
                return params.data && params.data.materialTypeId === 'energy' ? 2 : 1
              }
            },
            {
              headerName: '成本(CNY/Wh)',
              field: `scenario_${scenario.uid}_cost`,
              width: 120,
              cellRenderer: params => {
                // 如果是电芯能量行，不显示内容（因为被第一列跨列了）
                if (params.data && params.data.materialTypeId === 'energy') {
                  return ''
                }

                const value = params.value
                if (value !== null && value !== undefined && value !== '-') {
                  return typeof value === 'number' ? value.toFixed(4) : value
                }
                return '-'
              }
            }
          ]
        })
      })

      this.materialColumnDefs = columns
    },
    
    // 基准场景变更
    handleBaselineChange(value) {
      this.baselineScenario = value
    },

    // 自动计算（页面初始化时调用）
    async autoCalculate() {
      if (!this.baselineScenario) {
        console.warn('基准场景未设置，跳过自动计算')
        return
      }

      this.calculating = true
      try {
        // 构建请求参数
        const scenarios = this.selectedScenarios.map(s => ({
          scenarioId: s.id,
          bomCostOverviewId: s.bomCostOverviewId
        }))

        // 解析基准场景的bomCostOverviewId和scenarioId
        const [baselineBomId, baselineScenarioId] = this.baselineScenario.split('_')
        const baselineScenario = {
          scenarioId: parseInt(baselineScenarioId),
          bomCostOverviewId: parseInt(baselineBomId)
        }

        const requestData = {
          scenarios: scenarios,
          baselineScenario: baselineScenario
        }

        console.log('自动计算发送对比请求:', requestData)

        const res = await bomCostOverviewCompare(requestData)

        if (res && res.success) {
          await this.processCompareData(res.data)
          console.log('自动计算完成')
        } else {
          console.error('自动计算失败：', res ? res.message : '未知错误')
        }
      } catch (error) {
        console.error('自动计算异常：', error)
      } finally {
        this.calculating = false
      }
    },

    // 计算对比数据
    async handleCalculate() {

      if (!this.baselineScenario) {
        this.$message.warning('请先选择基准场景')
        return
      }

      if (!this.selectedScenarios || this.selectedScenarios.length === 0) {
        this.$message.warning('没有选中的场景数据')
        return
      }

      this.calculating = true
      try {
        // 重新构建列定义，确保基准场景排在第一列
        this.buildColumnDefs()
        this.buildMaterialColumnDefs()

        // 构建新的请求参数格式
        const scenarios = this.selectedScenarios.map(s => ({
          scenarioId: s.id,
          bomCostOverviewId: s.bomCostOverviewId
        }))

        // 解析基准场景的bomCostOverviewId和scenarioId
        const [baselineBomId, baselineScenarioId] = this.baselineScenario.split('_')
        const baselineScenario = {
          scenarioId: parseInt(baselineScenarioId),
          bomCostOverviewId: parseInt(baselineBomId)
        }

        const requestData = {
          scenarios: scenarios,
          baselineScenario: baselineScenario
        }

        const res = await bomCostOverviewCompare(requestData)

        if (res && res.success) {
          await this.processCompareData(res.data)
          this.$message.success('对比计算完成')
        } else {
          this.$message.error('计算失败：' + (res ? res.message : '未知错误'))
        }
      } finally {
        this.calculating = false
      }
    },

    // 合并所有场景的化学元素价格
    mergeChemicalPricesFromData(data) {
      const allPrices = []

      // 遍历所有场景数据，收集化学元素价格（不过滤重复）
      //data.forEach(item => {
        if (data[0].chemicalPrices && Array.isArray(data[0].chemicalPrices)) {
          data[0].chemicalPrices.forEach(price => {
            allPrices.push({
              elementName: price.elementName,
              cnyPrice: price.cnyPrice,
              unit: price.unit || '元/吨'
            })
          })
        }
      //})

      this.chemicalPrices = allPrices
      console.log('合并后的化学元素价格:', this.chemicalPrices)
    },

    // 构建材料对比数据
    async buildMaterialCompareData(data) {
      try {
        // 调用后端接口获取材料成本数据
        const requestData = {
          scenarios: this.selectedScenarios.map(s => ({
            scenarioId: s.id,
            bomCostOverviewId: s.bomCostOverviewId
          })),
          baselineScenario: {
            scenarioId: this.selectedScenarios[0].id,
            bomCostOverviewId: this.selectedScenarios[0].bomCostOverviewId
          }
        }

        console.log('发送材料成本计算请求:', requestData)
        const res = await bomCostOverviewCalculateMaterialCosts(requestData)

        if (res && res.success && res.data) {
          this.processMaterialCostData(res.data)
        } else {
          console.error('获取材料成本数据失败:', res)
          this.buildEmptyMaterialData()
        }
      } catch (error) {
        console.error('调用材料成本接口失败:', error)
        this.buildEmptyMaterialData()
      }
    },

    // 处理材料成本数据
    processMaterialCostData(materialCostResults) {
      const materialData = []

      // 首先添加电芯能量行
      const energyRow = {
        materialType: '电芯能量(Wh)',
        materialTypeId: 'energy',
        isTotal: false
      }

      // 为每个场景添加电芯能量数据
      this.selectedScenarios.forEach(scenario => {
        const scenarioResult = materialCostResults.find(result =>
          result.scenarioId === scenario.id && result.bomCostOverviewId === scenario.bomCostOverviewId
        )

        if (scenarioResult) {
          energyRow[`scenario_${scenario.uid}_cny`] = scenarioResult.ratedEnergy || 0
          energyRow[`scenario_${scenario.uid}_cost`] = ''
        } else {
          energyRow[`scenario_${scenario.uid}_cny`] = 0
          energyRow[`scenario_${scenario.uid}_cost`] = ''
        }
      })

      materialData.push(energyRow)

      // 添加各种材料类型行
      Object.keys(this.materialTypeDict).forEach(typeId => {
        const materialName = this.materialTypeDict[typeId]
        const rowData = {
          materialType: materialName,
          materialTypeId: parseInt(typeId),
          isTotal: false
        }

        // 为每个场景添加数据
        this.selectedScenarios.forEach(scenario => {
          // 查找对应场景的材料成本数据
          const scenarioResult = materialCostResults.find(result =>
            result.scenarioId === scenario.id && result.bomCostOverviewId === scenario.bomCostOverviewId
          )

          if (scenarioResult && scenarioResult.materialCosts && scenarioResult.materialCosts[typeId]) {
            const materialCost = scenarioResult.materialCosts[typeId]
            rowData[`scenario_${scenario.uid}_cny`] = parseInt(typeId) == 8 ? '-' : (materialCost.untaxedUnitPrice || 0)
            rowData[`scenario_${scenario.uid}_cost`] = materialCost.costPerWh || 0
          } else {
            rowData[`scenario_${scenario.uid}_cny`] = parseInt(typeId) == 8 ? '-' : 0
            rowData[`scenario_${scenario.uid}_cost`] = 0
          }
        })

        materialData.push(rowData)
      })

      // 添加合计行
      const totalRow = {
        materialType: '合计',
        isTotal: true
      }

      this.selectedScenarios.forEach(scenario => {
        //let totalCny = 0
        let totalCost = 0

        // 计算该场景所有材料的合计
        const scenarioResult = materialCostResults.find(result =>
          result.scenarioId === scenario.id && result.bomCostOverviewId === scenario.bomCostOverviewId
        )

        if (scenarioResult) {
          Object.keys(this.materialTypeDict).forEach(typeId => {
            if (scenarioResult.materialCosts && scenarioResult.materialCosts[typeId]) {
              const materialCost = scenarioResult.materialCosts[typeId]
              //totalCny += materialCost.untaxedUnitPrice || 0
              totalCost += materialCost.costPerWh || 0
            }
          })
        }

        totalRow[`scenario_${scenario.uid}_cny`] = '-'
        totalRow[`scenario_${scenario.uid}_cost`] = totalCost
      })

      materialData.push(totalRow)

      this.materialCompareData = materialData
      console.log('处理后的材料对比数据:', this.materialCompareData)
    },

    // 构建空的材料数据（当接口调用失败时使用）
    buildEmptyMaterialData() {
      const materialData = []

      // 首先添加电芯能量行
      const energyRow = {
        materialType: '电芯能量(Wh)',
        materialTypeId: 'energy',
        isTotal: false
      }

      // 为每个场景添加空的电芯能量数据
      this.selectedScenarios.forEach(scenario => {
        energyRow[`scenario_${scenario.uid}_cny`] = 0
        energyRow[`scenario_${scenario.uid}_cost`] = ''
      })

      materialData.push(energyRow)

      // 添加各种材料类型行
      Object.keys(this.materialTypeDict).forEach(typeId => {
        const materialName = this.materialTypeDict[typeId]
        const rowData = {
          materialType: materialName,
          materialTypeId: parseInt(typeId),
          isTotal: false
        }

        // 为每个场景添加数据
        this.selectedScenarios.forEach(scenario => {
          rowData[`scenario_${scenario.uid}_cny`] = parseInt(typeId) == 8 ? '-' : 0
          rowData[`scenario_${scenario.uid}_cost`] = 0
        })

        materialData.push(rowData)
      })

      // 添加合计行
      const totalRow = {
        materialType: '合计',
        isTotal: true
      }

      this.selectedScenarios.forEach(scenario => {
        totalRow[`scenario_${scenario.uid}_cny`] = '-'
        totalRow[`scenario_${scenario.uid}_cost`] = 0
      })

      materialData.push(totalRow)

      this.materialCompareData = materialData
      console.log('空材料对比数据:', this.materialCompareData)
    },

    // 处理对比数据
    async processCompareData(data) {
      const compareData = []

      // 合并所有场景的化学元素价格
      this.mergeChemicalPricesFromData(data)

      // 重新构建列定义，确保化学元素价格数据已准备好
      this.buildColumnDefs()
      this.buildMaterialColumnDefs()

      // 构建材料对比数据
      await this.buildMaterialCompareData(data)

      // 构建能量行数据
      const energyRowData = {
        compareContent: '能量(Wh)',
        type: 'energy',
        chemicalPrices: this.chemicalPrices
      }

      // 添加各场景的能量数据
      this.selectedScenarios.forEach(scenario => {
        // 从后端返回的数据中获取能量信息
        const scenarioData = data.find(d => d.bomCostOverviewId+''+d.scenarioId === scenario.uid)
        energyRowData[`scenario_${scenario.uid}`] = scenarioData ? scenarioData.ratedEnergy : '-'
      })
      compareData.push(energyRowData)

      // 构建汇总数据行
      const summaryData = this.calculateSummaryData(data)

      
      if (this.hasPerm('bomCostOverview:compare')) {
        // 添加电芯成本行
        const cellCostRow = {
          compareContent: '材料成本(元/Wh)',
          type: 'cellCost',
          chemicalPrices: this.chemicalPrices
        }
        this.selectedScenarios.forEach(scenario => {
          const scenarioData = summaryData[scenario.uid]
          cellCostRow[`scenario_${scenario.uid}`] = scenarioData ? Number(scenarioData.cellCost).toFixed(4) : '-'
        })
        compareData.push(cellCostRow)

        // 添加结构件成本行
        const structureCostRow = {
          compareContent: '结构件成本(元/Wh)',
          type: 'structureCost',
          chemicalPrices: this.chemicalPrices
        }
        this.selectedScenarios.forEach(scenario => {
          const scenarioData = summaryData[scenario.uid]
          structureCostRow[`scenario_${scenario.uid}`] = scenarioData ? Number(scenarioData.structureCost).toFixed(4) : '-'
        })
        compareData.push(structureCostRow)

        // 添加BOM总成本行
        const totalCostRow = {
          compareContent: 'BOM总成本(元/Wh)',
          type: 'totalCost',
          chemicalPrices: this.chemicalPrices
        }
        this.selectedScenarios.forEach(scenario => {
          const scenarioData = summaryData[scenario.uid]
          totalCostRow[`scenario_${scenario.uid}`] = scenarioData ? Number(scenarioData.totalCost).toFixed(4) : '-'
        })
        compareData.push(totalCostRow)

      }
      

      // 添加BOM成本百分比行
      const percentageRow = {
        compareContent: 'BOM成本百分比(%)',
        type: 'percentage',
        chemicalPrices: this.chemicalPrices
      }

      // 解析基准场景ID
      const [baselineBomId, baselineScenarioId] = this.baselineScenario.split('_')
      const baselineData = summaryData[baselineBomId+''+baselineScenarioId]

      this.selectedScenarios.forEach(scenario => {
        const scenarioData = summaryData[scenario.uid]
        if (scenarioData && baselineData) {
          const percentage = this.calculatePercentage(scenarioData.totalCost, baselineData.totalCost)
          percentageRow[`scenario_${scenario.uid}`] = `${percentage}%`
        } else {
          percentageRow[`scenario_${scenario.uid}`] = '-'
        }
      })
      compareData.push(percentageRow)

      this.compareData = compareData
    },

    // 计算汇总数据
    calculateSummaryData(data) {
      const summaryData = {}

      data.forEach(item => {
        if (!summaryData[item.bomCostOverviewId+ '' +item.scenarioId]) {
          summaryData[item.bomCostOverviewId+ '' +item.scenarioId] = {
            cellCost: item.cellCost || 0,
            structureCost: item.structureCost || 0,
            totalCost: item.totalCost || 0
          }
        }
      })

      return summaryData
    },
    
    // 计算百分比
    calculatePercentage(currentCost, baselineCost) {
      if (!baselineCost || baselineCost === 0) return 0
      return ((currentCost / baselineCost) * 100).toFixed(3)
    },
    

    
    // AG Grid 相关方法
    getRowId(params) {
      return params.data.productName + (params.data.type || '')
    },
    
    tooltipValueGetter(params) {
      return params.value
    },
    
    // 鼠标进入表格
    tableFocus() {
      // 滚动条样式处理
    },
    
    // 鼠标移出表格
    tableBlur() {
      // 滚动条样式处理
    },
    
    // 获取排序后的场景列表（基准组排在第一列）
    getSortedScenarios() {
      if (!this.baselineScenario || !this.selectedScenarios.length) {
        return this.selectedScenarios
      }

      // 解析基准场景ID（格式：bomCostOverviewId_scenarioId）
      const [baselineBomId, baselineScenarioId] = this.baselineScenario.split('_')
      const baselineUid = baselineBomId + baselineScenarioId // 匹配uid格式（无下划线）

      // 找到基准场景
      const baselineScenario = this.selectedScenarios.find(scenario => scenario.uid === baselineUid)

      if (!baselineScenario) {
        console.warn('未找到基准场景:', this.baselineScenario, '可用场景:', this.selectedScenarios.map(s => s.uid))
        return this.selectedScenarios
      }

      // 其他场景
      const otherScenarios = this.selectedScenarios.filter(scenario => scenario.uid !== baselineUid)

      // 基准场景排在第一位
      return [baselineScenario, ...otherScenarios]
    },

    // 导出对比结果
    async handleExport() {
      if (!this.compareData.length) {
        this.$message.warning('没有对比数据可以导出')
        return
      }

      this.exporting = true
      try {
        // 准备导出数据
        const exportData = {
          compareData: this.compareData,
          materialCompareData: this.materialCompareData,
          selectedScenarios: this.getSortedScenarios(),
          baselineScenario: this.baselineScenario,
          columnDefs: this.columnDefs,
          materialColumnDefs: this.materialColumnDefs
        }

        console.log('导出数据详情:', {
          compareDataSize: this.compareData.length,
          materialCompareDataSize: this.materialCompareData.length,
          selectedScenariosSize: this.getSortedScenarios().length,
          baselineScenario: this.baselineScenario,
          exportData: exportData
        })

        // 调用导出API
        const response = await bomCostOverviewExportCompare(exportData)

        console.log('导出API响应:', response)

        if (response && response.data) {
          console.log('响应数据类型:', typeof response.data)
          console.log('响应数据大小:', response.data.size || response.data.length)

          // 检查是否是错误响应（JSON格式）
          if (response.data.type === 'application/json') {
            const text = await response.data.text()
            console.error('服务器返回错误:', text)
            this.$message.error('导出失败：' + text)
            return
          }

          // 创建下载链接
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })

          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url

          // 生成文件名
          const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
          link.download = `BOM成本对比结果_${timestamp}.xlsx`

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('导出成功')
        } else {
          console.error('导出响应为空或无效')
          this.$message.error('导出失败：响应数据为空')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败：' + (error.message || '未知错误'))
      } finally {
        this.exporting = false
      }
    },

    // 关闭弹窗
    handleCancel() {
      this.$emit('update:visible', false)
    }
  },
  created(){
    const items = this.getDict('bom_bill_material_type')
    items.sort((a,b)=>a.sort-b.sort)
    this.materialTypeDict = {}
    items.forEach(item => {
      this.materialTypeDict[parseInt(item.code)] = item.name
    })
  }
}
</script>

<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
        --scroll-display: none;
        --scroll-border-bottom: none;
        --scroll-border-bottom-fixed: none;
    }
    /deep/.searchItem .label{
        width: initial;
    }
    /deep/.ag-body-horizontal-scroll{
        border-bottom: var(--scroll-border-bottom) !important;
    }
    /deep/.ag-body-horizontal-scroll-viewport {
        display: var(--scroll-display) !important;
        border-bottom: var(--scroll-border-bottom) !important;
    }

    /deep/.ag-horizontal-left-spacer,
    /deep/.ag-horizontal-right-spacer{
        border-bottom: var(--scroll-border-bottom-fixed) !important;
    }

    /deep/.search-container .vue-treeselect__multi-value-label{
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    /deep/.search-container .vue-treeselect__limit-tip-text{
        font-weight: initial;
        text-indent: -32px;
        overflow: hidden;
        margin: 0;
    }

    /* 复选框列样式 */
    /deep/ .ag-selection-checkbox {
        margin: 0 auto;
    }

    /deep/ .ag-header-select-all {
        margin: 0 auto;
    }

    /* 悬浮提示样式 */
    /deep/ .ag-tooltip {
        background-color: #ffffcc !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        padding: 8px !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        white-space: pre-line !important;
        max-width: 300px !important;
        word-wrap: break-word !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }

    /* 合并化学元素价格列的样式 */
    /deep/ .merged-chemical-price-cell {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-bottom: 1px solid #eee !important;
        background-color: #fff !important;
        border-right: 0;
    }

    /deep/ .merged-chemical-price-cell .ag-cell-wrapper {
        height: 100% !important;
        width: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }



    /deep/ .chemical-price-content {
        padding: 8px !important;
        line-height: 1.6 !important;
        font-size: 12px !important;
        color: #333 !important;
        text-align: center !important;
    }

    /deep/ .price-item {
        margin: 2px 0 !important;
        white-space: nowrap !important;
    }




.main-btn {
  display: flex;
  gap: 8px;
}

/* 鼠标悬停效果 */
/deep/.ag-row:hover .ag-cell {
  background-color: #f0f9ff !important;
}

</style>
